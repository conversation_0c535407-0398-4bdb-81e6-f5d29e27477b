<script setup lang="ts">
  import { computed, ref, onMounted, onUnmounted } from 'vue'
  import type { RecentContact } from '@/utils/callContact'
  import eventBus from '@/utils/eventBus'
  import dayjs from 'dayjs'

  const props = withDefaults(defineProps<RecentContact>(), {
    isCommonContact: false,
  })

  export type DispatchRecentContactCardEmit = {
    removeFromCommon: [targetDmrIDHex: string]
    addToCommon: [targetDmrIDHex: string]
    call: [targetDmrIDHex: string, callback?: (success: boolean) => void]
    hangup: [targetDmrIDHex: string, callback?: (success: boolean) => void]
  }

  const emit = defineEmits<DispatchRecentContactCardEmit>()

  const isCalling = ref<boolean>(false)

  const soundTime = computed(() => {
    return dayjs(props.soundTime).format('HH:mm:ss')
  })

  // 监听 speak-end 事件
  const handleSpeakEnd = (target: string) => {
    if (target === props.targetDmrIDHex) {
      isCalling.value = false
    }
  }

  // 组件挂载时添加事件监听器
  onMounted(() => {
    eventBus.on('speak-end', handleSpeakEnd)
  })

  // 组件卸载时移除事件监听器
  onUnmounted(() => {
    eventBus.off('speak-end', handleSpeakEnd)
  })

  // 处理呼叫/挂断按钮点击
  const handleCallClick = () => {
    if (isCalling.value) {
      emit('hangup', props.targetDmrIDHex, (success: boolean) => {
        if (success) {
          isCalling.value = false
        }
      })
    } else {
      emit('call', props.targetDmrIDHex, (success: boolean) => {
        if (success) {
          isCalling.value = true
        }
      })
    }
  }

  const srcIconClass = computed(() => {
    switch (props.srcType) {
      case 'sdcTerminal':
      case 'networkTerminal':
        return 'bfdx-duijiangjinei'
      default:
        return 'bfdx-duijiangjinei'
    }
  })

  const targetIconClass = computed(() => {
    switch (props.targetType) {
      case 'sdcTerminal':
      case 'networkTerminal':
        return 'bfdx-danhu'
      case 'group':
      case 'taskGroup':
        return 'bfdx-duijiangjizu'
      case 'fullCallContact':
        return 'bfdx-quanhu'
      default:
        return 'bfdx-danhu'
    }
  })
</script>

<template>
  <div class="card-container relative w-[274px] h-[90px] p-[10px] flex flex-col justify-center items-center">
    <div class="card-header h-[calc(50%+1px)] w-full !border-b !border-[#374A60] !p-0">
      <div class="src-contact pt-[5px] flex justify-center items-center gap-[5px]">
        <dispatch-recent-contact-card-icon :icon-class="srcIconClass" color="radial-gradient(#03dfff, #1677ff)" />
        <ellipsis-text class="w-[88px] text-center" not-wfull :content="props.srcName" />
        <dispatch-recent-contact-card-icon
          icon-class="bfdx-duijiangjitonghua"
          color="linear-gradient(to bottom right, #6895fe, #6edbff)"
          class="text-[rgba(0,0,0,0.2)]! mx-[5px]"
        />
        <dispatch-recent-contact-card-icon :icon-class="targetIconClass" color="linear-gradient(to bottom right, #6895fe, #6edbff)" class="mr-[5px]" />
        <ellipsis-text class="w-[39px] text-center" not-wfull :content="props.targetName" />
        <el-tooltip
          popper-class="bf-tooltip"
          placement="top"
          effect="dark"
          :show-after="500"
          :content="$t(`dispatch.contactCard.${isCommonContact ? 'removeFromCommon' : 'addToCommon'}`)"
        >
          <!-- todo:根据isCommonContact调整css样式 -->
          <dispatch-recent-contact-card-icon
            icon-class="bfdx-duijiangjiqiehuan"
            class="ml-[5px] cursor-pointer active:opacity-50"
            @click="isCommonContact ? $emit('removeFromCommon', props.targetDmrIDHex) : emit('addToCommon', props.targetDmrIDHex)"
          />
        </el-tooltip>
      </div>
    </div>
    <div class="card-content h-[calc(50%-1px)] w-full pl-[38px] pr-[50px] flex justify-between items-center">
      <div class="flex gap-[11px]">
        <ellipsis-text class="text-center" not-wfull :content="soundTime" />
        <p class="text-[#FFAA19] !m-0">{{ props.soundLen + 's' }}</p>
      </div>
      <dispatch-recent-contact-card-icon
        :icon-class="isCalling ? 'bfdx-guaduan' : 'bfdx-duijiangjimaikefeng'"
        :color="isCalling ? 'linear-gradient(to bottom right, #ff4242, #ff8181)' : 'linear-gradient(to bottom right, #42a4ff, #81c2ff)'"
        class="cursor-pointer active:opacity-50"
        @click="handleCallClick"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
  .card-container {
    background: url('@/assets/images/dispatch/contact_card/recent_contact_bg.svg') no-repeat center center;
    background-size: contain;
    font-size: 12px;
    font-weight: normal;
    color: #fff;
  }
</style>
