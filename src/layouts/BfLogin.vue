<template>
  <div class="login-page relative w-full h-screen flex flex-col items-center justify-center">
    <!-- 背景图片 -->
    <img ref="login_bg" src="@/assets/images/loginBg@small/login_bg_small.webp" class="w-full h-full absolute" />

    <BFHead class="absolute" :show-head-menu="false" :show-head-btn-group="false" />
    <!-- Logo -->
    <!--    <div class="login-logo absolute top-4 left-16 ml-[10px]">-->
    <!--      <picture>-->
    <!--        <source-->
    <!--          media="(min-width: 2561px)"-->
    <!--          srcset="@/assets/images/loginBg/2x/logo.webp"-->
    <!--        >-->
    <!--        <img-->
    <!--          src="@/assets/images/loginBg/1x/logo.webp"-->
    <!--          class="w-full h-full object-contain"-->
    <!--        >-->
    <!--      </picture>-->
    <!--    </div>-->

    <!-- 标题 -->
    <!--    <div class="login-title absolute top-0 left-1/2 transform -translate-x-1/2 text-white text-center flex flex-col">-->
    <!--      <EllipsisText-->
    <!--        class="title-main"-->
    <!--        :content="headTitle"-->
    <!--      />-->
    <!--      <EllipsisText-->
    <!--        class="title-sub"-->
    <!--        :content="'Smart Digital Connection System Platform'"-->
    <!--      />-->
    <!--    </div>-->

    <!-- 内容 -->
    <div class="content absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 flex items-center justify-center">
      <img ref="hud" src="@/assets/images/loginBg@small/hud_small.webp" class="w-full h-full absolute" />
      <div class="login-box absolute flex flex-col justify-between" :class="isLogin ? 'isLogin' : 'unLogin'">
        <img
          v-if="!isLogin"
          ref="login_box_bg"
          src="@/assets/images/loginBg@small/login_box_bg_small.webp"
          class="absolute w-full h-full top-[0] left-[0] -z-1"
        />
        <div class="login-box-header relative flex items-center justify-between px-8 w-full text-white">
          <!--返回登录-->
          <div v-show="isLogin" class="login-box-back self-end cursor-pointer flex justify-end" @click="backLogin">
            <EllipsisText class="text-end !w-[66px] leading-[35px] !mr-[12px]" :content="$t('loginDlg.backLogin')" />
          </div>
          <!--登录窗口文本-->
          <div class="login-box-title w-[200px] text-center leading-[65px] self-start absolute left-1/2 transform -translate-x-1/2">
            <EllipsisText class="text-center leading-[50px]" :content="isLogin ? $t('loginDlg.platform') : $t('loginDlg.loginWind')" />
          </div>
          <!--语言切换-->
          <switchLangs class="!absolute right-8 top-[20px] !w-[30px] !h-[30px]" />
        </div>
        <!-- 登录表单 -->
        <div class="login-box-form flex justify-end items-end w-full">
          <el-form v-if="!isLogin" class="mr-[36px]">
            <!--            <el-form-item>-->
            <!--              <el-input-->
            <!--                v-model="system"-->
            <!--                :placeholder="$t('loginDlg.system')"-->
            <!--                @keydown.enter="inputEnterLogin"-->
            <!--                @change="resetSession"-->
            <!--              >-->
            <!--                <template #prefix>-->
            <!--                  <i class="iconfont icon-number" />-->
            <!--                </template>-->
            <!--              </el-input>-->
            <!--            </el-form-item>-->
            <el-form-item class="username-item">
              <el-input
                id="username"
                v-model="username"
                :placeholder="$t('loginDlg.name')"
                :autofocus="true"
                @keydown.enter="inputEnterLogin"
                @change="resetSession"
              >
                <template #prefix>
                  <img src="@/assets/images/loginBg/username.svg" />
                </template>
              </el-input>
            </el-form-item>
            <el-form-item class="password-item">
              <el-input
                id="user_pwd"
                ref="password"
                v-model="password"
                :placeholder="$t('loginDlg.loginPassword')"
                type="password"
                @keydown.enter="inputEnterLogin"
                @change="resetSession"
              >
                <template #prefix>
                  <img src="@/assets/images/loginBg/password.svg" />
                </template>
              </el-input>
            </el-form-item>
            <el-form-item class="login-button-item">
              <el-button class="login_type_btn" type="primary" :disabled="disSignIn" @click="loginNow">
                {{ $t('loginDlg.login') }}
              </el-button>
            </el-form-item>
            <el-form-item class="login-status">
              <!-- <el-checkbox v-model="remember">
                        <span v-text="$t('loginDlg.remember')" />
                      </el-checkbox> -->
              <div class="flex max-w-full items-center mb-[40px]">
                <img :src="serverStatusImg" class="!w-5 !h-5 mr-2" />
                <EllipsisText class="text-white text-right" :content="statusTip" />
              </div>
            </el-form-item>
          </el-form>

          <div v-else class="login-btn-group h-full flex flex-col justify-around items-center mr-[36px]">
            <el-button class="login_type_btn w-full mb-[60px]" type="primary" @click="enterAdmin">
              <img src="@/assets/images/loginBg/manage.svg" class="w-full h-full object-contain" />
              <EllipsisText class="text-center" :content="$t('loginDlg.adminPlatform')" />
            </el-button>

            <el-button class="login_type_btn !ml-0 w-full mb-[80px]" type="primary" @click="enterDispatch">
              <img src="@/assets/images/loginBg/dispatch.svg" class="w-full h-full object-contain" />
              <EllipsisText class="text-center" :content="$t('loginDlg.dispatchPlatform')" />
            </el-button>

            <div class="flex items-center mb-[40px]">
              <img :src="serverStatusImg" class="!w-5 !h-5 mr-2" />
              <p class="text-white text-right">
                {{ statusTip }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import bfStorage from '@/utils/storage'
  import '@/css/iconfont/iconfont.css'
  import login, { syncServerTime } from '@/utils/login'
  import { loadLanguageAsync, fixLang } from '@/modules/i18n'
  import { saveLicense } from '@/utils/bfAuth'
  import packageJson from '~/package.json'
  import { compare } from 'compare-versions'
  import EllipsisText from '@/components/common/EllipsisText.vue'
  import serverStatus from '@/assets/images/loginBg/server_status.svg'
  import serverStatusReady from '@/assets/images/loginBg/server_status_ready.svg'
  import switchLangs from '@/components/common/switchLangs.vue'
  import BFHead from '@/layouts/BFHead.vue'
  import { DataManageRouteName, DispatchRouteName } from '@/router'

  const minServerVersion = packageJson.minServerVersion
  bfglob.console.log('minServerVersion', minServerVersion)

  export default {
    data() {
      return {
        remember: true,
        system: '',
        username: '',
        password: '',
        connectStats: 1000,
        disSignIn: true,
        isLogin: false,
        headTitle: '',
      }
    },
    components: {
      EllipsisText,
      switchLangs,
      BFHead,
    },
    methods: {
      // 记住用户账号名,登录时执行
      remembered() {
        if (this.remember) {
          const locale = fixLang(this.$i18n.locale).value
          if (locale !== this.$i18n.locale) {
            loadLanguageAsync(locale)
          }
          let account = {
            username: this.username,
            system: this.system,
            password: ''.padStart(this.password.length, '*'),
            sessionId: bfglob.sessionId,
            locale,
          }
          account = JSON.stringify(account)
          bfStorage.setItem('bfdx_account', account)
        } else {
          bfStorage.removeItem('bfdx_account')
        }
      },
      resetSession() {
        bfglob.sessionId = ''
      },
      passwordFocus() {
        this.resetSession()
        this.$refs.password.focus()
      },
      /**
       * 检查版本，如果小于指定的版本，则提示用户升级
       * @param version {string} 服务器当前版本
       */
      checkServerVersionIsNeedUpgrade(version) {
        // 如果没有版本号，则不处理
        if (!version) return
        if (compare(version, minServerVersion, '<')) {
          ElMessage({
            message: this.$t('loginDlg.upgradeServerTip', {
              latestServerVersion: minServerVersion,
              currentServerVersion: version,
            }),
            type: 'warning',
            duration: 0,
            showClose: true,
            offset: 60,
          })
        }
      },
      loginResponse(response) {
        bfglob.console.log('loginResponse', response)
        this.connectStats = response.responseCode
        // 100:成功 0:不成功(未知原因)
        // 密码登录错误: 1:无此用户 2:密码检验不通过 1101: 查询db_user数据库错误 1102: 用户既不是管理员也不是调度员
        // session登录错误: 3:无此sid 1201: 查询db_user_session_id数据库错误 1300:无法通过session查询到db_user,无此用户
        // session登录错误: 1301: 无法通过session查询到db_user,数据库错误 1302: 用户既不是管理员也不是调度员
        switch (response.responseCode) {
          case 1101:
            break
          case 1102:
            break
          case 1201:
            break
          case 1300:
            break
          case 1301:
            break
          case 1302:
            break
          case 0:
            break
          case 1:
            break
          case 2:
            this.passwordFocus()
            break
          case 3:
            this.password = ''
            this.passwordFocus()
            break
          case 100:
            this.checkServerVersionIsNeedUpgrade(response.serverVersion)

            // 设置全局属性设置
            login.saveLoginResponse(response)

            // 缓存账号信息
            this.remembered()
            // 释放变量内存
            window.bfaccount = null

            // nats没有向外提供客户端IP信息，发一次post请求到服务器上，以便服务记录客户端IP
            login.fetchClientIp(response.sid)

            // 保存授权信息
            // 兼容旧版本，如果没有mod-svt授权则补齐
            if (!response.lic.lic?.licenses['mod-svt']) {
              response.lic.lic.licenses['mod-svt'] = 0
            }
            saveLicense(response.lic)

            const user = response.dbUser
            if (user.isAdmin && user.isDispatcher) {
              this.isLogin = true
              this.connectStats = 1001
            } else {
              this.$router.replace('/main')
            }

            break
          default:
            bfglob.console.error('unknown login response:', response)
        }
      },
      loginCatch(err) {
        bfglob.console.error('loginCatch', err)

        this.connectStats = 1004
      },
      getSysIdFromHash() {
        const hash = window.location.hash // 例如：#/login?sysId=01
        const queryString = hash.split('?')[1]
        const params = new URLSearchParams(queryString)
        return params.get('sysId')
      },
      loginNow() {
        if (!bfglob.server.wasConnected) {
          this.connectStats = 1002
          return
        }
        this.system = this.getSysIdFromHash()
        bfglob.sysId = this.system
        if (!this.system || !this.username || !this.password) {
          bfglob.sessionId = ''
          return
        }

        // 标记登录中
        this.connectStats = 1003

        // 判断系统号、用户名、密码变化，清空sessionID
        if (window.bfaccount) {
          if (this.system !== window.bfaccount.system || this.username !== window.bfaccount.username || this.password !== window.bfaccount.password) {
            this.resetSession()
          }
        }

        login
          .login({
            sysId: this.system,
            userName: this.username,
            password: this.password,
          })
          .then(res => {
            this.loginResponse(res)
          })
          .catch(err => {
            this.loginCatch(err)
          })
      },
      inputEnterLogin() {
        if (this.disSignIn) {
          return
        }
        this.loginNow()
      },

      // 订阅处理服务器连接状态信息
      onConnectedToServer() {
        this.connectStats = 1001
        this.disSignIn = false
        // 同步服务器时间, 不需要登录也可以请求
        syncServerTime()
      },
      onReconnectedToServer() {
        this.onConnectedToServer()
      },
      onDisconnectFromServer() {
        bfglob.isLogin = false
        this.connectStats = 1002
      },
      onServerConnectionError(err) {
        bfglob.console.error(err)
        this.onDisconnectFromServer()
      },
      enterAdmin() {
        this.$router.push(`/${DataManageRouteName}`)
      },
      enterDispatch() {
        this.$router.push(`/${DispatchRouteName}`)
      },
      backLogin() {
        this.isLogin = false
        this.connectStats = 1001
      },
      loadImgToDom() {
        const imageSources = {
          login_bg: {
            '1x': new URL('@/assets/images/loginBg/1x/login_bg.webp', import.meta.url).href,
            '2x': new URL('@/assets/images/loginBg/2x/login_bg.webp', import.meta.url).href,
          },
          hud: {
            '1x': new URL('@/assets/images/loginBg/1x/hud.webp', import.meta.url).href,
            '2x': new URL('@/assets/images/loginBg/2x/hud.webp', import.meta.url).href,
          },
          login_box_bg: {
            '1x': new URL('@/assets/images/loginBg/1x/login_box_bg.webp', import.meta.url).href,
            '2x': new URL('@/assets/images/loginBg/2x/login_box_bg.webp', import.meta.url).href,
          },
        }

        const imgNames = Object.keys(imageSources)
        const resolutionKey = window.screen.width > 1920 ? '2x' : '1x'

        for (const name of imgNames) {
          const url = imageSources[name][resolutionKey]
          const img = new Image()
          img.src = url
          img.onload = () => {
            if (this.$refs[name]) {
              this.$refs[name].src = img.src
            }
          }

          img.onerror = () => {
            console.error(`图片加载失败: ${name} at ${url}`)
          }
        }
      },
    },
    watch: {
      system(val) {
        bfglob.sysId = val
      },
      username(val) {
        bfglob.userInfo.name = val
      },
    },
    computed: {
      serverStatusImg() {
        return this.serverConnected ? serverStatusReady : serverStatus
      },
      serverConnected() {
        return this.connectStats !== 1002
      },
      statusTip() {
        let tip = ''
        switch (this.connectStats) {
          case 1102:
          case 1302:
            tip = this.$t('loginDlg.noAdminAndDispatch')
            break
          case 1000:
            tip = this.$t('loginDlg.connecting')
            break
          case 1001:
            tip = this.$t('loginDlg.connected')
            break
          case 1002:
            tip = this.$t('loginDlg.disconnect')
            break
          case 1:
          case 1300:
          case 1101:
            tip = this.$t('loginDlg.noHasUser')
            break
          case 2:
            tip = this.$t('loginDlg.passwordError')
            break
          case 3:
            tip = this.$t('loginDlg.oldSession')
            break
          case 1004:
            tip = this.$t('loginDlg.loginTimeout')
            break
          case 1003:
          default:
            tip = this.$t('loginDlg.logging')
        }

        return tip
      },
      defaultTitle() {
        return bfglob.siteConfig.moveTitle || this.$t('header.moveTitle')
      },
    },
    beforeMount() {
      // 读取系统缓存账号信息
      if (window.bfaccount) {
        this.system = window.bfaccount.system
        this.username = window.bfaccount.username || window.bfaccount.name || ''
        this.password = window.bfaccount.password || (window.bfaccount.len ? ''.padStart(window.bfaccount.len, '*') : '')
        bfglob.sessionId = window.bfaccount.sessionId || window.bfaccount.sid || ''
      }

      bfglob.on('server.connect', this.onConnectedToServer)
      bfglob.on('server.reconnect', this.onReconnectedToServer)
      bfglob.on('server.error', this.onServerConnectionError)
      bfglob.on('server.disconnect', this.onDisconnectFromServer)

      login.connectServer()

      // TODO 这里只有登录后才能收到moveTitle事件，所以暂时无用
      const displayClientTitle = title => {
        this.headTitle = title || this.defaultTitle
      }
      bfglob.on('moveTitle', displayClientTitle)
      if (bfglob.systemSetting.clientTitle) {
        displayClientTitle(bfglob.systemSetting.clientTitle)
      } else if (!this.moveTitle) {
        displayClientTitle(this.defaultTitle)
      }
    },
    beforeUnmount() {
      bfglob.off('server.connect', this.onConnectedToServer)
      bfglob.off('server.reconnect', this.onReconnectedToServer)
      bfglob.off('server.error', this.onServerConnectionError)
      bfglob.off('server.disconnect', this.onDisconnectFromServer)
    },
    mounted() {
      this.loadImgToDom()
    },
  }
</script>

<style lang="scss" scoped>
  .content {
    width: 1068px;
    height: 708px;

    .login-box {
      width: 916px;
      height: 641px;
      padding: 110px 86px 88px 86px;

      .login-box-header {
        height: 60px;

        .login-box-back {
          width: 115px;
          height: 35px;
          background: url('@/assets/images/loginBg/1x/back_login_bg.webp') no-repeat center center;
          background-size: 100% 100%;
          font-family: YouSheBiaoTiHei;
          font-size: 16px;
        }

        .login-box-title {
          height: 50px;
          font-size: 30px;
          font-family: YouSheBiaoTiHei;
          color: #fff;
          letter-spacing: 2px;
        }

        img {
          width: 40px;
          height: 40px;
        }
      }

      .login-box-form {
        :deep(.el-form) {
          height: auto;

          .el-form-item {
            width: 300px;

            .el-input__wrapper {
              height: 45px;
              background-color: transparent !important;
              font-size: 18px;
              padding: 2.5px;

              input::placeholder {
                color: #b0eef9;
                font-size: 18px;
                text-align: center;
                letter-spacing: 4px;
                text-indent: -46px;
              }

              input {
                color: #b0eef9;
              }
            }

            img {
              width: 40px;
              height: 40px;
            }
          }

          .username-item.el-form-item {
            margin-bottom: 30px;
          }

          .password-item.el-form-item {
            margin-bottom: 46px;
          }

          .login-button-item.el-form-item {
            margin-bottom: 20px;
          }

          .login-status.el-form-item {
            margin-bottom: 0px;
          }

          .el-form-item.login-status .el-form-item__content {
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 14px;
            line-height: 20px;
          }
        }

        .login-btn-group {
          width: 300px;

          .el-button span {
            width: 100%;
            display: flex;
            align-items: center;
            position: relative;

            img {
              width: 35px;
              height: 35px;
            }
          }
        }
      }

      .login_type_btn {
        height: 45px;
        width: 100%;
        background-color: #1164ff;
        font-family: YouSheBiaoTiHei;
        font-weight: normal;
        font-size: 21px;
      }
    }
    .login-box.isLogin {
      background: url('@/assets/images/loginBg/1x/platform_bg.webp') no-repeat center center;
      background-size: 100% 100%;
    }
  }

  @media screen and (min-width: 2561px) {
    .content {
      .login-box-back {
        background: url('@/assets/images/loginBg/2x/back_login_bg.webp') no-repeat center center;
        background-size: 100% 100%;

        .login-box.isLogin {
          background: url('@/assets/images/loginBg/2x/platform_bg.webp') no-repeat center center;
          background-size: 100% 100%;
        }
      }
    }
  }
</style>
