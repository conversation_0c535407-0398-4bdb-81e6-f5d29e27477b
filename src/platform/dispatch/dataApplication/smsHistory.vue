<template>
  <div>
    <historyCommon
      v-show="type === 0"
      ref="hisCom"
      :dbListName="queryProps.dbListName"
      :cmd="queryProps.cmd"
      :label-width="labelWidth"
      :deviceDmrId="deviceDmrId"
      :userRid="userRid"
      :head="dthead"
      :name="dataTableName"
      :history-table="true"
      :replaceHistoryTableData="undefined"
      :parse-request-data="parseRequestData"
      :afterRequestEnd="showDataTable"
      @clear-dt="clearNotConfirmDt"
      @remove-data-table-data="removeDataTableData"
      @time-changed="onTimeChanged"
    >
      <template #optionsFormItem>
        <el-form-item :label="$t('dialog.senderDevice')" prop="deviceRid">
          <DataTableElSelect v-model="deviceDmrId" filterable clearable :placeholder="$t('dialog.select')" :no-match-text="$t('dialog.noMatchText')">
            <el-option v-for="item in deviceRids" :key="item.rid" :label="item.label" :value="item.dmrId" />
          </DataTableElSelect>
        </el-form-item>
        <el-form-item :label="$t('dataTable.senderName')" prop="userRid">
          <DataTableElSelect v-model="userRid" filterable clearable :placeholder="$t('dialog.select')" :no-match-text="$t('dialog.noMatchText')">
            <el-option v-for="item in userRids" :key="item.rid" :label="item.label" :value="item.rid" />
          </DataTableElSelect>
        </el-form-item>
      </template>
      <template #queryBtn>
        <div class="flex">
          <el-button
            type="primary"
            class="smsBtn w-[120px] bg-[url('@/assets/images/manage/search_btn.svg')] hover:bg-[url('@/assets/images/manage/search_btn_hover.svg')] bg-no-repeat bg-center bg-transparent border-0 active:opacity-70 disabled:opacity-50 transition duration-100 ease-in-out"
            @click="queryFunction()"
          >
            <ellipsis-text class="w-full mb-0" :content="$t('dialog.querySentSMS')" />
          </el-button>
          <el-button
            type="primary"
            class="smsBtn w-[130px] bg-[url('@/assets/images/manage/search_btn.svg')] hover:bg-[url('@/assets/images/manage/search_btn_hover.svg')] bg-no-repeat bg-center bg-transparent border-0 active:opacity-70 disabled:opacity-50 transition duration-100 ease-in-out"
            @click="queryFunction(1)"
          >
            <ellipsis-text class="w-full mb-0" :content="$t('dialog.queryNotSentSMS')" />
          </el-button>
        </div>
      </template>
    </historyCommon>
    <historyCommon
      v-show="type === 1"
      ref="notConfirmDt"
      :dbListName="'db_not_confirm_sms_list'"
      :cmd="queryProps.cmd"
      :label-width="labelWidth"
      :deviceDmrId="deviceDmrId"
      :userRid="userRid"
      :head="dthead"
      :name="dataTableName"
      :history-table="true"
      :parse-request-data="parseRequestData"
      :afterRequestEnd="showDataTable"
      @clear-dt="clearNotConfirmDt"
      @remove-data-table-data="removeDataTableData"
      @time-changed="onTimeChanged"
    >
      <template #optionsFormItem>
        <el-form-item :label="$t('dialog.senderDevice')" prop="deviceRid">
          <DataTableElSelect v-model="deviceDmrId" filterable clearable :placeholder="$t('dialog.select')" :no-match-text="$t('dialog.noMatchText')">
            <el-option v-for="item in deviceRids" :key="item.rid" :label="item.label" :value="item.dmrId" />
          </DataTableElSelect>
        </el-form-item>
        <el-form-item :label="$t('dataTable.senderName')" prop="userRid">
          <DataTableElSelect v-model="userRid" filterable clearable :placeholder="$t('dialog.select')" :no-match-text="$t('dialog.noMatchText')">
            <el-option v-for="item in userRids" :key="item.rid" :label="item.label" :value="item.rid" />
          </DataTableElSelect>
        </el-form-item>
      </template>
      <template #queryBtn>
        <div class="flex">
          <el-button
            type="primary"
            class="smsBtn w-[120px] bg-[url('@/assets/images/manage/search_btn.svg')] hover:bg-[url('@/assets/images/manage/search_btn_hover.svg')] bg-no-repeat bg-center bg-transparent border-0 active:opacity-70 disabled:opacity-50 transition duration-100 ease-in-out"
            @click="queryFunction()"
          >
            <ellipsis-text class="w-full mb-0" :content="$t('dialog.querySentSMS')" />
          </el-button>
          <el-button
            type="primary"
            class="smsBtn w-[130px] bg-[url('@/assets/images/manage/search_btn.svg')] hover:bg-[url('@/assets/images/manage/search_btn_hover.svg')] bg-no-repeat bg-center bg-transparent border-0 active:opacity-70 disabled:opacity-50 transition duration-100 ease-in-out"
            @click="queryFunction(1)"
          >
            <ellipsis-text class="w-full mb-0" :content="$t('dialog.queryNotSentSMS')" />
          </el-button>
        </div>
      </template>
    </historyCommon>
  </div>
</template>

<script>
  import vueMixin from '@/utils/vueMixin'
  import historyCommon from '@/components/common/historyCommon.vue'
  import bfNotify from '@/utils/notify'

  export default {
    name: 'VsmsHistory',
    mixins: [vueMixin],
    data() {
      return {
        queryProps: {
          dbListName: 'db_sms_history_list',
          cmd: 56,
        },
        dataTableName: 'smsHistoryTable',
        notConfirmSmsBodyCache: [],
        deviceDmrId: '',
        userRid: '',
        deviceRids: bfglob.gdevices.getList(),
        userRids: bfglob.guserData.getList(),
        //判断是否是查询短信历史还是查询未确认短信历史 查询短信历史: 0; 查询未确认短信历史 1;
        type: 0,
        notConfirmSmsDataTable: {
          name: 'notConfirmSmsHistoryTable',
          body: [],
        },
      }
    },
    methods: {
      queryFunction(type = 0) {
        this.type = type

        // 根据type判断执行哪个组件的查询
        if (type === 0) {
          // 查询短信历史
          this.$refs.hisCom.queryFunc(type)
        } else if (type === 1) {
          // 查询未确认短信
          this.$refs.notConfirmDt.queryFunc(type)
        }
      },

      // 处理时间变化事件，同步两个historyCommon组件的时间
      onTimeChanged(timeData) {
        const { startTime, endTime } = timeData

        // 同步到另一个historyCommon组件
        if (this.$refs.hisCom && this.$refs.notConfirmDt) {
          // 获取事件源组件
          const sourceComponent = this.$refs.hisCom.query.startTime === startTime ? this.$refs.hisCom : this.$refs.notConfirmDt
          const targetComponent = sourceComponent === this.$refs.hisCom ? this.$refs.notConfirmDt : this.$refs.hisCom

          // 同步时间到目标组件
          if (targetComponent && targetComponent.query) {
            targetComponent.query.startTime = startTime
            targetComponent.query.endTime = endTime
            targetComponent.query.fromTime = sourceComponent.query.fromTime
            targetComponent.query.toTime = sourceComponent.query.toTime
          }
        }
      },
      showDataTable(dbListOfProtoTypeName, tableBody) {
        const checkDataLength = () => {
          if (dbListOfProtoTypeName !== this.queryProps.dbListName) {
            this.notConfirmSmsDataTable.body = tableBody
          }
          if (tableBody.length <= 0) {
            bfNotify.messageBox(this.$t('msgbox.noRecords'), 'info')
          }
        }

        checkDataLength()
      },
      removeDataTableData() {
        this.deviceDmrId = ''
        this.userRid = ''
        this.notConfirmSmsDataTable.body = []
      },
      parseRequestData(item, dbListOfProtoTypeName) {
        if (dbListOfProtoTypeName === 'db_not_confirm_sms_list') return
        // 系统中心下发
        if (item.senderDmrid === '00000000') {
          const userData = bfglob.guserData.get(item.senderUserRid)
          if (userData) {
            item.orgShortName = bfglob.guserData.getOrgNameByKey(userData.orgId)
          }
          item.senderName = ''
        } else {
          const device = bfglob.gdevices.getDataByIndex(item.senderDmrid)
          if (!device) {
            bfglob.console.error('没有此对讲机', item.senderDmrid)
            return
          }
          item.orgShortName = device.orgShortName
          item.senderName = device.selfId
        }

        const repeater = bfglob.gcontrollers.getDataByIndex(item.receiveRepeater)
        item.repeaterName = repeater && repeater.selfId ? repeater.selfId : ''

        // 目标是全呼
        if (item.targetDmrid === bfglob.fullCallDmrId) {
          item.receiveName = this.$t('dialog.fullCall')
        } else if (item.targetDmrid === bfglob.systemDmrId) {
          // 目标是系统中心
          item.receiveName = this.$t('dialog.systemCenter')
        } else {
          let receiveName = item.targetDmrid
          // 目标是组呼
          let targetObj = bfglob.gorgData.getDataByIndex(item.targetDmrid)
          if (targetObj) {
            receiveName = targetObj.orgShortName
          } else {
            // 目标是单呼
            targetObj = bfglob.gdevices.getDataByIndex(item.targetDmrid)
            receiveName = targetObj ? targetObj.selfId : receiveName
          }

          item.receiveName = receiveName
        }

        item.senderUserName = bfglob.guserData.getUserNameByKey(item.senderUserRid)
        if (typeof item.confirmTime === 'string') {
          item.confirmTime = item.confirmTime.startsWith('200') || !item.confirmTime ? '' : item.confirmTime
        }

        // if (dbListOfProtoTypeName === this.queryProps.dbListName) {
        //   // this.bodyCache.push(item)
        //   return item
        // } else {
        //   this.notConfirmSmsBodyCache.push(item)
        // }
        return item
      },
      clearNotConfirmDt() {
        this.$refs.notConfirmDt?.$refs.datatable?.instance.clear()
      },
    },
    components: {
      historyCommon,
    },
    computed: {
      labelWidth() {
        return this.isFR ? '120px' : this.isEN ? '90px' : '80px'
      },
      dthead() {
        return [
          {
            title: this.$t('dialog.parentOrg'),
            data: 'orgShortName',
            width: '100px',
          },
          {
            title: this.$t('dialog.senderDevice'),
            data: 'senderName',
            width: '100px',
            render: (_data, _type, row, _meta) => {
              if (row.senderDmrid === '00000000') {
                return this.$t('dialog.systemCenter')
              } else {
                return row.senderName
              }
            },
          },
          {
            title: this.$t('dataTable.senderName'),
            data: 'senderUserName',
            width: '100px',
          },
          {
            title: this.$t('dialog.receiveDevice'),
            data: 'receiveName',
            width: '120px',
          },
          {
            title: this.$t('dialog.smsContent'),
            data: 'smsContent',
            width: '300px',
          },
          {
            title: this.$t('dialog.smsType'),
            data: null,
            width: '100px',
            render: (_data, _type, row, _meta) => {
              switch (row.smsType) {
                case '12':
                  return this.$t('dialog.autoPlaySms')
                default:
                  return this.$t('dialog.textInfo')
              }
            },
          },
          {
            title: this.$t('dataTable.repeater'),
            data: 'repeaterName',
            width: this.isEN ? '135px' : '100px',
            render: (_data, _type, row, _meta) => {
              if (row.receiveRepeater === '00000000') {
                return this.$t('dialog.systemCenter')
              } else {
                return row.repeaterName
              }
            },
          },
          {
            title: this.$t('dialog.sendTime'),
            data: 'startTime',
            width: '130px',
          },
          {
            title: this.$t('dataTable.confirmTime'),
            data: 'confirmTime',
            width: '130px',
          },
          {
            title: this.$t('dialog.notes'),
            data: 'note',
            width: '120px',
          },
        ]
      },
      notConfirmSmsDataDthead() {
        return [
          {
            title: this.$t('dialog.parentOrg'),
            data: 'orgShortName',
            width: '100px',
          },
          {
            title: this.$t('dialog.senderDevice'),
            data: 'senderName',
            width: '100px',
            render: (_data, _type, row, _meta) => {
              if (row.senderDmrid === '00000000') {
                return this.$t('dialog.systemCenter')
              } else {
                return row.senderName
              }
            },
          },
          {
            title: this.$t('dataTable.senderName'),
            data: 'senderUserName',
            width: '100px',
          },
          {
            title: this.$t('dialog.receiveDevice'),
            data: 'receiveName',
            width: '120px',
          },
          {
            title: this.$t('dialog.smsContent'),
            data: 'smsContent',
            width: '300px',
          },
          {
            title: this.$t('dialog.smsType'),
            data: null,
            width: '100px',
            render: (_data, _type, row, _meta) => {
              switch (row.smsType) {
                case '12':
                  return this.$t('dialog.autoPlaySms')
                default:
                  return this.$t('dialog.textInfo')
              }
            },
          },
          {
            title: this.$t('dataTable.repeater'),
            data: 'repeaterName',
            width: this.isEN ? '135px' : '100px',
            render: (_data, _type, row, _meta) => {
              if (row.receiveRepeater === '00000000') {
                return this.$t('dialog.systemCenter')
              } else {
                return row.repeaterName
              }
            },
          },
          {
            title: this.$t('dialog.sendTime'),
            data: 'startTime',
            width: '130px',
          },
          {
            title: this.$t('dialog.notes'),
            data: 'note',
            width: '120px',
          },
        ]
      },
      dlgTitle() {
        return this.$t('nav.smsHistory')
      },
      isEN() {
        return this.$i18n.locale === 'en'
      },
    },
  }
</script>

<style>
  .smsBtn.el-button span {
    display: inline-block;
    width: 100%;
  }
</style>
