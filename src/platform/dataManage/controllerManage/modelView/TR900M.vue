<template>
  <el-tabs v-model="settingRadio" tab-position="left" class="bf-tab repeaterCmd" :class="{ 'is-mobile': isMobile }">
    <!--    <el-tab-pane-->
    <!--        :label="$t('dialog.repeaterInfo')"-->
    <!--        name="1"-->
    <!--        class="repeaterInfo">-->
    <!--      <RepeaterInfo-->
    <!--          :repeater='repeater'-->
    <!--          :repeaterData='repeaterData'-->
    <!--          :getRepeaterId='getRepeaterId'-->
    <!--          :disabled="notRepeater"-->
    <!--          :saveMethod='saveMethod'>-->
    <!--      </RepeaterInfo>-->
    <!--    </el-tab-pane>-->
    <!--    <el-tab-pane-->
    <!--        :label="$t('dialog.generalSetting')"-->
    <!--        name="2"-->
    <!--        class="commonSetting">-->
    <!--      <el-form :model="commonSetting"-->
    <!--               label-width="95px"-->
    <!--               label-position="top"-->
    <!--               :rules="commonSettingRules"-->
    <!--               ref="commonSetting">-->
    <!--        <el-row :gutter="20">-->
    <!--          <el-col :xs="24" :sm="12">-->
    <!--            <el-form-item :label="$t('dialog.repeaterName')" prop="devName">-->
    <!--              <el-input v-model="commonSetting.devName"-->
    <!--                        :maxlength="8"></el-input>-->
    <!--            </el-form-item>-->
    <!--          </el-col>-->
    <!--          <el-col :xs="24" :sm="12">-->
    <!--            <el-form-item :label="$t('dialog.repeaterId')"-->
    <!--                          prop="repeaterId">-->
    <!--              <el-input v-model.number="commonSetting.repeaterId"-->
    <!--                        :maxlength="8"></el-input>-->
    <!--            </el-form-item>-->
    <!--          </el-col>-->
    <!--          <el-col :xs="24">-->
    <!--            <generateDmrId-->
    <!--                v-model="commonSetting.dmrid"-->
    <!--                isDec-->
    <!--                :gutter="20">-->
    <!--            </generateDmrId>-->
    <!--          </el-col>-->
    <!--          <el-col :xs="24" :sm="12">-->
    <!--            <el-form-item :label="$t('dialog.hangTime')" prop="hangTime">-->
    <!--              <el-input-number v-model="commonSetting.hangTime"-->
    <!--                               :min="1200" :max="6000" :step="30"></el-input-number>-->
    <!--            </el-form-item>-->
    <!--          </el-col>-->
    <!--          <el-col :xs="24" :sm="12">-->
    <!--            <el-form-item :label="$t('dialog.squelchLevel')" prop="squelchLevel">-->
    <!--              <el-input-number v-model="commonSetting.squelchLevel"-->
    <!--                               :min="0" :max="16" :step="1"></el-input-number>-->
    <!--            </el-form-item>-->
    <!--          </el-col>-->
    <!--          <el-col :xs="24" :sm="12">-->
    <!--            <el-form-item :label="$t('dialog.defaultChannel')" prop="defaultChannel">-->
    <!--              <el-input-number v-model="commonSetting.defaultChannel"-->
    <!--                               :min="1" :max="30" :step="1"></el-input-number>-->
    <!--            </el-form-item>-->
    <!--          </el-col>-->
    <!--          <el-col :xs="24" :sm="12">-->
    <!--            <el-form-item :label="$t('dialog.soundTip')" prop="soundTip">-->
    <!--              <el-switch v-model="soundTip"-->
    <!--                         :active-text="$t('dialog.on')"-->
    <!--                         :inactive-text="$t('dialog.off')">-->
    <!--              </el-switch>-->
    <!--            </el-form-item>-->
    <!--          </el-col>-->
    <!--          <el-col :span="24">-->
    <!--            <el-form-item label-width="0" class="center actions">-->
    <!--              <el-button type="primary" -->
    <!--                         @click="queryCommonSetting"-->
    <!--                         :disabled="notRepeater"-->
    <!--                         v-text="$t('dialog.querySetting')"></el-button>-->
    <!--              <el-button type="warning" -->
    <!--                         @click="writeInCommonSetting"-->
    <!--                         :disabled="notRepeater"-->
    <!--                         v-text="$t('dialog.writeIn')"></el-button>-->
    <!--            </el-form-item>-->
    <!--          </el-col>-->
    <!--        </el-row>-->
    <!--      </el-form>-->
    <!--    </el-tab-pane>-->
    <!--    <el-tab-pane-->
    <!--        :label="$t('dialog.buttonDefinition')"-->
    <!--        name="3"-->
    <!--        class="buttonDefinition">-->
    <!--      <RepeaterKey-->
    <!--          :disabled="notRepeater"-->
    <!--          :repeater='repeater'-->
    <!--          :repeaterData='repeaterData'-->
    <!--          :getRepeaterId='getRepeaterId'-->
    <!--          :saveMethod='saveMethod'>-->
    <!--      </RepeaterKey>-->
    <!--    </el-tab-pane>-->
    <!--    <el-tab-pane-->
    <!--        :label="$t('dialog.channelSetting')"-->
    <!--        name="4"-->
    <!--        class="channelSetting">-->
    <!--      <el-tabs v-model="channelSettingModel" type="border-card">-->
    <!--        <el-tab-pane :label="$t('dialog.channelList')" name="list">-->
    <!--          <el-table-->
    <!--              :data="channelDatas"-->
    <!--              border highlight-current-row-->
    <!--              max-height="350px"-->
    <!--              @row-dblclick='showSpecifiedChannel'-->
    <!--              style="width: 100%">-->
    <!--            <el-table-column-->
    <!--                v-for="(column,i) in channelTableColumns"-->
    <!--                :key="i"-->
    <!--                :prop="column.prop"-->
    <!--                :label="column.label"-->
    <!--                :width="column.width"-->
    <!--                :min-width='column.minWidth'>-->
    <!--              <template slot-scope="scope">-->
    <!--                      <span v-text="$t('dialog.low')"-->
    <!--                            v-if="column.prop=='txPower' && scope.row[column.prop]==0"></span>-->
    <!--                <span v-text="$t('dialog.high')"-->
    <!--                      v-else-if="column.prop=='txPower' && scope.row[column.prop]==1"></span>-->
    <!--                <span v-text="hz2Mhz(scope.row[column.prop])"-->
    <!--                      v-else-if="column.prop=='rxFrequency' || column.prop=='txFrequency'"></span>-->
    <!--                <span v-text="scope.row[column.prop]" v-else></span>-->
    <!--              </template>-->
    <!--            </el-table-column>-->
    <!--          </el-table>-->
    <!--        </el-tab-pane>-->
    <!--        <el-tab-pane :label="$t('dialog.channelSetting')" name="setting">-->
    <!--          <TR805005Channel-->
    <!--              ref='channelSetting'-->
    <!--              class='tr805005-channel'-->
    <!--              :disabled="notRepeater"-->
    <!--              :repeater='repeater'-->
    <!--              :repeaterData='repeaterData'-->
    <!--              :getRepeaterId='getRepeaterId'-->
    <!--              :saveMethod='saveMethod'>-->
    <!--          </TR805005Channel>-->
    <!--        </el-tab-pane>-->
    <!--      </el-tabs>-->
    <!--    </el-tab-pane>-->
    <!--    <el-tab-pane-->
    <!--        :label="$t('dialog.networkSetting')"-->
    <!--        name="5"-->
    <!--        class="networkSetting">-->
    <!--      <NetworkSetting-->
    <!--          :disabled="notRepeater"-->
    <!--          :repeater='repeater'-->
    <!--          :repeaterData='repeaterData'-->
    <!--          :getRepeaterId='getRepeaterId'-->
    <!--          :saveMethod='saveMethod'>-->
    <!--      </NetworkSetting>-->
    <!--    </el-tab-pane>-->
    <!--    <el-tab-pane-->
    <!--        :label="$t('dialog.serverSetting')"-->
    <!--        name="6"-->
    <!--        class="serverSetting">-->
    <!--      <ServerSetting-->
    <!--          :disabled="notRepeater"-->
    <!--          :repeater='repeater'-->
    <!--          :repeaterData='repeaterData'-->
    <!--          :getRepeaterId='getRepeaterId'-->
    <!--          :saveMethod='saveMethod'>-->
    <!--      </ServerSetting>-->
    <!--    </el-tab-pane>-->
    <el-tab-pane lazy :label="$t('dialog.curChannelInfo')" name="10" class="repeaterCurChInfo">
      <TR925MChannel
        :disabled="notRepeater"
        :repeater="repeater"
        :repeaterData="repeaterData"
        :getRepeaterId="getRepeaterId"
        :saveMethod="saveMethod"
        :packageName="packageName"
        :operation="Operation.RepeaterCurChInfo"
        :deviceModel="deviceModel"
        queryCurChInfo
        modify
      />
    </el-tab-pane>
    <el-tab-pane lazy :label="$t('dialog.switchChannel')" name="11" class="repeaterCurChSet">
      <SwitchChannel
        :disabled="notRepeater"
        :repeater="repeater"
        :repeaterData="repeaterData"
        :getRepeaterId="getRepeaterId"
        :saveMethod="saveMethod"
        :packageName="packageName"
        :operation="Operation.RepeaterCurChSet"
        :deviceModel="deviceModel"
        :maxChannel="maxChannel"
      />
    </el-tab-pane>
    <el-tab-pane lazy :label="$t('dialog.switchTransmitPower')" name="12" class="repeaterCurPowerSet">
      <SwitchTransmitPower
        :disabled="notRepeater"
        :repeater="repeater"
        :repeaterData="repeaterData"
        :getRepeaterId="getRepeaterId"
        :saveMethod="saveMethod"
        :packageName="packageName"
        :operation="Operation.RepeaterCurPowerSet"
        :deviceModel="deviceModel"
      />
    </el-tab-pane>
    <el-tab-pane lazy :label="$t('dialog.restartRepeater')" name="7" class="restartRepeater">
      <Restart
        :disabled="notRepeater"
        :repeater="repeater"
        :repeaterData="repeaterData"
        :getRepeaterId="getRepeaterId"
        :packageName="packageName"
        :operation="Operation.RepeaterCurChChange"
        :deviceModel="deviceModel"
        :processRestart="false"
      />
    </el-tab-pane>
  </el-tabs>
</template>

<script>
  import { cloneDeep, merge } from 'lodash'
  import bfproto, { tr925PackageName } from '@/modules/protocol'
  import bfutil from '@/utils/bfutil'
  import repeaterWfMod from '@/writingFrequency/repeater'
  import { defineAsyncComponent } from 'vue'
  import * as Models from '@/writingFrequency/interphone/models'

  const CommonSetting = {
    devName: '',
    dmrid: 0,
    repeaterId: '',
    hangTime: 5000,
    soundTip: 1,
    squelchLevel: '5',
    defaultChannel: '1',
  }

  // 最大支持99个信道的中继型号
  const channelNum99SupportModels = [Models.BR105M, Models.TR850M]

  export default {
    name: 'TR900M',
    props: {
      repeaterData: {
        type: [Object, undefined],
      },
      repeater: {
        type: String,
        default: '',
      },
      repeaterModel: {
        type: String,
        default: '',
      },
      getRepeaterId: {
        type: Function,
        default: bfutil.noop,
      },
      saveMethod: {
        type: Function,
        default: bfutil.noop,
      },
    },
    data() {
      return {
        channelSettingModel: 'setting',
        settingRadio: '10',

        commonSetting: {
          ...CommonSetting,
        },
        channelDatas: [],
      }
    },
    methods: {
      getFormName(settingRadio = this.settingRadio) {
        let name = ''
        switch (settingRadio) {
          case '2':
            name = 'commonSetting'
            break
        }

        return name
      },
      getDecodeMsgType(settingRadio = this.settingRadio) {
        let decodeMsgType = 'RepeaterInfo'
        switch (settingRadio) {
          case '2':
            decodeMsgType = 'RepeaterCommonSetting'
            break
        }

        return decodeMsgType
      },

      saveConfig(data) {
        const propName = this.getFormName()
        const val = merge(this[propName], data)

        this[propName] = val
        this.saveMethod(propName, val)

        return this[propName]
      },

      // 常规设置
      queryCommonSetting() {
        const options = merge(this.defQueryOption, {
          paraBin: {
            tableId: 2,
          },
        })

        repeaterWfMod
          .queryConfig(options)
          .then(res => {
            return this.saveConfig(res)
          })
          .catch(err => {
            bfglob.console.error('queryCommonSetting', err)
          })
      },
      writeInCommonSetting() {
        this.$refs.commonSetting.validate(valid => {
          if (!valid) {
            return false
          }
          // 判断dmrid是否已变更，如果已变更则用旧的dmrid
          let dmrId = this.commonSetting.dmrid
          const oldDmrid = this.repeaterData.writeFrequencySetting.commonSetting.dmrid
          if (dmrId !== oldDmrid) {
            dmrId = oldDmrid
          }

          const options = merge(this.defQueryOption, {
            sid: dmrId,
            paraBin: {
              operation: 6,
              tableId: 2,
            },
          })

          repeaterWfMod
            .writeInData(this.commonSetting, options)
            .then(res => {
              return this.saveConfig(this.commonSetting)
            })
            .catch(err => {
              bfglob.console.error('writeInCommonSetting', err)
            })
        })
      },

      showSpecifiedChannel(row) {
        if (!this.$refs.channelSetting && !this.$refs.channelSetting.setChannelSetting) {
          return
        }

        this.$refs.channelSetting.setChannelSetting(row)
        this.channelSettingModel = 'setting'
      },
      hz2Mhz(val) {
        return bfutil.frequencyHz2Mhz(val)
      },
    },
    computed: {
      maxChannel() {
        // BR1050M和TR850M最大信道数为99，其余的最大信道数为3776
        return channelNum99SupportModels.includes(this.deviceModel) ? 99 : 3776
      },
      fullscreen() {
        return this.bfmaxi ? true : !(this.$root.layoutLevel > 0)
      },
      isEn() {
        return this.$i18n.locale === 'en'
      },
      isMobile() {
        return this.$root.layoutLevel === 0
      },
      commonSettingRules() {
        return {
          devName: [
            {
              required: true,
              message: this.$t('dialog.requiredRule'),
              trigger: 'blur',
            },
            // {
            //   validator: (rule, value, callback) => {
            //     // 检测输入的字符串是否有中文
            //     bfutil.cannotIncludeChineseRule(rule, value, callback)
            //   },
            //   trigger: ['blur'],
            // },
          ],
          repeaterId: [
            {
              required: true,
              message: this.$t('dialog.requiredRule'),
              trigger: 'blur',
            },
            {
              validator: (rule, value, callback) => {
                // 检测输入的字符串只能为数字或者空值
                bfutil.mustNumberRule(rule, value, callback)
              },
              trigger: ['blur'],
            },
          ],
        }
      },
      channelTableColumns() {
        return [
          {
            prop: 'chId',
            label: this.$t('dialog.chId'),
            minWidth: this.fullscreen ? 'auto' : this.isEn ? '110px' : '80px',
          },
          {
            prop: 'chName',
            label: this.$t('dialog.chName'),
            minWidth: this.fullscreen ? 'auto' : this.isEn ? '130px' : '100px',
          },
          {
            prop: 'rxFrequency',
            label: this.$t('dialog.rxFrequency'),
            minWidth: this.fullscreen ? 'auto' : this.isEn ? '230px' : '130px',
          },
          {
            prop: 'txFrequency',
            label: this.$t('dialog.txFrequency'),
            minWidth: this.fullscreen ? 'auto' : this.isEn ? '230px' : '130px',
          },
          {
            prop: 'txPower',
            label: this.$t('dialog.txPower'),
            minWidth: this.fullscreen ? 'auto' : this.isEn ? '140px' : '100px',
          },
        ]
      },
      // 没有选中继设备，或中继不在线，则禁用按钮功能
      notRepeater() {
        return !this.repeater || !this.repeaterData || this.repeaterData.ctrlStats !== 1
      },

      // 常规设置计算属性
      soundTip: {
        get() {
          return this.commonSetting.soundTip === 1
        },
        set(val) {
          const soundTip = val ? 1 : 0
          if (this.commonSetting.soundTip !== soundTip) {
            this.commonSetting.soundTip = soundTip
          }
          return val
        },
      },

      defQueryOption() {
        return {
          sid: this.repeater,
          paraInt: this.getRepeaterId(),
          paraBin: {
            operation: 5,
          },
          decodeMsgType: this.getDecodeMsgType(),
          packageName: tr925PackageName,
        }
      },
      packageName() {
        return tr925PackageName
      },
      Operation() {
        return bfproto.lookupEnum('RepeaterOperation')
      },
      deviceModel() {
        return this.repeaterModel || 'TR900M'
      },
    },
    watch: {
      'repeaterData.writeFrequencySetting.allChannelSetting': {
        immediate: true,
        deep: true,
        handler(val) {
          // 生成表格需要的数据数组
          const data = []
          for (const k in val) {
            data.push(cloneDeep(val[k]))
          }
          this.channelDatas = data
        },
      },
      'repeaterData.writeFrequencySetting.commonSetting': {
        immediate: true,
        deep: true,
        handler(val) {
          this.commonSetting = merge({}, val || CommonSetting)
        },
      },
    },
    components: {
      // generateDmrId: defineAsyncComponent(() => import('@/components/common/generateDmrId')),
      // RepeaterInfo: defineAsyncComponent(() => import('@/platform/dataManage/controllerManage/common/RepeaterInfo')),
      Restart: defineAsyncComponent(() => import('@/platform/dataManage/controllerManage/common/Restart')),
      SwitchTransmitPower: defineAsyncComponent(() => import('@/platform/dataManage/controllerManage/common/SwitchTransmitPower')),
      SwitchChannel: defineAsyncComponent(() => import('@/platform/dataManage/controllerManage/common/SwitchChannel')),
      // NetworkSetting: defineAsyncComponent(() => import('@/platform/dataManage/controllerManage/common/NetworkSetting')),
      // RepeaterKey: defineAsyncComponent(() => import('@/platform/dataManage/controllerManage/common/RepeaterKey')),
      // ServerSetting: defineAsyncComponent(() => import('@/platform/dataManage/controllerManage/common/ServerSetting')),
      TR925MChannel: defineAsyncComponent(() => import('@/platform/dataManage/controllerManage/tr925/Channel')),
    },
  }
</script>
