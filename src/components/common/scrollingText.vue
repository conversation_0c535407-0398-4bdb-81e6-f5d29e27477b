<template>
  <section ref="scrollWrap" class="scroll-wrapper">
    <div ref="scrollContent" class="scroll-content" :style="{ animationPlayState: canScrollTitle ? 'running' : 'paused' }">
      <h3 class="scroll-text you-she-biao-ti-hei" v-text="title" />
      <h3 class="scroll-text you-she-biao-ti-hei" v-text="title" />
    </div>
  </section>
</template>

<script>
  export default {
    name: 'ScrollingText',
    props: {
      title: {
        type: String,
        default: '',
      },
      scrollStatus: {
        type: Boolean,
        default: false,
      },
    },
    computed: {
      canScrollTitle() {
        return this.scrollStatus && this.title && this.layout >= 2
      },
      layout() {
        return this.$root.layoutLevel
      },
    },
  }
</script>

<style lang="scss">
  .scroll-wrapper {
    width: 644px;
    height: 50px;
    overflow: hidden;
    position: relative;
    display: flex;
    align-items: center;
  }

  .scroll-content {
    display: flex;
    animation: scroll-text 20s linear infinite;
  }

  .scroll-text {
    white-space: nowrap;
    font-size: 44px;
    line-height: 50px;
    height: 50px;
    font-weight: 400;
    letter-spacing: 2px;
    margin: 0;
    padding: 0;
    margin-right: 100px; /* 两段文字之间的间隔 */

    background: linear-gradient(to bottom, #ffffff 32%, #245398 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  @keyframes scroll-text {
    0% {
      transform: translateX(0);
    }
    100% {
      transform: translateX(-50%);
    }
  }
</style>
