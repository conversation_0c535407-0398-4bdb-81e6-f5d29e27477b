<template>
  <div class="dz148-wrapper DZ148SVT-wrapper">
    <el-tabs v-model="settingRadio" tab-position="left" class="bf-tab repeaterCmd" :class="{ 'is-mobile': isMobile }">
      <el-tab-pane lazy :label="$t('dialog.vcRepeater')" name="8" class="vcRepeater-pane">
        <el-table :data="vcRepeaterList" border size="small" style="width: 100%" class="bf-table">
          <el-table-column type="index" width="50" label="#" />
          <el-table-column :label="$t('dialog.repeaterName')" min-width="140">
            <template #default="scope">
              {{ getControllerName(scope.row.deviceId) }}
            </template>
          </el-table-column>
          <el-table-column label="DMRID" min-width="140">
            <template #default="scope">
              {{ toHexDmrId(scope.row.deviceId) }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('dialog.txFrequency')" min-width="120">
            <template #default="scope">
              {{ scope.row.txFreq ? frequencyHz2Mhz(scope.row.txFreq) : '' }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('dialog.rxFrequency')" min-width="120">
            <template #default="scope">
              {{ scope.row.rxFreq ? frequencyHz2Mhz(scope.row.rxFreq) : '' }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('dataTable.registered')" min-width="100">
            <template #default="scope">
              <span v-if="checkIsReg(scope.row)">
                <!--注册-->
                {{ $t('dialog.yes') }}
              </span>
              <span v-else>
                <!--注销-->
                {{ $t('dialog.no') }}
              </span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('repeaterWriteFreq.repeaterModel')" min-width="100">
            <template #default="scope">
              {{ decodeRepeaterName(scope.row.deviceCode) }}
            </template>
          </el-table-column>

          <!-- 操作列 -->
          <el-table-column fixed="right" label="#" width="110" class-name="actions">
            <template #default="scope">
              <bfButton color-type="primary" icon="more" circle :disabled="!checkIsReg(scope.row)" @click="svtRepeaterAction(scope.row)" />
              <bfButton color-type="primary" icon="refresh" circle :disabled="scope.row.ctrlStats !== 1" @click="querySvtRepeaterInfo(scope.row)" />
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>
    <!--使用keep-alive缓存组件数据、状态，以便下次快速渲染-->
    <!-- 虚拟集群中继写频对话框 -->
    <bfDialog
      :title="$t('dialog.vcRepeater')"
      v-model="visible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :fullscreen="fullscreen"
      custom-class="svtRepeater-dialog"
      append-to-body
      class="svtRepeater-dialog-wrapper"
      :class="$i18n.locale"
      center
    >
      <el-tabs v-model="repeaterRadio" tab-position="left" class="svtRepeater-tabs repeaterCmd" :class="{ 'is-mobile': isMobile }">
        <el-tab-pane lazy :label="$t('repeaterWriteFreq.stateInfo')" :name="clientCmd.SVT_RELAY_STATUS + ''" class="repeaterStateInfo">
          <div :class="['m-3', 'state-info-container', locale]">
            <div class="my-3 state-info-title">
              {{ $t('dialog.repeaterName') }}:
              {{ getControllerName(stateInfo.deviceId) }}
            </div>
            <div class="state-info-row">
              <div v-for="key in stateInfoKeys" :key="key" class="state-info-col">
                <span class="label">{{ $t(`repeaterWriteFreq.state.${key}`) }}:</span>
                <span :class="['value', getStateInfoPropStatusClass(key, stateInfo[key])]">
                  {{ getStateInfoPropStatus(key, stateInfo[key]) }}
                </span>
              </div>
              <!--                是否自由中继，不显示 -->
              <!--                <div class="state-info-col">-->
              <!--                  <span class="label">{{ $t('repeaterWriteFreq.state.freeRelay') }}:</span>-->
              <!--                  <template v-if="stateInfo.freeRelay === 1">-->
              <!--                    <span class="value">{{ $t('dialog.yes') }}</span>-->
              <!--                  </template>-->
              <!--                  <template v-else>-->
              <!--                    <span class="value" style="color: red">{{ $t('dialog.no') }}</span>-->
              <!--                  </template>-->
              <!--                </div>-->
            </div>
            <div class="state-info-actions center actions">
              <bfButton color-type="primary" @click="queryRepeaterStateInfo" :disabled="notRepeater" v-text="$t('dialog.query')" />
            </div>
          </div>
        </el-tab-pane>
        <el-tab-pane lazy :label="$t('dialog.generalSetting')" :name="clientCmd.SVT_RELAY_CFG + ''" class="commonSetting">
          <el-form :model="commonSetting" :rules="commonSettingFormRules" :label-width="labelWidth" label-position="left" ref="commonSetting">
            <el-form-item class="form-label">
              <template #label>
                <EllipsisText>{{ $t('dialog.chHangTime', { unit: 'ms' }) }}:</EllipsisText>
              </template>
              <bfInputNumberV2 v-model="commonSetting.chHangTime" class="!w-full" :min="1000" :max="8000" :step="100" />
            </el-form-item>
            <el-form-item class="form-label">
              <template #label>
                <EllipsisText>{{ $t('dialog.praviteHangTime', { unit: 'ms' }) }}:</EllipsisText>
              </template>
              <bfInputNumberV2 v-model="commonSetting.privateHangTime" class="!w-full" :min="0" :max="7000" :step="100" />
            </el-form-item>
            <el-form-item class="form-label">
              <template #label>
                <EllipsisText>{{ $t('dialog.groupHangTime', { unit: 'ms' }) }}:</EllipsisText>
              </template>
              <bfInputNumberV2 v-model="commonSetting.groupHangTime" class="!w-full" :min="0" :max="7000" :step="100" />
            </el-form-item>
            <el-form-item class="form-label">
              <template #label>
                <EllipsisText>{{ $t('dialog.emerHangTime', { unit: 'ms' }) }}:</EllipsisText>
              </template>
              <bfInputNumberV2 v-model="commonSetting.emerHangTime" class="!w-full" :min="0" :max="7000" :step="100" />
            </el-form-item>
            <el-form-item class="form-label">
              <template #label>
                <EllipsisText>{{ $t('dialog.testMachineSignalDuration', { unit: 'ms' }) }}:</EllipsisText>
              </template>
              <bfInputNumberV2 v-model="commonSetting.autoSignalDurtion" class="!w-full" :min="240" :max="1800" :step="100" />
            </el-form-item>
            <el-form-item class="form-label">
              <template #label>
                <EllipsisText>{{ $t('dialog.testMachineSignalInterval', { unit: 'ms' }) }}:</EllipsisText>
              </template>
              <bfInputNumberV2 v-model="commonSetting.autoSignalInterval" class="!w-full" :min="960" :max="18000" :step="100" />
            </el-form-item>
            <el-form-item class="form-label" prop="rxFreq">
              <template #label>
                <EllipsisText>{{ $t('dialog.rxFrequency') }}:</EllipsisText>
              </template>
              <FrequencyMhz v-model="commonSetting.rxFreq" :maxlength="9" />
            </el-form-item>
            <el-form-item class="form-label" prop="txFreq">
              <template #label>
                <EllipsisText>{{ $t('dialog.txFrequency') }}:</EllipsisText>
              </template>
              <FrequencyMhz v-model="commonSetting.txFreq" :maxlength="9" />
            </el-form-item>
            <el-form-item class="form-label" prop="cc">
              <template #label>
                <EllipsisText>{{ $t('dialog.colorCodes') }}:</EllipsisText>
              </template>
              <bfInputNumberV2 v-model="commonSetting.cc" class="!w-full" :min="0" :max="15" :step="1" />
            </el-form-item>
            <el-form-item label=" ">
              <bfCheckbox v-model="commonSetting.authEnable" @change="authEnableChange">
                <span v-text="$t('writeFreq.enableAuth')" />
              </bfCheckbox>
            </el-form-item>
            <el-form-item class="form-label" prop="authKey">
              <template #label>
                <EllipsisText>{{ $t('writeFreq.authenticationSecretKey') }}:</EllipsisText>
              </template>
              <bfInput v-model="commonSetting.authKey" :maxlength="32" :disabled="!commonSetting.authEnable" @change="authKeyChange" />
            </el-form-item>
            <el-form-item label=" " class="center actions">
              <bfButton color-type="primary" @click="queryCommonSetting" :disabled="notRepeater" v-text="$t('dialog.query')" />
              <bfButton color-type="primary" @click="writeGeneralSetting" v-text="$t('dialog.writeIn')" :disabled="notRepeater" />
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane lazy :label="$t('dialog.switchTransmitPower')" :name="clientCmd.TX_POWER_SWITCH + ''" class="repeaterCurPowerSet">
          <el-form :model="power" :label-width="labelWidth" label-position="left" ref="powerSwitch">
            <el-form-item class="form-label">
              <template #label>
                <EllipsisText>{{ $t('dialog.txPower') }}:</EllipsisText>
              </template>
              <bfSelect
                v-model="power.txPower"
                class="!w-full !h-[50px]"
                popper-class="DZ148SVT-select-dropdown"
                :placeholder="$t('dialog.select')"
                filterable
                :no-match-text="$t('dialog.noMatchText')"
              >
                <el-option v-for="item in powerList" :key="item.label" :label="item.label" :value="item.value" />
              </bfSelect>
            </el-form-item>
            <el-form-item class="form-label">
              <template #label>
                <EllipsisText>{{ $t('dialog.customPower') }}:</EllipsisText>
              </template>
              <bfInputNumberV2 v-model="power.customPower" class="!w-full" :min="5" :max="50" :disabled="power.txPower !== 3" />
            </el-form-item>
            <el-form-item label=" " class="center actions">
              <bfButton color-type="primary" @click="queryPowerInfo" :disabled="notRepeater" v-text="$t('dialog.query')" />
              <bfButton color-type="primary" @click="switchPower" :disabled="notRepeater" v-text="$t('dialog.writeIn')" />
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane lazy :label="$t('dialog.restartRepeater')" :name="clientCmd.DEVICE_REBOOT + ''" class="restartRepeater">
          <el-form ref="restartRepeater" :label-width="labelWidth" label-position="left">
            <el-form-item class="center actions">
              <bfButton color-type="primary" @click="restartSvtRepeater" v-text="$t('dialog.restartRepeater')" :disabled="notRepeater" />
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>
    </bfDialog>
  </div>
</template>

<script>
  import bfproto, { kcpPackageName } from '@/modules/protocol'
  import { messageBox } from '@/utils/notify'
  import validateRules from '@/utils/validateRules'
  import { frequencyHz2Mhz, frequencyMhz2Hz, objToArray, toHexDmrId } from '@/utils/bfutil'
  import { defineAsyncComponent } from 'vue'
  import EllipsisText from '@/components/common/EllipsisText.vue'
  import { convertPxToRem, calcScaleSize } from '@/utils/setRem'
  import bfButton from '@/components/bfButton/main.ts'
  import bfInput from '@/components/bfInput/main.ts'
  import bfSelect from '@/components/bfSelect/main.ts'
  import bfInputNumberV2 from '@/components/bfInputNumber/main.ts'
  import bfCheckbox from '@/components/bfCheckbox/main.ts'
  import bfDialog from '@/components/bfDialog/main'

  import {
    CLIENT_CMD,
    queryCommonSetting,
    querySvtRepeaterInfo,
    querySvtRepeaterPowerInfo,
    querySvtRepeaterStateInfo,
    restartSvtRepeater,
    saveSvtRepeaterInfo,
    switchPower,
    writeCommonSetting,
  } from '@/writingFrequency/repeater/DZ148SVT'
  import RepeaterWfMixin from '@/utils/repeaterWfMixin'
  import { getModelName } from '@/writingFrequency/modelInfo'

  export default {
    name: 'DZ148SVT',
    mixins: [RepeaterWfMixin],
    props: {
      repeater: {
        type: String,
        default: '',
      },
    },
    data() {
      return {
        settingRadio: '8',
        visible: false,
        repeaterRadio: CLIENT_CMD.SVT_RELAY_STATUS + '',
        vcRepeaterList: [],

        // currentRow存储的是虚拟集群中继上报的数据
        currentRow: null,
        power: {
          txPower: 0,
          customPower: 1,
        },
        commonSetting: {
          /**信道挂起时间 1000~8000 **/
          chHangTime: 3000,
          /**单呼挂起时间 0~7000**/
          privateHangTime: 5000,
          /**组呼挂起时间 0~7000**/
          groupHangTime: 5000,
          /**紧急呼叫挂起时间 0~7000**/
          emerHangTime: 5000,
          /**自动发送信标时长 240~1800**/
          autoSignalDurtion: 240,
          /**自动发送信标间隔 960~18000**/
          autoSignalInterval: 960,
          /**接收频率 **/
          rxFreq: 0,
          /**发射频率 **/
          txFreq: 0,
          /**色码 0~15**/
          cc: 0,
          /**鉴权开关**/
          authEnable: false,
          ubAuthEnable: 1,
          /**鉴权秘钥**/
          authKey: '',
          ucnAuthKey: [],
        },
        /**(虚拟集群)中继设备状态信息**/
        stateInfo: {
          /**从机(中继)设备ID**/
          deviceId: 0,
          /**天线状态**/
          antStatus: 0,
          /**温度状态**/
          tempStatus: 0,
          /**GPS同步状态**/
          gpsStatus: 1,
          /**风扇状态**/
          fanStatus: 0,
          /**发射状态**/
          txStatus: 0,
          /**接收状态**/
          rxStatus: 0,
          /**电压状态**/
          volStatus: 0,
          /**PLL锁定状态**/
          pllStatus: 0,
          /**时隙1会话状态**/
          sessionSlot1: 0,
          /**时隙2会话状态**/
          sessionSlot2: 0,
          /**当前是否是自由中继**/
          freeRelay: 0,
        },
      }
    },
    methods: {
      decodeRepeaterName(modelBytes) {
        const modelCode = this.decodeModel(modelBytes)
        return getModelName(modelCode)
      },
      decodeModel(modelBytes) {
        // 6字节机型码+2字节功能码
        return String.fromCharCode.apply(null, Array.from(modelBytes).slice(0, 6))
      },
      authEnableChange() {
        this.commonSetting.authKey = ''
        this.$refs.commonSetting?.clearValidate(['authKey'])
      },
      authKeyChange(val) {
        // 密钥长度必须是2的倍数
        if (val.length % 2 !== 0) {
          this.commonSetting.authKey = val + '0'
        }
      },
      encodeAuthKey(authKey) {
        const authKeyBytes = new Array(16).fill(0)
        for (let i = 0; i < authKey.length; i += 2) {
          const str = authKey.slice(i, i + 2)
          const num = parseInt(str, 16)
          if (!isNaN(num)) {
            authKeyBytes[i / 2] = num
          }
        }
        return authKeyBytes
      },
      decodeAuthKey(bytes) {
        // 过滤后面补0的数据
        let end = bytes.length
        for (let i = bytes.length - 1; i >= 0; i--) {
          if (bytes[i] !== 0) {
            end = i
            break
          }
        }

        // 截取有效的数据
        bytes = bytes.slice(0, end + 1)

        return bytes
          .map(b => b.toString(16).padStart(2, '0'))
          .join('')
          .toUpperCase()
      },
      async queryCommonSetting() {
        if (!this.currentRow) {
          return
        }
        const res = await queryCommonSetting(this.defaultOptions).catch(err => {
          bfglob.console.error('queryCommonSetting err:', err)
        })
        if (!res) {
          return
        }
        messageBox(this.$t('msgbox.selSuccess'), 'success')
        bfglob.console.log('queryCommonSetting res:', res)
        this.commonSetting = {
          ...this.commonSetting,
          ...res,
          // 转换后，用于页面显示和设置
          authEnable: res.ubAuthEnable === 1,
          authKey: this.decodeAuthKey(res.ucnAuthKey),
        }
      },
      writeGeneralSetting() {
        if (!this.currentRow) {
          return
        }
        this.commonSetting.ubAuthEnable = this.commonSetting.authEnable ? 1 : 0
        this.commonSetting.ucnAuthKey = this.encodeAuthKey(this.commonSetting.authKey)
        writeCommonSetting(this.commonSetting, this.defaultOptions)
          .then(() => {
            messageBox(this.$t('msgbox.configSuccess'), 'success')
          })
          .catch(err => {
            bfglob.console.error('setChannelConfig err:', err)
          })
      },
      toHexDmrId(deviceId) {
        return toHexDmrId(deviceId, false)
      },
      getControllerName(intDmrId) {
        const dmrId = this.toHexDmrId(intDmrId)
        const controller = bfglob.gcontrollers.getDataByIndex(dmrId)
        return controller?.selfId ?? ''
      },
      getStateInfoPropStatus(key, value) {
        const valueTexts = {
          default: {
            0: this.$t('repeaterWriteFreq.abnormal'),
            1: this.$t('repeaterWriteFreq.normal'),
          },
          antStatus: {
            0: this.$t('repeaterWriteFreq.normal'),
            1: this.$t('repeaterWriteFreq.normal'),
            2: this.$t('repeaterWriteFreq.abnormal'),
          },
          tempStatus: {
            0: this.$t('repeaterWriteFreq.normal'),
            1: this.$t('repeaterWriteFreq.normal'),
            2: this.$t('repeaterWriteFreq.tooHigh'),
            3: this.$t('repeaterWriteFreq.abnormal'),
          },
          gpsStatus: {
            0: this.$t('repeaterWriteFreq.notLocate'),
            1: this.$t('repeaterWriteFreq.hasLocate'),
          },
          fanStatus: {
            0: this.$t('repeaterWriteFreq.normal'),
            1: this.$t('repeaterWriteFreq.normal'),
            2: this.$t('repeaterWriteFreq.abnormal'),
          },
          volStatus: {
            0: this.$t('repeaterWriteFreq.normal'),
            1: this.$t('repeaterWriteFreq.normal'),
            2: this.$t('repeaterWriteFreq.tooHigh'),
          },
          pllStatus: {
            0: this.$t('repeaterWriteFreq.locked'),
            1: this.$t('repeaterWriteFreq.notLocked'),
          },
        }

        return valueTexts[key]?.[value] ?? valueTexts.default?.[value] ?? valueTexts.default?.[0]
      },
      getStateInfoPropStatusClass(key, value) {
        const classes = {
          default: {
            0: 'abnormal',
            1: 'normal',
          },
          antStatus: {
            0: 'normal',
            1: 'normal',
            2: 'abnormal',
          },
          tempStatus: {
            0: 'normal',
            1: 'normal',
            2: 'warning',
            3: 'abnormal',
          },
          gpsStatus: {
            0: 'grey',
            1: 'normal',
          },
          fanStatus: {
            0: 'normal',
            1: 'normal',
            2: 'abnormal',
          },
          volStatus: {
            0: 'normal',
            1: 'normal',
            2: 'abnormal',
          },
          pllStatus: {
            0: 'normal',
            1: 'abnormal',
          },
        }

        return classes[key]?.[value] ?? classes.default?.[value] ?? classes.default?.[0]
      },
      async queryRepeaterStateInfo() {
        if (!this.currentRow) {
          return
        }
        const res = await querySvtRepeaterStateInfo(this.defaultOptions).catch(err => {
          bfglob.console.error('queryRepeaterStateInfo err:', err)
        })
        if (!res) {
          return
        }

        messageBox(this.$t('msgbox.selSuccess'), 'success')
        bfglob.console.log('querySvtRepeaterStateInfo res:', res)

        this.stateInfo = {
          ...this.stateInfo,
          ...res,
        }
      },
      async queryPowerInfo() {
        if (!this.currentRow) {
          return
        }
        const res = await querySvtRepeaterPowerInfo(this.defaultOptions).catch(err => {
          bfglob.console.error('querySvtRepeaterPowerInfo err:', err)
        })

        if (!res) {
          return
        }

        messageBox(this.$t('msgbox.selSuccess'), 'success')
        bfglob.console.log('querySvtRepeaterPowerInfo res:', res)
        this.power = {
          ...this.power,
          ...res,
        }
      },
      switchPower() {
        if (!this.currentRow) {
          return
        }
        switchPower(this.power, this.defaultOptions)
          .then(() => {
            messageBox(this.$t('msgbox.configSuccess'), 'success')
          })
          .catch(err => {
            bfglob.console.error('switchPower err:', err)
          })
      },
      restartSvtRepeater() {
        if (!this.currentRow) {
          return
        }
        restartSvtRepeater(this.defaultOptions)
          .then(() => {
            messageBox(this.$t('msgbox.sendSuccess'), 'success')
          })
          .catch(err => {
            bfglob.console.error('restartSvtRepeater err:', err)
          })
      },
      async querySvtRepeaterInfo(row) {
        this.currentRow = row
        const data = await querySvtRepeaterInfo(this.defaultOptions).catch(bfglob.console.error)
        data && saveSvtRepeaterInfo(data)
      },
      svtRepeaterAction(row) {
        this.currentRow = row
        this.visible = true
      },
      openSvtRepeaterOperation(controller) {
        if (!this.checkIsReg(controller.svtRepeaterInfo)) {
          return
        }
        this.visible = true
        this.currentRow = { ...controller.svtRepeaterInfo }
      },
      frequencyMhz2Hz,
      frequencyHz2Mhz,
      // 新的同播中继上线，需要添加到操作列表中
      syncOneSvtRepeaterInfo(svtRepeaterInfo) {
        // 上线的集群中继，不是挂载到当前集群控制器上的，需要过滤掉
        const dmrId = toHexDmrId(svtRepeaterInfo.deviceId, false)
        const controller = bfglob.gcontrollers.getDataByIndex(dmrId)
        if (controller?.simulcastParent !== this.vcController?.rid) {
          return
        }

        const index = this.vcRepeaterList.findIndex(item => item.deviceId === svtRepeaterInfo.deviceId)
        if (index === -1) {
          this.vcRepeaterList.push(svtRepeaterInfo)
        } else {
          this.vcRepeaterList[index] = svtRepeaterInfo
        }
      },
      listenControllerOnlineState(controller) {
        if (!controller) {
          return
        }

        const intDmrId = parseInt(controller.dmrId, 16)
        const index = this.vcRepeaterList.findIndex(item => item.deviceId === intDmrId)
        if (index === -1) {
          return
        }

        // 给同播中继状态信息对象添加在线标记
        this.vcRepeaterList[index]['ctrlStats'] = controller.ctrlStats
      },
      checkIsReg(row) {
        return row.isReg === 1 || row.isReg === 2
      },
    },
    computed: {
      labelWidth() {
        return convertPxToRem(calcScaleSize(150)) + 'rem'
      },
      vcController() {
        return bfglob.gcontrollers.getDataByIndex(this.repeater)
      },
      stateInfoKeys() {
        // 需要特别处理的状态信息
        // 时隙1、2，是否自由中继(是否加入集群)，接收、发射，屏蔽不显示
        const excludes = ['deviceId', 'freeRelay', 'sessionSlot1', 'sessionSlot2', 'txStatus', 'rxStatus']
        return Object.keys(this.stateInfo).filter(k => !excludes.includes(k))
      },
      clientCmd() {
        return CLIENT_CMD
      },
      defaultOptions() {
        return {
          rpcCmdFields: {
            sid: this.repeater,
          },
          deviceId: this.currentRow.deviceId,
        }
      },
      powerList() {
        /**功率等级 （低:0、中:1、高:2、自定义:3）**/
        return [
          {
            label: this.$t('dialog.low'),
            value: 0,
          },
          {
            label: this.$t('dialog.mid'),
            value: 1,
          },
          {
            label: this.$t('dialog.high'),
            value: 2,
          },
          {
            label: this.$t('dialog.customize'),
            value: 3,
          },
        ]
      },
      // 没有选中继设备，或中继不在线，则禁用按钮功能
      notRepeater() {
        /**注册或注销- 1:注册, 0:注销 2:控制器重新上线后同步在线中继信息**/
        return !this.repeater || !this.currentRow || !this.checkIsReg(this.currentRow)
      },
      commonSettingFormRules() {
        const authKeyRules = []
        if (this.commonSetting.authEnable) {
          const keyReg = /^[0-9A-F]+$/
          authKeyRules.push(validateRules.required())
          authKeyRules.push({
            validator: (rule, value, callback) => {
              if (keyReg.test(value)) {
                callback()
              } else {
                callback(new Error('0-9, A-F'))
              }
            },
            trigger: ['change', 'blur'],
          })
        }
        return {
          authKey: authKeyRules,
        }
      },
    },
    watch: {
      // 选中的同播控制器DMRID
      repeater(val) {
        const controller = bfglob.gcontrollers.getDataByIndex(val)
        if (!controller) {
          return
        }

        const controllers = objToArray(bfglob.gcontrollers.getAll())
        const repeaters = controllers.filter(item => item.simulcastParent === controller.rid)
        if (!repeaters.length) {
          return
        }

        this.vcRepeaterList = repeaters.map(r => {
          if (r.svtRepeaterInfo) {
            r.svtRepeaterInfo.ctrlStats = r.ctrlStats
            return r.svtRepeaterInfo
          }
          const t = bfproto.bfdx_proto_msg_T('SvtRepeatorInfoReport', kcpPackageName)
          const info = t.create()
          info.deviceId = parseInt(r.dmrId, 16)
          info.ctrlStats = r.ctrlStats
          return info
        })
      },
    },
    components: {
      FrequencyMhz: defineAsyncComponent(() => import('@/components/common/FrequencyMhz')),
      EllipsisText,
      bfButton,
      bfInput,
      bfSelect,
      bfInputNumberV2,
      bfCheckbox,
      bfDialog,
    },
    mounted() {
      bfglob.on('openSvtRepeaterOperation', this.openSvtRepeaterOperation)
      bfglob.on('svtRepeaterInfo', this.syncOneSvtRepeaterInfo)
      bfglob.on('update_controller_stats', this.listenControllerOnlineState)
    },
    beforeUnmount() {
      bfglob.off('openSvtRepeaterOperation', this.openSvtRepeaterOperation)
      bfglob.off('svtRepeaterInfo', this.syncOneSvtRepeaterInfo)
      bfglob.off('update_controller_stats', this.listenControllerOnlineState)
    },
  }
</script>

<style lang="scss">
  @import url('@/css/repeaterWf/dz148.scss');

  .form-label {
    height: 50px;
    line-height: 50px;
  }

  .bf-dialog.svtRepeater-dialog {
    .el-tab-pane.repeaterStateInfo {
      .state-info-row .state-info-col {
        .value.normal {
          color: #4caf50;
        }

        .value.abnormal {
          color: #f44336;
        }

        .value.grey {
          color: #9e9e9e;
        }

        .value.warning {
          color: #ff9800;
        }
      }
    }
  }
</style>
