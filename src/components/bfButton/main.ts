import { defineComponent, h, computed } from 'vue'
import type { CSSProperties } from 'vue'
import { ElButton } from 'element-plus'
import { calcScaleSize, convertPxToRemWithUnit } from '@/utils/setRem'
import { v1 as uuidV1 } from 'uuid'
import 'element-plus/es/components/button/style/css'
import './main.scss'

interface BfBtnProps {
  strokeWidth?: number
  width?: number | string
  height?: number | string
  colorType?: 'primary' | 'info' | 'warning' | 'danger' | 'default'
  cornerSize?: string
}

export default defineComponent({
  name: 'BfBtn',
  inheritAttrs: false, // 阻止自动继承 attrs 到根元素
  props: {
    strokeWidth: { type: Number, default: 2 },
    width: { type: [Number, String], default: 122 },
    height: { type: [Number, String], default: 42 },
    colorType: { type: String, default: 'default' },
    cornerSize: { type: String, default: '10px' },
    ...ElButton.props,
  },
  setup(props: BfBtnProps & InstanceType<typeof ElButton>['$props'], { slots, attrs }) {
    const { strokeWidth, width, height, colorType, cornerSize, ...restProps } = props

    const widthValue = computed(() => (typeof width === 'string' ? parseFloat(width) : (width as number)))
    const heightValue = computed(() => (typeof height === 'string' ? parseFloat(height) : (height as number)))

    const pathData = computed(() => {
      const cornerSizeValue = parseFloat(props.cornerSize + '')
      const topLeftCut = cornerSizeValue - 2
      const bottomRightCut = cornerSizeValue + 2
      return `M ${topLeftCut},0 L ${widthValue.value},0 L ${widthValue.value},${heightValue.value - bottomRightCut} L ${widthValue.value - bottomRightCut},${heightValue.value} L 0,${heightValue.value} L 0,${topLeftCut} Z`
    })

    // 直接使用 attrs，因为已经设置了 inheritAttrs: false
    const buttonProps = computed(() => {
      return { ...restProps, ...attrs }
    })

    const gradientId = uuidV1()

    return () =>
      h(
        'div',
        {
          class: ['bf-btn-wrapper', `bf-btn-${colorType}`, `${props.disabled ? 'is-disabled' : ''}`, attrs.class].filter(Boolean).join(' '),
          style: {
            '--bf-btn-container-min-width': convertPxToRemWithUnit(calcScaleSize(widthValue.value)),
            '--bf-btn-container-height': convertPxToRemWithUnit(calcScaleSize(heightValue.value)),
            '--bf-btn-stroke-width': strokeWidth,
            '--bf-btn-corner-size': cornerSize,
            ...((attrs.style as CSSProperties) || {}),
          },
        },
        [
          // ---- SVG 层 (保持不变) ----
          h(
            'svg',
            {
              class: 'bf-btn__renderer',
              width: '100%',
              height: '100%',
              viewBox: `0 0 ${widthValue.value} ${heightValue.value}`,
              preserveAspectRatio: 'none',
              // 外部阴影由 SCSS 控制
            },
            [
              h('defs', null, [
                h('linearGradient', { id: gradientId, x1: '0%', y1: '100%', x2: '0%', y2: '0%' }, [
                  h('stop', { offset: '0%', 'stop-color': 'rgba(var(--bf-btn-stroke-color))', 'stop-opacity': '1' }),
                  h('stop', { offset: '100%', 'stop-color': 'rgba(var(--bf-btn-stroke-color))', 'stop-opacity': '0' }),
                ]),
              ]),
              h('path', {
                d: pathData.value,
                fill: 'rgba(var(--bf-btn-fill-color), 0.16)',
                stroke: `url(#${gradientId})`,
                'stroke-width': 'var(--bf-btn-stroke-width)',
                'vector-effect': 'non-scaling-stroke',
              }),
            ]
          ),

          // ---- ElButton 内容层 ----
          h(
            ElButton,
            {
              ...buttonProps.value,
              class: ['bf-btn', buttonProps.value.class].filter(Boolean),
            },
            slots
          ),

          // ---- 角落三角装饰层 ----
          h('i', { class: 'bf-btn__corner top-left' }),
          h('i', { class: 'bf-btn__corner bottom-right' }),
        ]
      )
  },
})
