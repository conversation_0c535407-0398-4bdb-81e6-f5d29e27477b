1. 将代码中中使用el-input的地方替换为使用src/components/bfButton/main.ts;
2. 将代码中使用el-input-number的地方替换为使用src/components/bfInputNumber/main.ts, 导入src/components/bfInputNumber/main.ts时应该将名称命名为bfInputNumberV2来使用, 且添加class="!w-full";
3. 将代码中使用el-select的地方替换为使用src/components/bfSelect/main.ts, 并添加class="!w-full !h-[50px]";
4. 将代码中使用el-checkbox的地方替换为使用src/components/bfCheckbox/main.ts;
5. 将代码中使用el-Radio的地方替换为使用src/components/bfRadio/main.ts;
6. 将代码中使用el-button的地方替换为使用src/components/bfButton/main.ts, 如果el-button上使用了class需要删除, 将type名称替换为color-type;
7. 将代码中使用el-transfer的地方替换为使用src/components/bfTransfer/main.ts;
8. 如果有el-form, 将表单设置为`左对齐`, 设置表单的:label-width="labelWidth", labelWidth使用计算属性`return convertPxToRem(calcScaleSize(150)) + 'rem'`, 函数来自于src/utils/setRem.ts;
9. 如果有el-form-item使用, 将label+":", label设置为高度50px, 设置行高为50px, 将label使用src/components/common/EllipsisText.vue组件来包裹;
10. 如果有使用el-table, 则在el-table上添加class="bf-table";
11. 如果有使用el-tooltip或者el-popover组件, 则将popper-class属性添加"bf-tooltip";
12. 如果有使用el-dialog, 则将el-dialog替换为使用'src/components/bfDialog/main'
13. 如果有使用@/components/common/generateDmrId, 将其替换为使用src/components/common/DmridInput.vue;
14. 如果上述完成后, 请继续检查代码中的Components都导入了哪些组件, 将这些组件内部代码继续按照agent-prompt.md来修改;
15. 将`第1-第10`点当作一个`task`来处理, 将`第9点`中的每一次组件都生成一个`task`来处理.
