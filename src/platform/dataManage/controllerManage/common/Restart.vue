<template>
  <el-form ref="restartRepeater" label-position="left" class="w-[400px] flex justify-center gap-2">
    <el-form-item v-if="processRestart" class="center actions">
      <bfButton color-type="primary" :disabled="disabled" @click="repeaterProcessRestart" v-text="$t('dialog.restartRepeater')" />
    </el-form-item>
    <el-form-item class="center actions">
      <bfButton color-type="warning" :disabled="disabled" @click="repeaterRepowerOnAndRestart" v-text="$t('dialog.repowerOnAndRestart')" />
    </el-form-item>
  </el-form>
</template>

<script>
  import repeaterWfMod, { DefModel } from '@/writingFrequency/repeater'
  import bfnotify from '@/utils/notify'
  import bfutil from '@/utils/bfutil'
  import { merge } from 'lodash'
  import { kcpPackageName } from '@/modules/protocol'
  import bfButton from '@/components/bfButton/main.ts'
  import { convertPxToRem, calcScaleSize } from '@/utils/setRem'

  export default {
    name: 'BfRestart',
    components: {
      bfButton,
    },
    props: {
      repeaterData: {
        type: [Object, undefined],
      },
      repeater: {
        type: String,
        default: '',
      },
      getRepeaterId: {
        type: Function,
        default: bfutil.noop,
      },
      disabled: {
        type: Boolean,
        default: false,
      },
      packageName: {
        type: String,
        default: kcpPackageName,
      },
      deviceModel: {
        type: String,
        default: DefModel,
      },
      saveMethod: {
        type: Function,
        default: bfutil.noop,
      },
      operation: {
        type: Number,
        default: 8,
      },
      processRestart: {
        type: Boolean,
        default: true,
      },
    },
    methods: {
      repeaterRestart(options) {
        repeaterWfMod
          .writeInData(null, options)
          .then(res => {
            // 重启中继，需要标记中继下线
            // eslint-disable-next-line
            this.repeaterData.ctrlStats = 0
            // 提示发送指令成功消息
            bfnotify.messageBox(this.$t('msgbox.sendSuccess'), 'success')
          })
          .catch(err => {
            bfglob.console.error('repeaterRestart', err)
          })
      },
      repeaterProcessRestart() {
        const options = merge(this.defQueryOption, {
          paraBin: {
            operation: 8,
          },
        })

        this.repeaterRestart(options)
      },
      repeaterRepowerOnAndRestart() {
        const options = merge(this.defQueryOption, {
          paraBin: {
            operation: 9,
          },
        })

        this.repeaterRestart(options)
      },
    },
    computed: {
      labelWidth() {
        return convertPxToRem(calcScaleSize(150)) + 'rem'
      },
      defQueryOption() {
        return {
          paraBin: {
            operation: this.operation,
          },
          sid: this.repeater,
          paraInt: this.getRepeaterId(),
        }
      },
    },
  }
</script>

<style></style>
