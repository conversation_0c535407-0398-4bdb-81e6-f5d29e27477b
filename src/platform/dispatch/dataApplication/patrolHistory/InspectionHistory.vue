<template>
  <history-common
    v-show="!showTraceMap"
    ref="hisCom"
    :dbListName="queryProps.dbListName"
    :cmd="queryProps.cmd"
    :point="queryProps.point"
    :pointRid="pointRid"
    :label-width="labelWidth"
    :deviceRid="deviceRid"
    :userRid="userRid"
    :head="dthead"
    :name="dataTableName"
    :exportNamePrefix="dlgTitle"
    :parse-request-data="parseRequestdata"
    @data-change="dataChange"
    @remove-data-table-data="removeDataTableData"
  >
    <template #prefixButtons>
      <DataTableRowItem :enable="!isEmpty" iconFont="bfdx-guijixunhuan" @click="() => openPlayer()">
        <ellipsis-text class="mb-0" :content="$t('dialog.playTrack')" />
      </DataTableRowItem>
    </template>
    <template #optionsFormItem>
      <el-form-item :label="$t('dialog.terminalName')" prop="deviceRid">
        <DataTableElSelect v-model="deviceRid" filterable clearable :placeholder="$t('dialog.select')" :no-match-text="$t('dialog.noMatchText')">
          <el-option v-for="item in deviceRids" :key="item.rid" :label="item.label" :value="item.rid" />
        </DataTableElSelect>
      </el-form-item>
      <el-form-item :label="$t('dialog.userName')" prop="userRid">
        <DataTableElSelect v-model="userRid" filterable clearable :placeholder="$t('dialog.select')" :no-match-text="$t('dialog.noMatchText')">
          <el-option v-for="item in userRids" :key="item.rid" :label="item.label" :value="item.rid" />
        </DataTableElSelect>
      </el-form-item>
      <el-form-item prop="pointRid">
        <template #label>
          <EllipsisText :content="$t('dialog.pointName')" class="!w-[64px] text-right" />
        </template>
        <DataTableElSelect v-model="pointRid" filterable clearable :placeholder="$t('dialog.select')" :no-match-text="$t('dialog.noMatchText')">
          <el-option v-for="item in linePointRids" :key="item.rid" :label="item.label" :value="item.rid" />
        </DataTableElSelect>
      </el-form-item>
    </template>
  </history-common>
  <traceMap v-if="showTraceMap" class="flex-auto" :trace-data="$refs.hisCom.dataTableBody" trace-origin="patrolPlayer" @close="closeTraceMap" />
</template>

<script>
  import bfTime from '@/utils/time'
  import vueMixin from '@/utils/vueMixin'
  import HistoryCommon from '@/components/common/historyCommon.vue'
  import EllipsisText from '@/components/common/EllipsisText.vue'
  import traceMap from '@/components/common/traceMap.vue'
  import { useRouteParams } from '@/router'

  const { getRouteParams } = useRouteParams()

  export default {
    mixins: [vueMixin],
    data() {
      return {
        queryProps: {
          dbListName: 'db_rfid_history_list',
          cmd: 48,
          point: true,
        },
        dataTableName: 'InspectionHistoryTable',
        //终端设备、用户名称、巡查点名称
        deviceRid: '',
        userRid: '',
        pointRid: '',
        deviceRids: bfglob.gdevices.getList(),
        userRids: bfglob.guserData.getList(),
        linePointRids: bfglob.glinePoints.getList(),
        //展示轨迹地图
        showTraceMap: false,
        isEmpty: true,
      }
    },
    methods: {
      closeTraceMap() {
        this.showTraceMap = false
        this.$nextTick(() => {
          // 退出轨迹回放地图后调整表格高度
          this.$refs.hisCom.$refs.datatable?.resetTableHeight()
          // 退出轨迹回放地图后调整表头
          this.$refs.hisCom.$refs.datatable?.columnsAdjust()
        })
      },
      dataChange(val) {
        this.isEmpty = val.length === 0
      },
      parseRequestdata(item) {
        var device = bfglob.gdevices.get(item.deviceId)
        if (typeof device === 'undefined') {
          bfglob.console.error('没有此对讲机', item.deviceId)
          return
        }
        var pointItem = bfglob.glinePoints.get(item.pointId)
        if (!pointItem) {
          bfglob.console.error('没有此巡查点', item.pointId)
          return
        }
        item.orgShortName = device.orgShortName
        item.deviceSelfId = device.selfId
        item.userName = bfglob.guserData.getUserNameByKey(item.checkerId)
        item.pointName = pointItem.pointName
        item.pointNo = pointItem.pointId

        return item
      },
      openPlayer() {
        this.showTraceMap = true
      },
      removeDataTableData() {
        this.deviceRid = ''
        this.userRid = ''
        this.pointRid = ''
      },
    },
    components: {
      traceMap,
      HistoryCommon,
      EllipsisText,
    },
    computed: {
      labelWidth() {
        return this.isFR ? '120px' : this.isEN ? '100px' : '90px'
      },
      dthead() {
        return [
          {
            title: this.$t('dialog.parentOrg'),
            data: 'orgShortName',
            width: '120px',
          },
          {
            title: this.$t('dialog.terminalName'),
            data: 'deviceSelfId',
            width: '120px',
          },
          {
            title: this.$t('dialog.userName'),
            data: 'userName',
            width: '120px',
          },
          {
            title: this.$t('dataTable.patrolTime'),
            data: 'checkTime',
            width: this.isFR ? '140px' : '120px',
          },
          {
            title: this.$t('dataTable.pointNo'),
            data: 'pointNo',
            width: this.isEN ? '150px' : '120px',
          },
          {
            title: this.$t('dialog.pointName'),
            data: 'pointName',
            width: this.isFR ? '180px' : this.isEN ? '150px' : '120px',
          },
          {
            title: this.$t('dataTable.receiveTime'),
            data: 'receiveTime',
            width: this.isFR ? '140px' : '120px',
          },
        ]
      },
      dlgTitle() {
        return this.$t('nav.InspectionHistory')
      },
    },
    activated() {
      this.$route.params = getRouteParams(this.$route.name)
      if (this.$route.params.getMoreInspectionHistory) {
        const _time = new Date()
        const hisCom = this.$refs.hisCom
        if (hisCom) {
          hisCom.query.startTime = bfTime.getDayBeforeTheSpecifiedTime(_time, 24 * 7)
          hisCom.query.endTime = _time
          this.pointRid = this.$route.params.pointRid
          this.$nextTick(() => {
            hisCom.queryFunc()
          })
        }
      }
    },
  }
</script>
