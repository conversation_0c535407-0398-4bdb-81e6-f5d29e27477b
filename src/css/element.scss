// .el-menu {
//   --el-menu-item-height: 40px;
//   --el-menu-base-level-padding: 12px;
//   --el-menu-icon-width: 16px;

//   &.el-menu--horizontal {
//     --el-menu-horizontal-height: 40px;
//     --el-menu-border-color: transparent;
//     --el-menu-bg-color: transparent;
//     --el-menu-hover-bg-color: transparent;
//     --el-menu-text-color: #fff;
//     --el-menu-hover-text-color: #fff;
//     --el-menu-active-color: #fff;
//   }

//   &.el-menu--vertical {
//     --el-menu-bg-color: #545c64;
//     --el-menu-text-color: #fff;
//     --el-menu-hover-bg-color: rgb(67, 74, 80);
//     --el-menu-item-height: 56px;
//   }

//   &.el-menu--vertical .is-active {
//     color: #ffd04b;
//   }
// }

// // 子级menu菜单
// .header-top-submenu.el-menu--horizontal .el-menu {
//   --el-menu-active-color: #20a0ff;
//   --el-menu-hover-text-color: #303133;
//   --el-menu-hover-bg-color: #ccc;
// }

// // 对话框header和footer拥有边框线，需要去掉padding
// .el-overlay-dialog .el-dialog {
//   padding: 0;

//   & > * {
//     padding: 12px;
//   }

//   .el-dialog__header {
//     border-bottom: 1px solid var(--el-border-color);
//   }
// }

// // 警告消息框等按钮样式
// .el-overlay-message-box .el-message-box {
//   .el-message-box__btns {
//     justify-content: center;

//     .el-button {
//       width: 36%;
//     }
//   }
// }

// // 使用拖拽自定义指令的弹窗的部分样式
// .drag-dialog-modal {
//   inset: unset !important;

//   .el-overlay-dialog {
//     height: 0;
//     overflow: visible;
//   }
// }

// .el-dialog.drag-dialog {
//   .el-dialog__header {
//     padding: 10px;
//     text-align: left;
//     font-size: 18px;
//   }
//   .el-dialog__header:hover {
//     cursor: move !important;
//   }
// }

// // 表单组件下面的input-number需要宽度100%, 仅限于form-item下第一个就为input-number的，还存在多个input-number组合在一个form-item里面的
// .el-form .el-form-item .el-form-item__content > .el-input-number:first-child {
//   width: 100%;
// }

@use '@/components/bfInput/main.scss' as bfInput;
@use '@/components/bfSelect/main.scss' as bfSelect;
@use '@/components/bfCheckbox/main.scss' as bfCheckbox;
@use '@/assets/fonts/fonts.css' as *;

// 表单内按钮样式
.bf-form-item-button.el-button {
  border: none;
  border-radius: unset;
  background-color: transparent;
  box-shadow: 0 0 0 var(--bf-border-size) var(--bf-form-item-btn-border-color) inset;

  &:hover {
    background-color: transparent;
    box-shadow: 0 0 0 var(--bf-border-size) var(--bf-form-item-btn-hover-border-color) inset;
  }
}

// El Message 样式重置
.bf-message {
  --bf-message-info-border-color: #9A9A9A;
  --bf-message-success-border-color: #1398E9;
  --bf-message-warning-border-color: #FF801D;
  --bf-message-error-border-color: #FF4E4E;

  background-color: #fff;

  &.bf-message-info {
    border: var(--bf-border-size) solid var(--bf-message-info-border-color);
  }

  &.bf-message-success {
    border: var(--bf-border-size) solid var(--bf-message-success-border-color);
  }

  &.bf-message-warning {
    border: var(--bf-border-size) solid var(--bf-message-warning-border-color);
  }

  &.bf-message-error {
    border: var(--bf-border-size) solid var(--bf-message-error-border-color);
  }

  .el-message__icon {
    width: 30px;
    height: 30px;
    border-radius: 2px;

    &:has(.bf-icon-info) {
      background-color: var(--bf-message-info-border-color);
    }

    &:has(.bf-icon-success) {
      background-color: var(--bf-message-success-border-color);
    }

    &:has(.bf-icon-warning) {
      background-color: var(--bf-message-warning-border-color);
    }

    &:has(.bf-icon-error) {
      background-color: var(--bf-message-error-border-color);
    }


    .bf-icon {
      color: #fff;
    }
  }
}

// El dispatch Notify
.bf-dispatch-notify.el-notification {
  --el-notification-width: 196px;
  --el-notification-padding: 0;
  --bf-dispatch-notify-height: 130px;

  --el-notification-group-margin-left: 0;
  --el-notification-group-margin-right: 0;
  --el-notification-title-color: #fff;

  height: var(--bf-dispatch-notify-height);
  background-image: url('@/assets/images/dispatch/map/dispatch-notify-bg.svg');
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;
  background-color: transparent;
  border: none;
  overflow: visible;

  &.right {
    right: 50px;
  }

  bottom: 62px !important;

  .el-notification__group {
    display: flex;
    flex-direction: column;

    .el-notification__title {
      text-align: left;
      font-family: 'YouSheBiaoTiHei';
      font-weight: 400;
      font-size: 14px;
      display: inline-block;
      width: 66px;
      margin-left: 5px;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }

    .el-notification__content {
      flex-grow: 1;

      p {
        height: 100%;
      }

      .siteNotify-content {
        height: 100%;
        position: relative;
        display: flex;
        flex-direction: column;
        padding: 0 10px 10px 10px;
        gap: 6px;

        .content-title {
          height: 16px;
          background: rgba(6, 121, 204, 0.46);
          box-shadow: inset 0px 0px 9px rgba(14, 190, 255, 0.74);
          font-size: 8px;
          font-family: 'AlibabaPuHuiTi-2';
          line-height: 16px;
          text-align: center;
        }

        .content-message {
          flex-grow: 1;
          background: linear-gradient(180deg, rgba(20, 130, 205, 0.435135) 0%, rgba(22, 133, 205, 0.16) 59.48%, rgba(20, 130, 205, 0.0001) 148.25%);
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 6px;
          gap: 4px;

          .bf-iconfont {
            font-size: 45px;
          }

          .message {
            font-size: 14px;
            font-family: 'YouSheBiaoTiHei';
            text-align: left;
          }
        }
      }
    }

    .el-notification__closeBtn {
      position: absolute;
      width: 22px;
      height: 22px;
      top: -4px;
      right: 4px;

      .close_icon {
        width: 22px;
        height: 22px;
        background-image: url('@/components/bfDialog/images/close.png');
        background-size: 100% 100%;
        cursor: pointer;
      }
    }

  }
}

// EL Table 样式重置
.el-table.bf-table {
  --el-table-text-color: #D3F1FF;
  --el-table-header-text-color: #D3F1FF;
  --el-table-header-bg-color: #0055B1;
  --el-table-bg-color: #054972;
  --el-table-tr-bg-color: var(--el-table-bg-color);
  --el-table-border-color: #449AD2;
  --el-table-row-hover-bg-color: rgba(5, 73, 114, 0.7);
  --el-fill-color-lighter: rgba(5, 73, 114, 0.7);

  .el-checkbox {
    @extend .bf-checkbox;
  }

  &.el-table--default,
  &.el-table--fit {
    font-size: 13px;
    text-align: center;
  }

  & thead th.el-table__cell.is-leaf {
    font-weight: normal;
    border-bottom: none;
  }

  &.el-table--border,
  &.el-table--fit .el-table__cell {
    border-right: none;

    &::after {
      content: "";
      position: absolute;
      right: 0;
      /* 靠右 */
      top: 10%;
      /* 从高度的 10% 开始 */
      width: 0;
      /* 宽度为0，让 border-right 来画线 */
      height: 90%;
      /* 占剩下的 90% 高度 */
      border-right: 1px solid #449AD2;
      /* 边框样式 */
    }
  }
}

// El el-time-picker 样式重置
.bf-time-picker.el-date-editor.el-date-editor--time {
  &.el-input {
    @extend .bf-input;
  }
}

.bf-time-picker-popper.el-popper.el-picker__popper {
  --bf-time-picker-base-color: #1398e9;
  --el-popper-bg-color: #012C4A;
  --el-time-picker-popper-border-color: #50D5FF;

  background-color: var(--el-popper-bg-color);
  border: 2px solid transparent;
  border-radius: var(--el-popper-border-radius);
  background-image: linear-gradient(var(--el-popper-bg-color), var(--el-popper-bg-color)), linear-gradient(to bottom, rgba(80, 213, 255, 0.63), rgba(80, 213, 255, 0));
  background-clip: padding-box, border-box;
  background-origin: border-box;
  background-color: transparent;

  .el-popper__arrow::before {
    background-color: var(--el-popper-bg-color);
    border: 2px solid var(--el-time-picker-popper-border-color);
  }

  .el-time-panel {
    margin: 0;
    box-shadow: none;
    border-radius: 0 0 2px 2px;

    .el-time-panel__content {
      &::before {
        border: 0;
      }
    }

    .el-time-spinner__list:after,
    .el-time-spinner__list:before {
      height: 98px;
    }

    .el-time-spinner__item {
      color: var(--bf-time-picker-base-color);
      font-size: 13px;
      height: 28px;
      line-height: 28px;
      text-align: center;

      &.is-active:not(.is-disabled) {
        color: #fff;
      }

      &:hover:not(.is-disabled):not(.is-active) {
        background-color: #fff;
      }
    }

    .el-time-panel__footer {
      display: flex;
      justify-content: center;
      align-items: center;
      gap: 10px;
      border-top: 1px solid #3095bd;
      height: 28px;
      padding: 0;

      .el-time-panel__btn {
        color: var(--bf-time-picker-base-color);
        margin: 0;

        &.confirm {
          color: #fff;
        }
      }
    }
  }
}

// EL el-date-picker input 样式重置
.bf-data-picker.el-date-editor.el-date-editor--date {
  &.el-input {
    @extend .bf-input;
  }
}

.bf-date-picker-popper.el-popper.el-picker__popper {
  height: 260px;
}

// EL el-tooltip 样式重置
.bf-tooltip.el-popper.el-tooltip {
  @extend .bf-select-popper;

  // 根据弹出位置调整渐变方向
  &[data-popper-placement^="top"] {
    background-image:
      linear-gradient(var(--el-popper-bg-color), var(--el-popper-bg-color)),
      linear-gradient(to top, rgba(80, 213, 255, 0.63), rgba(80, 213, 255, 0));
  }

  &[data-popper-placement^="bottom"] {
    background-image:
      linear-gradient(var(--el-popper-bg-color), var(--el-popper-bg-color)),
      linear-gradient(to bottom, rgba(80, 213, 255, 0.63), rgba(80, 213, 255, 0));
  }

  &[data-popper-placement^="left"] {
    background-image:
      linear-gradient(var(--el-popper-bg-color), var(--el-popper-bg-color)),
      linear-gradient(to left, rgba(80, 213, 255, 0.63), rgba(80, 213, 255, 0));
  }

  &[data-popper-placement^="right"] {
    background-image:
      linear-gradient(var(--el-popper-bg-color), var(--el-popper-bg-color)),
      linear-gradient(to right, rgba(80, 213, 255, 0.63), rgba(80, 213, 255, 0));
  }
}

.bf-card.el-card {
  --el-card-bg-color: transparent;
  --el-text-color-primary: #fff;
}

// 对el-tab组件的样式进行覆盖
.el-tabs.bf-tab {
  height: 100%;
  max-height: calc(100vh - 146.5px - 47px - 35px - 40px - 53px - 40px);
  overflow: hidden;

  .el-tabs__header {
    background: linear-gradient(0deg, rgba(0, 63, 107, 0.8), rgba(0, 63, 107, 0.8));
    border-radius: 2px;

    .el-tabs__nav-wrap {

      &::after {
        display: none;
      }

      .el-tabs__nav-scroll {
        .el-tabs__nav {
          .el-tabs__active-bar {
            display: none;
          }

          .el-tabs__item {
            @extend .alibab-puhui-ti-2;
            text-align: center;
            justify-content: center;
            font-weight: 400;
            font-size: 12px;
            color: #FFFFFF;

            &.is-active {
              color: #FF811D;
            }
          }
        }
      }
    }
  }

  .el-tabs__content {
    border-left: none !important;
    overflow-y: scroll;
    overflow-x: hidden;
    background-color: inherit; 
  }
}