<template>
  <bf-dialog
    v-model="visible"
    ref="commonContactEdit"
    :title="t('dialog.editCommonContacts')"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :modal="false"
    class="header-border shadow-md shadow-slate-800 common-contact-edit-dialog drag-dialog"
    modal-class="drag-dialog-modal"
    append-to-body
    draggable
    center
  >
    <div class="w-full flex justify-between gap-4 pt-[15px]">
      <div class="common-contact-edit-container">
        <div class="h-[24px] flex justify-between mb-[10px] text-[16px]">
          {{ t('dialog.availableObjects') }}
          <p>{{ availableContactCheckedText }}</p>
        </div>
        <div class="w-full h-[460px]">
          <VxeTableTree
            ref="avaliableContactTableTree"
            :withPageHeader="false"
            :filter="true"
            :checkAll="false"
            :checkStrictly="true"
            need-full-call-node
            @checkbox-change="updateAvailableContactCheckedText"
          />
        </div>
      </div>

      <div class="flex flex-col justify-center gap-[30px]">
        <button class="h-[25px] w-[66px] flex justify-between items-center text-[16px] leading-[25px] active:opacity-50" @click="onAddCommonContact">
          <i class="bf-iconfont bfdx-youcaozuo size-[25px] before:text-[25px] before:leading-[25px]"></i>
          {{ $t('dialog.add') }}
        </button>
        <button class="h-[25px] w-[66px] flex justify-between items-center text-[16px] leading-[25px] active:opacity-50" @click="onRemoveCommonContact">
          <i class="bf-iconfont bfdx-zuocaozuo size-[25px] before:text-[25px] before:leading-[25px]"></i>
          {{ $t('dialog.remove') }}
        </button>
      </div>

      <div class="common-contact-edit-container">
        <div class="h-[24px] flex justify-between mb-[10px] text-[16px]">
          {{ t('dialog.currentList') }}
          <p>{{ currentContactCheckedText }}</p>
        </div>
        <div class="w-full h-[460px]">
          <VxeTableTree
            ref="currentContactTableTree"
            :withPageHeader="false"
            :filter="true"
            :checkAll="false"
            :enableTree="false"
            :checkStrictly="true"
            :should-sync-source-data="false"
            @checkbox-change="updateCurrentContactCheckedText"
            :filter-source-data="filterCurrentContactSourceData"
          />
        </div>
      </div>
    </div>
  </bf-dialog>
</template>

<script lang="ts" setup>
  import bfDialog from '@/components/bfDialog/main'
  import { onMounted, watch, ref, useTemplateRef } from 'vue'
  import { useI18n } from 'vue-i18n'
  import { TreeNodeData, TreeNodeType } from '@/components/common/tableTree'
  import VxeTableTree from '@/components/common/tableTree/VxeTableTree.vue'
  import {
    deleteDbGroupCallContactByOrgId2db,
    deleteDbSingleCallContactByDeviceId2db,
    insertDbGroupCallContact2db,
    insertDbSingleCallContact2db,
  } from '@/utils/callContact'
  import { useCommonContact } from '@/utils/callContact/commonContact'

  const { t } = useI18n()

  // 接收从openDialog传递的dialogVisible属性
  const props = withDefaults(
    defineProps<{
      dialogVisible?: boolean
      currentContactRids?: string[] | string
    }>(),
    {
      currentContactRids: () => [],
    }
  )
  // 定义emit事件，用于更新dialogVisible
  const emit = defineEmits<{
    'update:dialogVisible': [value: boolean]
  }>()

  // 内部状态
  const visible = ref(false)

  // 监听props.dialogVisible的变化
  watch(
    () => props.dialogVisible,
    newVal => {
      if (newVal !== undefined) {
        visible.value = newVal
      }
    },
    { immediate: true }
  )

  // 监听内部visible的变化，同步到父组件
  watch(visible, newVal => {
    emit('update:dialogVisible', newVal)
  })

  const availableContactTableTreeRef = useTemplateRef('avaliableContactTableTree')
  const currentContactTableTreeRef = useTemplateRef('currentContactTableTree')

  const getVxeTreeManager = (tableTreeRef: InstanceType<typeof VxeTableTree> | null) => {
    return tableTreeRef?.getTreeManager() || null
  }

  const getVxeTreeRef = (tableTreeRef: InstanceType<typeof VxeTableTree> | null) => {
    return getVxeTreeManager(tableTreeRef)?.treeRef || null
  }

  const availableContactCheckedText = ref('')
  const currentContactCheckedText = ref('')

  const updateAvailableContactCheckedText = () => {
    const treeRef = getVxeTreeRef(availableContactTableTreeRef.value)
    if (!treeRef) return
    const total = treeRef.getData().length
    const checked = treeRef.getCheckboxRecords().length
    availableContactCheckedText.value = `${checked}/${total}`
  }

  const updateCurrentContactCheckedText = () => {
    const treeRef = getVxeTreeRef(currentContactTableTreeRef.value)
    if (!treeRef) return
    const total = treeRef.getData().length
    const checked = treeRef.getCheckboxRecords().length
    currentContactCheckedText.value = `${checked}/${total}`
  }

  const filterCurrentContactSourceData = (node: TreeNodeData) => {
    if (!props.currentContactRids.length) {
      return false
    }

    return props.currentContactRids.includes(node.rid)
  }

  const onAddCommonContact = () => {
    const availableContactVxeRef = getVxeTreeRef(availableContactTableTreeRef.value)
    if (!availableContactVxeRef) return
    const currentContactVxeRef = getVxeTreeRef(currentContactTableTreeRef.value)
    if (!currentContactVxeRef) return
    const selectedRows = availableContactVxeRef.getCheckboxRecords() as TreeNodeData[]
    const { commonContacts } = useCommonContact()

    const rows = selectedRows.filter(row => {
      if (commonContacts.value.some(item => item.targetRid === row.rid)) {
        // 丢弃重复联系人
        return false
      }
      switch (row.nodeType) {
        case TreeNodeType.Org:
          insertDbGroupCallContact2db(row.rid, 0)
          break
        case TreeNodeType.Terminal:
          insertDbSingleCallContact2db(row.rid, 0)
          break
        default:
          return false
      }
      return true
    })

    currentContactVxeRef.insert(rows)

    availableContactVxeRef?.setAllCheckboxRow(false)
  }

  const onRemoveCommonContact = () => {
    const availableContactVxeRef = getVxeTreeRef(availableContactTableTreeRef.value)
    if (!availableContactVxeRef) return
    const currentContactVxeRef = getVxeTreeRef(currentContactTableTreeRef.value)
    if (!currentContactVxeRef) return
    const selectedRows = currentContactVxeRef.getCheckboxRecords() as TreeNodeData[]
    const { commonContacts } = useCommonContact()

    const rows = selectedRows.filter(row => {
      const contact = commonContacts.value.find(item => item.targetRid === row.rid)
      if (!contact) {
        // 丢弃不在常用联系人列表中的联系人
        return false
      }
      switch (row.nodeType) {
        case TreeNodeType.Org:
          deleteDbGroupCallContactByOrgId2db(contact.targetRid)
          break
        case TreeNodeType.Terminal:
          deleteDbSingleCallContactByDeviceId2db(contact.targetRid)
          break
        default:
          return false
      }
      return true
    })
    currentContactVxeRef?.remove(rows)
  }

  onMounted(() => {
    setTimeout(() => {
      // setDefaultCheckedNodes()
      updateAvailableContactCheckedText()
      updateCurrentContactCheckedText()
    }, 500) // 增加延迟时间确保树完全加载
  })
</script>

<style lang="scss">
  .common-contact-edit-dialog.el-dialog {
    --el-dialog-width: 650px;
    height: 600px;
  }
  .common-contact-edit-container {
    width: 230px;
    height: 443px;
    font-family: 'AlibabaPuHuiTi2';

    .vxe-table .vxe-table--render-wrapper .vxe-column-content {
      color: #1ac8ed;
    }
  }
</style>
