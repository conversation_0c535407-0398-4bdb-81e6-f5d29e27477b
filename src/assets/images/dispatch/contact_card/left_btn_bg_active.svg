<svg width="112" height="48" viewBox="0 0 112 48" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_2732_359)">
<g filter="url(#filter0_i_2732_359)">
<path d="M72.0442 8.1044L106.343 7.959L111.862 14.4582C111.951 14.5632 112 14.6958 112 14.8329V46.9131C112 47.2346 111.737 47.4952 111.412 47.4952H6.98869L0.137363 39.4022C0.0486411 39.2974 0 39.165 0 39.0283V1.50217C0 1.18062 0.263314 0.919983 0.588176 0.919983H67.927L72.0442 8.1044Z" fill="#FF3939" fill-opacity="0.176471"/>
</g>
<path d="M0.587891 1.21393H67.7568L71.7891 8.25104L71.874 8.39948L72.0459 8.3985L106.206 8.25299L111.638 14.6485C111.682 14.7006 111.706 14.7663 111.706 14.8331V46.9131C111.706 47.0691 111.577 47.2011 111.412 47.2012H7.12598L0.362305 39.212C0.318393 39.1601 0.293968 39.0951 0.293945 39.0284V1.50201C0.294033 1.34604 0.422968 1.21408 0.587891 1.21393Z" stroke="url(#paint0_linear_2732_359)" stroke-width="0.5888"/>
<g filter="url(#filter1_i_2732_359)">
<path d="M111.932 12.8733V8.22437H108.196L111.932 12.8733Z" fill="#FF3939"/>
</g>
<g filter="url(#filter2_i_2732_359)">
<path d="M9.27448e-05 43.7003L9.27448e-05 48H3.82324L9.27448e-05 43.7003Z" fill="#FF3939"/>
</g>
</g>
<defs>
<filter id="filter0_i_2732_359" x="0" y="-3.08002" width="112" height="50.5753" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="-4"/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.365101 0 0 0 0 0 0 0 0 0 0 0 0 0 0.160784 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_2732_359"/>
</filter>
<filter id="filter1_i_2732_359" x="108.196" y="8.22437" width="3.73584" height="4.64886" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.745098 0 0 0 0 1 0 0 0 0.188235 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_2732_359"/>
</filter>
<filter id="filter2_i_2732_359" x="0" y="43.7003" width="3.82324" height="4.29968" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset/>
<feGaussianBlur stdDeviation="3"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0.745098 0 0 0 0 1 0 0 0 0.188235 0"/>
<feBlend mode="normal" in2="shape" result="effect1_innerShadow_2732_359"/>
</filter>
<linearGradient id="paint0_linear_2732_359" x1="-56" y1="0.919983" x2="-56" y2="47.4952" gradientUnits="userSpaceOnUse">
<stop stop-color="#FF3939" stop-opacity="0.01"/>
<stop offset="1" stop-color="#FF3939"/>
</linearGradient>
<clipPath id="clip0_2732_359">
<rect width="112" height="48" fill="white"/>
</clipPath>
</defs>
</svg>
