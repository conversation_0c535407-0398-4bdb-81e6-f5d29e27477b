import { createGlobalState, useStorage } from '@vueuse/core'
import type { RecentContact } from './types'
import { getGroupContactType, getSingleContactType } from './helper'
import { isCommonGroupContact, isCommonSingleContact } from './commonContact'
import dayjs from 'dayjs'

// 最大长度为20, recentContacts全局唯一
export const useRecentContact = createGlobalState(() => {
  const recentContacts = useStorage('recent-contacts', [] as RecentContact[], localStorage, { mergeDefaults: true })
  // 只保留当天内的记录
  recentContacts.value = recentContacts.value.filter(item => dayjs().diff(dayjs(item.soundTime), 'day') <= 1)
  function addRecentContact(contact: RecentContact) {
    if (recentContacts.value.length >= 20) {
      recentContacts.value.shift()
    }
    recentContacts.value.push(contact)
  }

  function updateRecentContact(contact: RecentContact) {
    const index = recentContacts.value.findIndex(
      c => c.srcRid === contact.srcRid && c.targetDmrIDHex === contact.targetDmrIDHex && c.soundTime === contact.soundTime
    )
    if (index !== -1) {
      recentContacts.value[index] = {
        ...recentContacts.value[index],
        ...contact,
      }
    }
  }

  function removeRecentContacts(contact: RecentContact) {
    if (recentContacts.value.length === 0) return
    const index = recentContacts.value.findIndex(
      c => c.srcRid === contact.srcRid && c.targetDmrIDHex === contact.targetDmrIDHex && c.soundTime === contact.soundTime
    )
    if (index === -1) {
      return
    }
    recentContacts.value.splice(index, 1)
  }

  function clearRecentContacts() {
    recentContacts.value = []
  }

  return {
    recentContacts,
    addRecentContact,
    updateRecentContact,
    removeRecentContacts,
    clearRecentContacts,
  }
})

export function addRecentContactFromHistory(dbSoundHistory) {
  const src = bfglob.gdevices.get(dbSoundHistory.deviceId)
  if (!src) {
    return
  }

  let targetIsDevice = true
  let target = bfglob.gdevices.getDataByIndex(dbSoundHistory.target)
  if (!target) {
    target = bfglob.gorgData.getDataByIndex(dbSoundHistory.target)
    if (!target) {
      return
    }
    targetIsDevice = false
  }

  const recentContact: RecentContact = {
    keyId: `${dbSoundHistory.deviceId}_${dbSoundHistory.target}_${dbSoundHistory.soundTime}`,
    srcRid: src.rid as string,
    srcName: src.selfId as string,
    srcType: getSingleContactType(src.deviceType),
    targetDmrIDHex: dbSoundHistory.target,
    targetRid: target.rid as string,
    targetName: targetIsDevice ? target.selfId : target.orgShortName,
    targetType: targetIsDevice ? getSingleContactType(target.deviceType) : getGroupContactType(target.dmrID, target.orgIsVirtual),
    isCommonContact: targetIsDevice ? isCommonSingleContact(target.dmrID) : isCommonGroupContact(target.dmrID),
    soundTime: dbSoundHistory.soundTime,
    soundLen: dbSoundHistory.soundLen,
  }
  useRecentContact().addRecentContact(recentContact)
  bfglob.console.log('add recent contact from history', useRecentContact().recentContacts)
}
