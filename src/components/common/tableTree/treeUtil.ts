import bfutil, { formatDmrIdLabel, getDynamicGroupOrgType, DefOrgRid } from '@/utils/bfutil'
import dayjs from 'dayjs'
import bfTime from '@/utils/time'
import { TreeNodeType, TreeNodeData } from './types'
import { fullCallDmrIdStr, getFullCallData } from './vxeTree'

// 获取节点对应的原始数据
export const getNodeOriginData = (row: TreeNodeData) => {
  if (row.rid === fullCallDmrIdStr) {
    return getFullCallData()
  }
  if (row.nodeType === TreeNodeType.Org) {
    return bfglob.gorgData.get(row.rid)
  } else {
    return bfglob.gdevices.get(row.rid)
  }
}

const dynamicGroupOrgType = getDynamicGroupOrgType()
const dynamicGroupStatusClasses = {
  // 0: 'init',
  1: 'valid',
  10: 'invalid', // 3: 'deldete',
}

/**
 * @param orgItem DbOrg
 */
export function getOrgTaskGroupStatus<T extends { orgIsVirtual?: number; dynamicGroupState?: number }>(orgItem: T | null): string {
  if (!orgItem) {
    return ''
  }

  // 不是动态组，没有状态
  if (!dynamicGroupOrgType.includes(orgItem.orgIsVirtual)) {
    return ''
  }

  // dynamic_group_state  1:正常 10:失效 / 删除中
  return dynamicGroupStatusClasses[orgItem.dynamicGroupState] || dynamicGroupStatusClasses[10]
}

const orgNodeIconFonts = {
  1: 'bfdx-xunizuzhiguanli',
  100: 'bfdx-a-zuzhiguanli3',
  102: 'bfdx-a-zuzhiguanli3',
  101: 'bfdx-a-zuzhiguanli2',
}

/**
 * @param orgItem DbOrg
 */
export function getOrgNodeIcon<T extends { orgIsVirtual?: number }>(orgItem: T | null): string | false {
  if (!orgItem) {
    return false
  }
  // 1：虚拟组，2：真实组，101：任务组，100：临时组
  return orgNodeIconFonts[orgItem.orgIsVirtual] || false
}

// 获取组织节点的 Tooltip
export function getOrgNodeTooltip<T extends { dmrId: string }>(orgItem: T): string {
  const hex = orgItem.dmrId
  const masked = (Number(`0x${hex}`) & 0x7ffff) >>> 0
  return `${formatDmrIdLabel(hex)} (${masked})`
}

export function getDeviceNodeTooltip<T extends { dmrId: string; deviceUser: string; lastRfidPerson: string }>(device: T): string {
  const hex = device.dmrId
  const masked = (Number(`0x${hex}`) & 0x7ffff) >>> 0
  const dmrIdLabel = `${formatDmrIdLabel(device.dmrId)} (${masked})`

  let userRid = device.deviceUser
  // 如果有终端读卡，则使用最后读卡的人员
  if (device.lastRfidPerson && device.lastRfidPerson !== DefOrgRid) {
    userRid = device.lastRfidPerson
  }
  const userData = bfglob.guserData.get(userRid)
  const userPhone = userData?.userPhone ?? ''
  if (userPhone) {
    return `${dmrIdLabel} ${userPhone}`
  }

  return dmrIdLabel
}

/** org终端数量相关函数 ------ 开始 */

export function clearTreeOrgDeviceCount() {
  const g_org = bfglob.gorgData.getAll()
  for (const i in g_org) {
    g_org[i].ownDeviceCount = 0
    g_org[i].sumDeviceCount = -1
  }
}

/**
 * sumDeviceCount:org本身的终端数量以及子org的终端数量总和
 * ownDeviceCount:org本身的终端数量
 * @param org_item dbOrg
 * @returns number sumDeviceCount
 */
export function calculateTreeOrgSubOrgDeviceCount(org_item) {
  if (org_item.orgIsVirtual === 1) {
    return org_item.ownDeviceCount
  }
  const sub_org = []
  const g_org = bfglob.gorgData.getAll()
  for (const i in g_org) {
    if (g_org[i].parentOrgId === org_item.rid) {
      if (org_item.rid !== g_org[i].rid) {
        sub_org.push(g_org[i])
      }
    }
  }

  let result = org_item.ownDeviceCount
  for (const j in sub_org) {
    if (sub_org[j].orgIsVirtual === 1) {
      continue
    }

    if (sub_org[j].sumDeviceCount >= 0) {
      result += sub_org[j].sumDeviceCount
    } else {
      result += calculateTreeOrgSubOrgDeviceCount(sub_org[j])
    }
  }

  return result
}

export function updateTreeOrgDeviceCount() {
  clearTreeOrgDeviceCount()
  const g_org = bfglob.gorgData.getAll()
  const g_device = bfglob.gdevices.getAll()
  for (const i in g_org) {
    for (const j in g_device) {
      if (g_device[j].orgId === g_org[i].rid) {
        g_org[i].ownDeviceCount++
      }
      if (g_device[j].virOrgs.includes(g_org[i].rid)) {
        g_org[i].ownDeviceCount++
      }
    }
  }

  // sum all the device of the org
  for (const k in g_org) {
    if (g_org[k].sumDeviceCount >= 0) {
      continue
    }
    g_org[k].sumDeviceCount = calculateTreeOrgSubOrgDeviceCount(g_org[k])
  }
}

/** org终端数量相关函数 ------ 结束 */

// 终端节点操作方法
// 判断对讲机最后数据时间，如果在半小时内则视为在线
export function checkedDeviceIsOnline(device) {
  let lastDataTime = device?.lastDataTime ?? ''
  lastDataTime = bfTime.utcTimeToLocalTime(lastDataTime)
  const time = bfglob.sysConfig.maxAgeOfOnline || 30
  // 在线则返回true
  return dayjs(lastDataTime).add(time, 'm').isAfter(dayjs())
}

// 获取对讲机状态 className
export function getDeviceStatusClassName(device) {
  if (!device) return 'device_status_none'
  // 最后数据时间在半小时外，视为关机，不显示状态
  if (!checkedDeviceIsOnline(device)) {
    return 'device_status_none'
  }

  // 先判断是否关机，如果最后数据时间、最后开机时间都在最后关机是前，则视为关机状态
  const lastPoweroffTime = new Date(device.lastPoweroffTime).getTime()
  const lastPoweronTime = new Date(device.lastPoweronTime).getTime()
  const lastDataTime = new Date(device.lastDataTime ?? '2000-01-01').getTime()
  if (lastPoweroffTime >= lastDataTime && lastPoweroffTime >= lastPoweronTime) {
    // 关机超过10分钟，显示浅灰色
    if (new Date(bfTime.nowUtcTime()).getTime() - lastPoweroffTime > 10 * 60 * 1000) {
      return 'device_status_light_gray'
    }
    return 'device_status_gray'
  }

  // 当前为开机状态，需要处理在线的各种状态
  // speaking
  if (bfutil.deviceIsInCalling(device)) {
    return 'device_status_icon_th'
  }

  if (typeof device.msStatusBin === 'undefined') {
    device.msStatusBin = [0, 0, 0, 0, 0, 0]
  }
  // emergency
  if (device.msStatusBin[3] & 0x01) {
    if (device.av === 1 && bfutil.checkedDeviceLastLonValid(device)) {
      return 'device_status_emergency_green'
    }
    return 'device_status_emergency_yellow'
  }

  // other alarm
  if (device.msStatusBin[3] & 0xfe) {
    if (device.av === 1 && bfutil.checkedDeviceLastLonValid(device)) {
      return 'device_status_green device_status_other_red'
    }
    return 'device_status_yellow device_status_other_red'
  }

  // locate
  if (device.av === 1 && bfutil.checkedDeviceLastLonValid(device)) {
    return 'device_status_green'
  }

  // gps not valid,but online
  return 'device_status_yellow'
}

export function deviceIsOnlineFromClassName(cls) {
  // 手台状态classname包含'none','gray'，则为不在线，返回false
  return !(cls.includes('none') || cls.includes('gray'))
}

export function deviceIsLocateFromClassName(cls) {
  // 手台状态classname包含'green'，则为已定位，返回true
  return cls.includes('green')
}

export function deviceIsOnline(device) {
  const cls = getDeviceStatusClassName(device)
  return deviceIsOnlineFromClassName(cls)
}

export function getDeviceChannel(device) {
  if (!checkedDeviceIsOnline(device)) {
    return ''
  }

  if (device.msStatusBin.length < 6) {
    device.msStatus = '000000000000'
    device.msStatusBin = [0, 0, 0, 0, 0, 0]
    return ''
  }
  let channel = device.msStatusBin[4] & 0x1f
  channel += (device.msStatusBin[5] & 0x7f) << 5
  if (!channel) {
    return ''
  }

  return channel + ''
}

export function getDeviceUserName(device) {
  if (device.lastRfidPerson === DefOrgRid || !device.lastRfidPerson) {
    return device.deviceUserName ?? ''
  } else {
    return device.userName ?? ''
  }
}
