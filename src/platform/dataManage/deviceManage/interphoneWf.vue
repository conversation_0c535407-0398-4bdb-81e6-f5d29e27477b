<template>
  <el-main :class="localeClass">
    <el-progress v-if="progress.action" :show-text="false" :stroke-width="2" :percentage="100" color="#c6e2ff" class="wf-progress" />

    <div class="h-auto flex justify-center">
      <el-form :label-width="labelWidth" class="w-2xl grid grid-cols-2 gap-2 interphone-model-form">
        <el-form-item :label="$t('dialog.model')">
          <el-select v-model="model" :placeholder="$t('dialog.select')" filterable :no-match-text="$t('dialog.noMatchText')" @change="loadComponent">
            <el-option v-for="(item, i) in modelInfoList" :key="i" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('dialog.programmingPwd')">
          <el-input v-model="ioPassword" type="password" autocomplete="new-password" :maxlength="8" />
        </el-form-item>
      </el-form>
    </div>

    <component
      :is="viewComponent"
      v-if="viewComponent"
      :QWebServer="QWebServer"
      :noDevice="noDevice"
      :ioPassword="ioPassword"
      :isFullscreen="fullscreen"
      :expectedModel="model"
      class="flex-auto flex flex-col justify-stretch items-center write-freq-component"
      @progress-action="progressAction"
    />
  </el-main>
</template>

<script>
  import { markRaw } from 'vue'
  import bfNotify from '@/utils/notify'
  import qWebChannelObj from '@/utils/qWebChannelObj'
  import * as deviceModels from '@/writingFrequency/interphone/models'
  import { getModelName } from '@/writingFrequency/modelInfo'
  import { getDeviceModelList, getDeviceModelName } from '@/writingFrequency/customModelConfig'
  import VueMixin from '@/utils/vueMixin'

  const currentModel = deviceModels.TD800SDCModel
  // TD081100与TD081000为同一个机型，因为要区分不同硬件芯片新加的机型
  const customModelList = getDeviceModelList()
  const modelList =
    customModelList.length > 0
      ? customModelList
      : [
          deviceModels.TD510SDCModel,
          deviceModels.TD510SVTModel,
          deviceModels.TD511SDCModel,
          deviceModels.TD511FRModel,
          deviceModels.TD511SVTModel,
          deviceModels.TD800SDCModel,
          deviceModels.TD818SDCModel,
          deviceModels.TD818SVTModel,
          deviceModels.TD880SDCModel,
          deviceModels.TD880SVTModel,
          deviceModels.TD910SDCModel,
          deviceModels.TD910PSDCModel,
          deviceModels.TD920Model,
          deviceModels.TD930SDCModel,
          deviceModels.TD930SDCR7F,
          deviceModels.TD930SVTModel,
          deviceModels.TD930SVTR7F,
          deviceModels.TM825SDCModel,
          deviceModels.TM825FRModel,
          deviceModels.TM8250SDCR7F,
          deviceModels.BP750SDC,
          deviceModels.BP750SVT,
          deviceModels.BP610Model,
          deviceModels.BP620Model,
          deviceModels.BP660Model,
          deviceModels.BP860SDCModel,
          deviceModels.BP860SVTModel,
          deviceModels.TM8250SVTR7F,
        ]
  const modelNames = {
    [deviceModels.TD510SDCModel]: 'TD510SDC',
    [deviceModels.TD510SVTModel]: 'TD510SVT',
    [deviceModels.TD511SDCModel]: 'TD511SDC',
    [deviceModels.TD511SVTModel]: 'TD511SVT',
    [deviceModels.TD511FRModel]: 'TD511SDC_FR',
    [deviceModels.TD800SDCModel]: 'TD800SDC',
    [deviceModels.TD818SDCModel]: 'TD818SDC',
    [deviceModels.TD818SVTModel]: 'TD818SVT',
    // BF-TD880(SDC)/BF-TD880(SVT)，实际就是BF-TD511(SDC)/BF-TD511(SVT)，只是机型码不一样
    [deviceModels.TD880SDCModel]: 'TD880SDC',
    [deviceModels.TD880SVTModel]: 'TD880SVT',
    [deviceModels.TD910SDCModel]: 'TD910SDC',
    [deviceModels.TD910PSDCModel]: 'TD910PSDC',
    [deviceModels.TD920Model]: 'TD920',
    [deviceModels.TD930SDCModel]: 'TD930SDC',
    [deviceModels.TD930SDCR7F]: 'TD930SDC_R7F',
    [deviceModels.TD930SVTModel]: 'TD930SVT',
    [deviceModels.TD930SVTR7F]: 'TD930SVT_R7F',
    [deviceModels.TM825SDCModel]: 'TM8250SDC',
    [deviceModels.TM8250SDCR7F]: 'TM8250SDC_R7F',
    [deviceModels.TM825FRModel]: 'TM8250SDC_FR',
    [deviceModels.BP750SDC]: 'BP750SDC',
    [deviceModels.BP750SVT]: 'BP750SVT',
    [deviceModels.BP610Model]: 'BP610',
    [deviceModels.BP620Model]: 'BP620',
    [deviceModels.BP660Model]: 'BP660',
    [deviceModels.BP860SDCModel]: 'BP860SDC',
    [deviceModels.BP860SVTModel]: 'BP860SVT',
    [deviceModels.TM8250SVTR7F]: 'TM8250SVT_R7F',
  }

  export default {
    name: 'InterphoneWriteFrequency',
    mixins: [VueMixin],
    data() {
      return {
        qWebChannelObj,

        selectedDeviceDmrId: '',
        viewComponent: undefined,
        isInit: false,
        noDevice: true,
        model: modelList.includes(currentModel) ? currentModel : (modelList[0] ?? ''),
        ioPassword: '',
        progress: {
          action: false,
        },
      }
    },
    methods: {
      redrawTree() {
        bfglob.emit('wf:redrawTree')
      },
      progressAction(action) {
        this.progress.action = action
      },
      loadViewComponent(src) {
        if (typeof src !== 'string' || !src) {
          return Promise.resolve()
        }

        return import(`@/platform/dataManage/deviceManage/views/${src}.vue`)
          .then(res => markRaw(res.default || res))
          .catch(err => {
            bfglob.console.warn('loadViewComponent error:', err)
            return Promise.resolve()
          })
      },

      // 程序连接TC918服务器初始化
      hasQWebServer() {
        if (!this.QWebServer) {
          bfglob.console.warn('can not found usb manager')
          return false
        }

        return true
      },
      showDeviceInsetOrRemovedMessage() {
        let msg = this.$t('msgbox.usbDeviceInsertSuccess')
        let type = 'success'

        if (this.noDevice) {
          msg = this.$t('msgbox.usbDeviceHasBeenOut')
          type = 'warning'
        }
        bfNotify.messageBox(msg, type)
      },
      listenServerSignal() {
        if (!this.hasQWebServer()) {
          return
        }

        // 为避免服务程序中的信号名变更导致出错
        try {
          this.QWebServer.sg_device_usb_new.connect(() => {
            this.noDevice = false
            this.showDeviceInsetOrRemovedMessage()
          })
          this.QWebServer.sg_device_usb_removed.connect(() => {
            this.noDevice = true
            this.showDeviceInsetOrRemovedMessage()
          })
        } catch (e) {
          bfglob.console.warn('listenServerSignal error:', e)
        }
      },
      findUsbDevice() {
        if (!this.hasQWebServer()) {
          return
        }

        try {
          this.QWebServer.sl_is_device_usb_exists(is_exist => {
            this.noDevice = !is_exist
            this.showDeviceInsetOrRemovedMessage()
          })
        } catch (e) {
          bfglob.console.warn('sl_is_device_usb_exists error:', e)
        }
      },
      afterServerConnect() {
        if (this.isInit) {
          return
        }

        this.isInit = true
        this.listenServerSignal()
        this.findUsbDevice()
      },
      // 处理以数字开头的机型组件名称
      wrapModelName(name) {
        return modelNames[name] || name
      },
      async loadComponent(model) {
        this.ioPassword = ''
        const component = await this.loadViewComponent(this.wrapModelName(model))
        if (!component) {
          return
        }
        this.viewComponent = component
        this.redrawTree()
      },
    },
    computed: {
      QWebServer() {
        return this.qWebChannelObj && this.qWebChannelObj.server
      },
      modelInfoList() {
        return modelList
          .map(model => {
            return {
              label: getDeviceModelName(model) || getModelName(model),
              value: model,
            }
          })
          .sort((a, b) => a.label.localeCompare(b.label))
      },
      labelWidth() {
        // return this.isFR || this.isEN ? '100px' : '80px'
        return this.isFR || this.isEN ? '180px' : '120px'
      },
      localeClass() {
        return ['locale-' + this.$i18n.locale]
      },
    },
    watch: {
      QWebServer(val) {
        if (!val) {
          return
        }
        this.afterServerConnect()
      },
    },
    beforeMount() {
      this.loadComponent(this.model)
      if (this.QWebServer) {
        this.afterServerConnect()
      }
    },
    activated() {
      if (!qWebChannelObj.server) {
        qWebChannelObj.initServer()
      }
    },
  }
</script>

<style lang="scss">
  @use '@/css/interphoneWf/list-config.scss' as *;

  .el-main.page-interphone-write-freq {
    .write-freq-component {
      height: calc(100% - 40px);

      & > .el-tabs.writer-frequency-tabs {
        border: 1px solid var(--el-border-color);
        border-radius: var(--radius-sm);
        width: 100%;
        flex: auto;
        margin-top: calc(var(--spacing) * 2);

        &.el-tabs--left > .el-tabs__header .el-tabs__nav.is-left {
          min-width: 140px;

          .el-tabs__item {
            height: 30px;
            line-height: 30px;
          }
        }
      }
    }

    .el-progress.wf-progress {
      margin: -10px -15px 8px;

      @keyframes wf-progress-active {
        0% {
          width: 0;
        }

        100% {
          width: 100%;
        }
      }

      .el-progress-bar__inner {
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: #409eff;
          border-radius: 10px;
          animation: wf-progress-active 2s ease-in-out infinite;
        }
      }
    }

    .el-form {
      overflow-x: hidden;

      //.el-form-item {
      //  margin-bottom: 12px;
      //}
    }

    //   span.fancytree-custom-icon.mdi-alert-circle-outline {
    //     color: #E6A23C;
    //   }

    //   .encrypt-settings-box.settings-box {
    //     .encrypt-list-header {
    //       margin-bottom: 8px;
    //     }
    //   }
  }
</style>
