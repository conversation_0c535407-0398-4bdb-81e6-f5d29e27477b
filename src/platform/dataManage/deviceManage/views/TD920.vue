<template>
  <div class="writer-frequency-wrap">
    <selectDevice v-model="selectedDeviceDmrId" :disabled="isReading || isWriting" />
    <section class="TD910-layout">
      <TableTree
        :ref="menuTreeId"
        class="TD910-menu-tree"
        :treeId="menuTreeId"
        :filter="false"
        :contextmenuOption="menuTreeContextmenuOption"
        :option="menuTreeOpts"
        @loaded="treeLoaded"
      />
      <main class="TD910-content">
        <el-card v-if="showTD910DeviceInfo" shadow="never" class="write-freq-component deviceInfo-container">
          <deviceInfo ref="deviceWriteInfo" v-model="deviceWriteInfo" :model="deviceModel" :multipleFreqRange="2">
            <template #features>
              <el-row :gutter="20" class="no-margin-x features">
                <el-col :xs="24" :sm="12">
                  <el-form-item>
                    <el-checkbox v-model="deviceWriteInfo.config.locate" disabled>
                      <span v-text="$t('writeFreq.locateFunc')" />
                    </el-checkbox>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12">
                  <el-form-item>
                    <el-checkbox v-model="deviceWriteInfo.config.recording" disabled>
                      <span v-text="$t('writeFreq.recordingFunc')" />
                    </el-checkbox>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12">
                  <el-form-item>
                    <el-checkbox v-model="deviceWriteInfo.config.upendAlarm" disabled>
                      <span v-text="$t('writeFreq.runBackwardAlarm')" />
                    </el-checkbox>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12">
                  <el-form-item>
                    <el-checkbox v-model="deviceWriteInfo.config.bluetooth" disabled>
                      <span v-text="$t('writeFreq.bluetoothFunc')" />
                    </el-checkbox>
                  </el-form-item>
                </el-col>

                <el-col :xs="24" :sm="12">
                  <el-form-item>
                    <el-checkbox v-model="deviceWriteInfo.config.tts" disabled>
                      <span>TTS</span>
                    </el-checkbox>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12">
                  <el-form-item>
                    <el-checkbox v-model="deviceWriteInfo.config.shake" disabled>
                      <span v-text="$t('writeFreq.shake')" />
                    </el-checkbox>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12">
                  <el-form-item>
                    <el-checkbox v-model="deviceWriteInfo.config.denoise" disabled>
                      <span v-text="$t('writeFreq.denoise')" />
                    </el-checkbox>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12">
                  <el-form-item>
                    <el-checkbox v-model="deviceWriteInfo.config.sdc" disabled>
                      <span>SDC</span>
                    </el-checkbox>
                  </el-form-item>
                </el-col>
              </el-row>
            </template>
            <template #resourceVersion>
              <el-row :gutter="20" class="no-margin-x">
                <el-col>
                  <el-form-item :label="$t('writeFreq.resourceVersion')">
                    <el-input type="textarea" :autosize="{ minRows: 3, maxRows: 6 }" :value="resourceVersion.verInfo" disabled />
                  </el-form-item>
                </el-col>
              </el-row>
            </template>
          </deviceInfo>
        </el-card>
        <el-card v-if="showGeneralSettings" shadow="never" class="write-freq-component general-settings-container">
          <el-form
            ref="generalSettings"
            class="general-settings-form"
            :model="generalSettings"
            label-width="100px"
            label-position="top"
            :rules="generalSettingsRules"
          >
            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.terminalName')" prop="deviceName">
                  <el-input v-model="generalSettings.deviceName" :maxlength="16" disabled />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item label="DMRID">
                  <el-input :value="dmrIdLabel" disabled />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.repeaterId')">
                  <el-input-number v-model="generalSettings.repeaterId" step-strictly :min="1" :max="16777215" :step="1" />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.programmingPwd')">
                  <el-input v-model="passwordInfo.md5Key" type="password" :maxlength="8" @input="fixMd5KeyValue" />
                </el-form-item>
              </el-col>
              <el-col v-if="!DeviceNoLocale" :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.languageType')">
                  <el-select v-model="generalSettings.locale" :placeholder="$t('dialog.select')" filterable :no-match-text="$t('dialog.noMatchText')">
                    <el-option v-for="(item, i) in localeList" :key="i" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.voiceLevel')" prop="soundCtrlLevel">
                  <bf-input-number
                    v-model="generalSettings.soundCtrlLevel"
                    step-strictly
                    :min="0"
                    :max="9"
                    :step="1"
                    :formatter="
                      v => {
                        return v === 0 ? $t('writeFreq.off') : v
                      }
                    "
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.voiceDelay')" prop="soundCtrlDelay">
                  <el-input-number v-model="generalSettings.soundCtrlDelay" step-strictly :min="500" :max="10000" :step="500" />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.sendPreambleDuration')" prop="sendLeadCodeTime">
                  <el-input-number v-model="generalSettings.sendLeadCodeTime" step-strictly :min="0" :max="8640" :step="240" />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.offNetworkGroupCallHangTime')" prop="offlineGroupCallHungTime">
                  <el-input-number v-model="generalSettings.offlineGroupCallHungTime" step-strictly :min="0" :max="7000" :step="500" />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.offNetworkSingleCallHangTime')" prop="offlineSingleCallHungTime">
                  <el-input-number v-model="generalSettings.offlineSingleCallHungTime" step-strictly :min="0" :max="7000" :step="500" />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.powerSavingMode')" prop="savePowerMode">
                  <el-select v-model="generalSettings.savePowerMode" :placeholder="$t('dialog.select')" filterable :no-match-text="$t('dialog.noMatchText')">
                    <el-option v-for="(item, i) in savePowerModeList" :key="i" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.savePowerDelayTime')" prop="savePowerDelayTime">
                  <el-input-number v-model="generalSettings.savePowerDelayTime" :min="5" :max="60" :step="1" />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="generalSettings.LEDConfig.disabledAllLED">
                    <span v-text="$t('dialog.disabledAllLed')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="generalSettings.baseSettings.rejectUnfamiliarCall">
                    <span v-text="$t('dialog.rejectingStrangeCalls')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="generalSettings.baseSettings.allowErasing">
                    <span v-text="$t('writeFreq.allowErasingDevice')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="generalSettings.baseSettings.pttAlone">
                    <span v-text="$t('writeFreq.pttAlone')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="generalSettings.baseSettings.allowedSelfDestruct">
                    <span v-text="$t('writeFreq.allowedSelfDestruct')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 声音提示 -->
            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('writeFreq.voicePrompt')" />
              </el-divider>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="generalSettings.soundSettings.muteAll">
                    <span v-text="$t('dialog.muteAll')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="generalSettings.soundSettings.voiceNotice" :disabled="muteAll">
                    <span v-text="$t('dialog.voiceIndication')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="generalSettings.soundSettings.channelFreeNotice" :disabled="muteAll">
                    <span v-text="$t('dialog.channelIdleIndication')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.callPermissionIndication')">
                  <el-select
                    v-model="generalSettings.soundSettings.allowCallInstruction"
                    :disabled="muteAll"
                    :placeholder="$t('dialog.select')"
                    filterable
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option v-for="(item, i) in allowCallInstructionList" :key="i" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.receiveLowPowerPromptInterval')" prop="powerInfoAlert">
                  <el-input-number v-model="generalSettings.powerInfoAlert" step-strictly :min="0" :max="635" :step="5" />
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 振动模式 -->
            <el-row v-if="deviceWriteInfo.config.shake" :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span>{{ $t('writeFreq.shakeMode') }}</span>
              </el-divider>
              <el-col :xs="24" :sm="12">
                <el-form-item prop="shakeSettings.shakeEnable">
                  <el-checkbox v-model="generalSettings.shakeSettings.shakeEnable">
                    <span>{{ $t('writeFreq.allowShake') }}</span>
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item prop="shakeSettings.singleCallShake">
                  <el-checkbox v-model="generalSettings.shakeSettings.singleCallShake" :disabled="!generalSettings.shakeSettings.shakeEnable">
                    <span>{{ $t('writeFreq.singleCallVibration') }}</span>
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item prop="shakeSettings.singleCallShake">
                  <el-checkbox v-model="generalSettings.shakeSettings.messageShake" :disabled="!generalSettings.shakeSettings.shakeEnable">
                    <span>{{ $t('writeFreq.messageVibration') }}</span>
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item prop="shakeSettings.singleCallShake">
                  <el-checkbox v-model="generalSettings.shakeSettings.callNoticeShake" :disabled="!generalSettings.shakeSettings.shakeEnable">
                    <span>{{ $t('writeFreq.callToneVibration') }}</span>
                  </el-checkbox>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 隐蔽模式 -->
            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span>{{ $t('writeFreq.stealthMode') }}</span>
              </el-divider>
              <el-col :xs="24" :sm="12">
                <el-form-item prop="stealthSettings.stealthModeEnable">
                  <el-checkbox v-model="generalSettings.stealthSettings.stealthModeEnable">
                    <span>{{ $t('writeFreq.shiNeng') }}</span>
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item prop="stealthSettings.stealthModeHeadsetMute">
                  <el-checkbox v-model="generalSettings.stealthSettings.stealthModeHeadsetMute">
                    <span>{{ $t('writeFreq.stealthModeHeadsetMute') }}</span>
                  </el-checkbox>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 卫星定位 -->
            <el-row v-if="deviceWriteInfo.config.locate" :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span>{{ $t('writeFreq.gpsLocate') }}</span>
              </el-divider>
              <el-col :xs="24" :sm="12">
                <el-form-item prop="gpsSettings.gpsEnable">
                  <el-checkbox v-model="generalSettings.gpsSettings.gpsEnable">
                    <span>{{ $t('writeFreq.gpsLocate') }}</span>
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item prop="gpsSettings.beiDouEnable">
                  <el-checkbox v-model="generalSettings.gpsSettings.beiDouEnable">
                    <span>{{ $t('writeFreq.beidou') }}</span>
                  </el-checkbox>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 密码 -->
            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('loginDlg.password')" />
              </el-divider>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.powerOnPwd')">
                  <el-input
                    v-model="menuSettings.powerOnPwd"
                    type="password"
                    show-password
                    :maxlength="6"
                    :minlength="6"
                    @input="fixPowerOnValue"
                    @blur="fixPowerValueOnBlur"
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.channelConfigPassword')">
                  <el-select
                    v-model="chConfigPwdMode"
                    :placeholder="$t('dialog.select')"
                    filterable
                    :no-match-text="$t('dialog.noMatchText')"
                    @change="val => passwordModeChange(menuSettings, 'chConfigPwd', val)"
                  >
                    <el-option v-for="(item, i) in passwordModeList" :key="i" :label="item.label" :value="item.value" />
                  </el-select>
                  <el-input
                    v-model="menuSettings.chConfigPwd"
                    type="password"
                    :disabled="chConfigPwdMode !== 1"
                    show-password
                    :maxlength="6"
                    :minlength="6"
                    @input="fixChConfigPwd"
                  />
                </el-form-item>
              </el-col>
              <el-col v-if="deviceWriteInfo.config.recording" :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.uDiskModePassword')">
                  <el-select
                    v-model="usbFlashDiskPasswordMode"
                    :placeholder="$t('dialog.select')"
                    filterable
                    :no-match-text="$t('dialog.noMatchText')"
                    @change="val => passwordModeChange(generalSettings, 'usbFlashDiskPassword', val)"
                  >
                    <el-option v-for="(item, i) in passwordModeList" :key="i" :label="item.label" :value="item.value" />
                  </el-select>
                  <el-input
                    v-model="generalSettings.usbFlashDiskPassword"
                    type="password"
                    :disabled="usbFlashDiskPasswordMode !== 1"
                    show-password
                    :maxlength="6"
                    :minlength="6"
                    @input="fixUsbFlashDiskPassword"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 时间 -->
            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('dialog.timeSetting')" />
              </el-divider>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="generalSettings.syncTime" @change="syncTimeChange">
                    <span v-text="$t('writeFreq.synchronisedTime')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.timeZoneHours')">
                  <el-input-number v-model="generalSettings.timeZoneHour" :min="-12" :max="12" :step="1" :disabled="syncTime" />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.timeZoneMinutes')">
                  <el-input-number v-model="generalSettings.timeZoneMinute" :min="0" :max="59" :step="1" :disabled="syncTime" />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.dateTime.year')">
                  <el-input-number v-model="generalSettings.year" :min="1970" :max="0xffff" :step="1" :disabled="syncTime" />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.dateTime.month')">
                  <el-input-number v-model="generalSettings.month" :min="1" :max="12" :step="1" :disabled="syncTime" />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.dateTime.date')">
                  <el-input-number v-model="generalSettings.day" :min="1" :max="maxDate" :step="1" :disabled="syncTime" />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.dateTime.hours')">
                  <el-input-number v-model="generalSettings.hour" :min="0" :max="23" :step="1" :disabled="syncTime" />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.dateTime.minutes')">
                  <el-input-number v-model="minute" :min="0" :max="59" :step="1" :disabled="syncTime" />
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 录音 -->
            <el-row v-if="deviceWriteInfo.config.recording" :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('dialog.recording')" />
              </el-divider>
              <el-col :xs="24" :sm="12">
                <el-form-item prop="recordSettings.enable">
                  <el-checkbox v-model="generalSettings.recordSettings.enable">
                    <span v-text="$t('dialog.recordEnable')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.recordCompRatio')" prop="recordSettings.compressionRatio">
                  <el-select
                    v-model="generalSettings.recordSettings.compressionRatio"
                    :disabled="!generalSettings.recordSettings.enable"
                    :placeholder="$t('dialog.select')"
                    filterable
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option v-for="(item, i) in recordCompressionRatioList" :key="i" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 功率自动转换 -->
            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('writeFreq.powerAutoConversion')" />
              </el-divider>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="generalSettings.powerTransfer.enable">
                    <span v-text="$t('dialog.enable')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.switchHighPowerThreshold')">
                  <el-input-number v-model="generalSettings.highPowerLimit" step-strictly :min="-115" :max="-75" :step="1" :disabled="powerTransferEnable" />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.switchMediumPowerThreshold')">
                  <el-input-number v-model="generalSettings.middlePowerLimit" step-strictly :min="-105" :max="-70" :step="1" :disabled="powerTransferEnable" />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.switchLowPowerThreshold')">
                  <el-input-number v-model="generalSettings.lowPowerLimit" step-strictly :min="-95" :max="-65" :step="1" :disabled="powerTransferEnable" />
                </el-form-item>
              </el-col>
            </el-row>

            <!-- TTS -->
            <el-row v-if="deviceWriteInfo.config.tts" :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span>TTS</span>
              </el-divider>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.ttsSpeed')">
                  <el-input-number v-model="generalSettings.ttsSpeed" step-strictly :min="0" :max="10" :step="1" />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.ttsTune')">
                  <el-input-number v-model="generalSettings.ttsTune" step-strictly :min="0" :max="10" :step="1" />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.ttsVolume')">
                  <el-input-number v-model="generalSettings.ttsVolume" step-strictly :min="0" :max="10" :step="1" />
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 蓝牙 -->
            <el-row v-if="deviceWriteInfo.config.bluetooth" :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('writeFreq.bluetooth')" />
              </el-divider>
              <el-col :xs="24" :sm="12">
                <el-form-item prop="bluetoothSettings.enable">
                  <el-checkbox v-model="generalSettings.bluetoothSettings.enable">
                    <span v-text="$t('writeFreq.bluetoothSwitch')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item prop="bluetoothSettings.pttKeepEnable">
                  <el-checkbox v-model="generalSettings.bluetoothSettings.pttKeepEnable" :disabled="!generalSettings.bluetoothSettings.enable">
                    <span v-text="$t('writeFreq.bluetoothPTTKeep')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 降噪 -->
            <el-row v-if="deviceWriteInfo.config.denoise" :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span>{{ $t('writeFreq.denoise') }}</span>
              </el-divider>
              <el-col :xs="24" :sm="12">
                <el-form-item prop="denoiseSettings.enable">
                  <el-checkbox v-model="generalSettings.denoiseSettings.enable">
                    <span>{{ $t('writeFreq.denoiseEnable') }}</span>
                  </el-checkbox>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
        <el-card v-if="showButtonSettings" shadow="never" class="write-freq-component buttonDefine-container">
          <el-form ref="buttonDefine" class="buttonDefine-form" :model="buttonDefined" label-position="top">
            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.longPressDuration')" :label-width="buttonDefineLabelWidth">
                  <el-input-number v-model="buttonDefined.longPressTime" step-strictly :min="250" :max="3750" :step="250" />
                </el-form-item>
              </el-col>
              <el-col :xs="24">
                <el-table :data="buttonDefined.sideKey" :empty-text="$t('msgbox.emptyText')">
                  <el-table-column label="" min-width="65">
                    <template #default="scope">
                      <span v-text="getKeyName(scope.$index)" />
                    </template>
                  </el-table-column>
                  <el-table-column :label="$t('dialog.shortPress')" min-width="100">
                    <template #default="scope">
                      <el-form-item label-width="0">
                        <el-select
                          v-model="scope.row.short"
                          :placeholder="$t('dialog.select')"
                          filterable
                          :no-match-text="$t('dialog.noMatchText')"
                          @change="
                            () => {
                              syncLongPressDefine(scope.row)
                            }
                          "
                        >
                          <el-option v-for="(shortKey, i) in getSoftKeyFuncDefine(0)" :key="i" :label="shortKey.label" :value="shortKey.value" />
                        </el-select>
                      </el-form-item>
                    </template>
                  </el-table-column>
                  <el-table-column :label="$t('dialog.longPress')" min-width="85">
                    <template #default="scope">
                      <el-form-item label-width="0">
                        <el-select
                          v-model="scope.row.long"
                          :placeholder="$t('dialog.select')"
                          filterable
                          :no-match-text="$t('dialog.noMatchText')"
                          @change="
                            () => {
                              syncLongPressDefine(scope.row)
                            }
                          "
                        >
                          <el-option v-for="(shortKey, i) in getSoftKeyFuncDefine(1)" :key="i" :label="shortKey.label" :value="shortKey.value" />
                        </el-select>
                      </el-form-item>
                    </template>
                  </el-table-column>
                </el-table>
              </el-col>
              <el-col :xs="24">
                <el-divider>
                  <el-icon>
                    <CaretBottom />
                  </el-icon>
                  <span v-text="$t('writeFreq.presetChannel')" />
                </el-divider>
                <el-table :data="buttonDefined.defaultChannel" :empty-text="$t('msgbox.emptyText')">
                  <el-table-column label="" min-width="100">
                    <template #default="scope">
                      <span v-text="$t(`writeFreq.defaultChannel.ch${scope.$index + 1}`)" />
                    </template>
                  </el-table-column>
                  <el-table-column min-width="100" :label="$t('writeFreq.zones.root')">
                    <template #default="scope">
                      <el-form-item label-width="0">
                        <el-select
                          v-model="scope.row.rootId"
                          :placeholder="$t('dialog.select')"
                          filterable
                          :no-match-text="$t('dialog.noMatchText')"
                          @change="val => defaultChannelZoneRootIdChange(scope.row, val)"
                        >
                          <el-option v-for="item in buttonDefineZoneRootList" :key="item.label" :label="item.label" :value="item.value" />
                        </el-select>
                      </el-form-item>
                    </template>
                  </el-table-column>
                  <el-table-column min-width="100" :label="$t('writeFreq.zones.parent')">
                    <template #default="scope">
                      <el-form-item label-width="0">
                        <el-select
                          v-model="scope.row.parentId"
                          :placeholder="$t('dialog.select')"
                          filterable
                          :no-match-text="$t('dialog.noMatchText')"
                          @change="val => defaultChannelZoneParentIdChange(scope.row, val)"
                        >
                          <el-option v-for="item in buttonDefineZoneParentList[scope.row.rootId]" :key="item.label" :label="item.label" :value="item.value" />
                        </el-select>
                      </el-form-item>
                    </template>
                  </el-table-column>
                  <el-table-column min-width="100" :label="$t('writeFreq.zones.leaf')">
                    <template #default="scope">
                      <el-form-item label-width="0">
                        <el-select
                          v-model="scope.row.zoneId"
                          :placeholder="$t('dialog.select')"
                          filterable
                          :no-match-text="$t('dialog.noMatchText')"
                          @change="val => defaultChannelZoneIdChange(scope.row, val)"
                        >
                          <el-option v-for="item in buttonDefineZoneLeafList[scope.row.parentId]" :key="item.label" :label="item.label" :value="item.value" />
                        </el-select>
                      </el-form-item>
                    </template>
                  </el-table-column>
                  <el-table-column min-width="100" :label="$t('dialog.channel')">
                    <template #default="scope">
                      <el-form-item label-width="0">
                        <el-select v-model="scope.row.channelId" :placeholder="$t('dialog.select')" filterable :no-match-text="$t('dialog.noMatchText')">
                          <el-option v-for="item in buttonDefineChannelList[scope.row.zoneId]" :key="item.label" :label="item.label" :value="item.value" />
                        </el-select>
                      </el-form-item>
                    </template>
                  </el-table-column>
                </el-table>
              </el-col>
              <el-col :xs="24">
                <el-divider>
                  <el-icon>
                    <CaretBottom />
                  </el-icon>
                  <span v-text="$t('writeFreq.singleKeyFuncCall')" />
                </el-divider>
                <el-table :data="buttonDefined.singleKeyCall" :empty-text="$t('msgbox.emptyText')">
                  <el-table-column label="" type="index" />
                  <el-table-column :label="$t('dialog.callTarget')" min-width="100">
                    <template #default="scope">
                      <el-form-item label-width="0">
                        <el-select
                          v-model="scope.row.callId"
                          :placeholder="$t('dialog.select')"
                          filterable
                          :no-match-text="$t('dialog.noMatchText')"
                          @change="val => buttonDefinedCallIdChange(scope.row, val)"
                        >
                          <el-option v-for="item in buttonDefineAddressList" :key="item.label" :label="item.label" :value="item.value" />
                        </el-select>
                      </el-form-item>
                    </template>
                  </el-table-column>
                  <el-table-column :label="$t('dialog.callType')" min-width="85">
                    <template #default="scope">
                      <el-form-item v-if="scope.row.callId !== 0xffff" label-width="0">
                        <el-select
                          v-model="scope.row.callType"
                          :placeholder="$t('dialog.select')"
                          filterable
                          :no-match-text="$t('dialog.noMatchText')"
                          @change="val => buttonDefinedCallTypeChange(scope.row, val)"
                        >
                          <el-option
                            v-for="callType in getSoftKeyCallTypeList(scope.row)"
                            :key="callType.value"
                            :label="callType.label"
                            :value="callType.value"
                          />
                        </el-select>
                      </el-form-item>
                    </template>
                  </el-table-column>
                  <el-table-column :label="$t('writeFreq.sms')" min-width="85">
                    <template #default="scope">
                      <el-form-item v-if="!(scope.row.callId === 0xffff || scope.row.callType !== SoftKeyCallType.MSG)" label-width="0">
                        <el-select
                          v-model="scope.row.smsId"
                          :placeholder="$t('dialog.select')"
                          filterable
                          :no-match-text="$t('dialog.noMatchText')"
                          popper-class="sms-selection-container"
                        >
                          <el-option v-for="sms in smsContent" :key="sms.msgContent" :label="sms.msgContent" :value="sms.msgId" />
                        </el-select>
                      </el-form-item>
                    </template>
                  </el-table-column>
                </el-table>
              </el-col>
              <el-col :xs="24">
                <el-divider>
                  <el-icon>
                    <CaretBottom />
                  </el-icon>
                  <span>{{ $t('writeFreq.longPressNumCallTable') }}</span>
                </el-divider>
                <el-table :data="buttonDefined.longPressNumCallTable" :empty-text="$t('msgbox.emptyText')">
                  <el-table-column label="" min-width="20">
                    <template #default="scope">
                      <span>{{ scope.$index }}</span>
                    </template>
                  </el-table-column>
                  <el-table-column :label="$t('dialog.callTarget')" min-width="100">
                    <template #default="scope">
                      <el-form-item label-width="0">
                        <el-select
                          v-model="scope.row.callId"
                          :placeholder="$t('dialog.select')"
                          filterable
                          :no-match-text="$t('dialog.noMatchText')"
                          @change="val => buttonDefinedCallIdChange(scope.row, val)"
                        >
                          <el-option v-for="item in buttonDefineAddressList" :key="item.label" :label="item.label" :value="item.value" />
                        </el-select>
                      </el-form-item>
                    </template>
                  </el-table-column>
                  <el-table-column :label="$t('dialog.callType')" min-width="85">
                    <template #default="scope">
                      <el-form-item v-if="scope.row.callId !== 0xffff" label-width="0">
                        <el-select
                          v-model="scope.row.callType"
                          :placeholder="$t('dialog.select')"
                          filterable
                          :no-match-text="$t('dialog.noMatchText')"
                          @change="val => buttonDefinedCallTypeChange(scope.row, val)"
                        >
                          <el-option
                            v-for="callType in getSoftKeyCallTypeList(scope.row)"
                            :key="callType.value"
                            :label="callType.label"
                            :value="callType.value"
                          />
                        </el-select>
                      </el-form-item>
                    </template>
                  </el-table-column>
                  <el-table-column :label="$t('writeFreq.sms')" min-width="85">
                    <template #default="scope">
                      <el-form-item v-if="!(scope.row.callId === 0xffff || scope.row.callType !== SoftKeyCallType.MSG)" label-width="0">
                        <el-select
                          v-model="scope.row.smsId"
                          :placeholder="$t('dialog.select')"
                          filterable
                          :no-match-text="$t('dialog.noMatchText')"
                          popper-class="sms-selection-container"
                        >
                          <el-option v-for="sms in smsContent" :key="sms.msgContent" :label="sms.msgContent" :value="sms.msgId" />
                        </el-select>
                      </el-form-item>
                    </template>
                  </el-table-column>
                </el-table>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
        <el-card v-if="showShortMessage" shadow="never" class="write-freq-component short-message-container">
          <shortMessage :ref="refSms" v-model="smsContent" :maxSize="smsMaxSize" />
        </el-card>
        <el-card v-if="showEncryptSettings" shadow="never" class="write-freq-component encrypt-settings-container">
          <encryptSettings
            v-model:encryptEnable="encryptConfig.config.encryptEnable"
            v-model:encryptXORList="encryptXORList"
            v-model:encryptARC4List="encryptARC4List"
            v-model:encryptAES256List="encryptAES256List"
            :encryptListLimit="encryptListLimit"
          />
        </el-card>
        <el-card v-if="showMenuSettings" shadow="never" class="write-freq-component menu-settings-container">
          <el-form ref="menuSettings" class="menu-settings-form" :model="menuSettings" label-position="top">
            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.menuHangTime')" :label-width="menuHangTimeLabelWidth">
                  <el-input-number v-model="menuSettings.hangTime" step-strictly :min="0" :max="30" :step="1" />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.lcdHangTime')" :label-width="menuHangTimeLabelWidth">
                  <el-input-number v-model="menuSettings.lcdHangTime" step-strictly :min="0" :max="30" :step="1" />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.keyboardHangTime')" :label-width="menuHangTimeLabelWidth">
                  <el-input-number v-model="menuSettings.keyboardHangTime" step-strictly :min="0" :max="30" :step="1" />
                </el-form-item>
              </el-col>
              <!--              <el-col :xs="24" :sm="12">-->
              <!--                <el-form-item :label="$t('dialog.chDisplayMode')"-->
              <!--                              :label-width="menuHangTimeLabelWidth">-->
              <!--                  <el-select v-model="menuSettings.baseSetting.chDisplayMode"-->
              <!--                             :placeholder="$t('dialog.select')"-->
              <!--                             filterable-->
              <!--                             :no-match-text="$t('dialog.noMatchText')">-->
              <!--                    <el-option v-for="(item,i) in freqDisplayList"-->
              <!--                               :key="i"-->
              <!--                               :label="item.label"-->
              <!--                               :value="item.value">-->
              <!--                    </el-option>-->
              <!--                  </el-select>-->
              <!--                </el-form-item>-->
              <!--              </el-col>-->
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.callDisplayMode')" :label-width="menuHangTimeLabelWidth">
                  <el-select
                    v-model="menuSettings.baseSetting.callDisplayMode"
                    :placeholder="$t('dialog.select')"
                    filterable
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option v-for="(item, i) in callDisplayModeList" :key="i" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.baseSetting.menuOff">
                    <span v-text="$t('dialog.closeMenuButton')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.baseSetting.numberKeyFastDial">
                    {{ $t('writeFreq.numberKeyFastDial') }}
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col v-if="deviceWriteInfo.config.locate" :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.baseSetting.callDirectionEnable">
                    {{ $t('writeFreq.callDirectionEnable') }}
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.zoneConfig.enable">
                    <span v-text="$t('writeFreq.zone')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col v-if="deviceWriteInfo.config.bluetooth" :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.bluetoothConfig.enable">
                    <span v-text="$t('writeFreq.bluetooth')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('dialog.addressBook')" />
              </el-divider>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.contactConfig.contacts">
                    <span v-text="$t('dialog.addressBook')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.contactConfig.contactList" :disabled="contactsDisabled">
                    <span v-text="$t('dialog.contactList')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.contactConfig.contactGroup" :disabled="contactsDisabled">
                    {{ $t('writeFreq.contactGroup') }}
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.contactConfig.manualDialing" :disabled="contactsDisabled">
                    <span v-text="$t('dialog.manualDialing')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.contactConfig.newContact" :disabled="contactsDisabled">
                    <span v-text="$t('dialog.newContact')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.contactConfig.groupManagement" :disabled="contactsDisabled">
                    {{ $t('writeFreq.groupManagement') }}
                  </el-checkbox>
                </el-form-item>
              </el-col>

              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.contactOperation.editEnable" :disabled="contactsDisabled">
                    <span v-text="$t('writeFreq.edit')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.contactOperation.deleteEnable" :disabled="contactsDisabled">
                    {{ $t('writeFreq.contactDelete') }}
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.contactOperation.groupJoinOrExit" :disabled="contactsDisabled">
                    {{ $t('writeFreq.groupJoinOrExit') }}
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.contactOperation.sendSms" :disabled="contactsDisabled">
                    <span v-text="$t('dialog.sendMessages')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.contactOperation.callPrompt" :disabled="contactsDisabled">
                    <span v-text="$t('dialog.callReminder')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.contactOperation.deviceDetect" :disabled="contactsDisabled">
                    <span v-text="$t('dialog.deviceDetect')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.contactOperation.remoteMonitor" :disabled="contactsDisabled">
                    <span v-text="$t('dialog.remoteMonitor')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.contactOperation.deviceActive" :disabled="contactsDisabled">
                    <span v-text="$t('dialog.deviceActive')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>

              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.deviceControl.deviceRemoteDeath" :disabled="contactsDisabled">
                    <span v-text="$t('dialog.deviceRemoteDeath')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.deviceControl.deviceRemoteKill" :disabled="contactsDisabled">
                    <span v-text="$t('writeFreq.deviceRemoteDestroy')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.deviceControl.deviceRemoteStun" :disabled="contactsDisabled">
                    <span v-text="$t('writeFreq.deviceStun')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.deviceControl.deviceRemoteWakeup" :disabled="contactsDisabled">
                    <span v-text="$t('writeFreq.deviceWokeUp')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>

              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.contactGroup.singleDial" :disabled="contactsDisabled">
                    <span v-text="$t('dialog.singleCallDialing')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.contactGroup.groupDial" :disabled="contactsDisabled">
                    <span v-text="$t('dialog.groupCallDialing')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.contactGroup.newSingleContact" :disabled="contactsDisabled">
                    {{ $t('writeFreq.newSingleContact') }}
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.contactGroup.newGroupContact" :disabled="contactsDisabled">
                    {{ $t('writeFreq.newGroupContact') }}
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.contactGroup.newGroup" :disabled="contactsDisabled">
                    {{ $t('writeFreq.newGroup') }}
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.contactGroup.deleteGroup" :disabled="contactsDisabled">
                    {{ $t('writeFreq.deleteGroup') }}
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.contactGroup.editGroup" :disabled="contactsDisabled">
                    {{ $t('writeFreq.editGroup') }}
                  </el-checkbox>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('dialog.phoneBook')" />
              </el-divider>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.phoneConfig.enable">
                    <span v-text="$t('dialog.phoneBook')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.phoneConfig.phoneList" :disabled="phoneConfigDisabled">
                    <span v-text="$t('dialog.contactList')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.phoneConfig.manualDial" :disabled="phoneConfigDisabled">
                    <span v-text="$t('dialog.manualDialing')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.phoneConfig.newContact" :disabled="phoneConfigDisabled">
                    <span v-text="$t('dialog.newContact')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.phoneConfig.editContact" :disabled="phoneConfigDisabled">
                    <span v-text="$t('writeFreq.editContact')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.phoneConfig.deleteContact" :disabled="phoneConfigDisabled">
                    <span v-text="$t('writeFreq.contactDelete')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('writeFreq.scan')" />
              </el-divider>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.scanConfig.menuEnable">
                    <span v-text="$t('writeFreq.shiNeng')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.scanConfig.scanEnable" :disabled="scanConfigDisabled">
                    <span v-text="$t('dialog.scanEnable')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.scanConfig.editScanList" :disabled="scanConfigDisabled">
                    <span v-text="$t('writeFreq.editList')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('writeFreq.roaming')" />
              </el-divider>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.roamConfig.menuEnable">
                    <span v-text="$t('writeFreq.shiNeng')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.roamConfig.roamEnable" :disabled="roamConfigDisabled">
                    <span v-text="$t('writeFreq.roamEnable')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.roamConfig.lockSite" :disabled="roamConfigDisabled">
                    <span v-text="$t('writeFreq.roamLockSite')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.roamConfig.manualRoam" :disabled="roamConfigDisabled">
                    <span v-text="$t('writeFreq.roamManual')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.roamConfig.editRoamList" :disabled="roamConfigDisabled">
                    <span v-text="$t('writeFreq.editList')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('writeFreq.sms')" />
              </el-divider>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.smsConfig.enable">
                    <span v-text="$t('writeFreq.sms')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.smsConfig.newSms" :disabled="smsConfigDisabled">
                    <span v-text="$t('dialog.newSmsMessage')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.smsConfig.presetSms" :disabled="smsConfigDisabled">
                    <span v-text="$t('dialog.preMadeSms')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.smsConfig.receiveBox" :disabled="smsConfigDisabled">
                    <span v-text="$t('dialog.inbox')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.smsConfig.sendBox" :disabled="smsConfigDisabled">
                    <span v-text="$t('dialog.outbox')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.smsConfig.clearSms" :disabled="smsConfigDisabled">
                    <span v-text="$t('writeFreq.clearSms')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>

              <el-col v-if="deviceWriteInfo.config.tts" :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.smsOperation.tts" :disabled="smsConfigDisabled">TTS</el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.smsOperation.reply" :disabled="smsConfigDisabled">
                    <span v-text="$t('writeFreq.reply')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.smsOperation.forward" :disabled="smsConfigDisabled">
                    <span v-text="$t('writeFreq.forward')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.smsOperation.resend" :disabled="smsConfigDisabled">
                    <span v-text="$t('writeFreq.resend')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.smsOperation.delete" :disabled="smsConfigDisabled">
                    <span v-text="$t('operations.Delete')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('dialog.callRecord')" />
              </el-divider>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.callConfig.callRecord">
                    <span v-text="$t('dialog.callRecord')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.callConfig.unReceivedCall" :disabled="callConfigDisabled">
                    <span v-text="$t('dialog.missedCall')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.callConfig.receivedCall" :disabled="callConfigDisabled">
                    <span v-text="$t('dialog.answeredCall')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.callConfig.callOut" :disabled="callConfigDisabled">
                    <span v-text="$t('dialog.outgoingCall')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.callConfig.clearCallRecord" :disabled="callConfigDisabled">
                    <span v-text="$t('writeFreq.clearRecord')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row v-if="deviceWriteInfo.config.recording" :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('dialog.recording')" />
              </el-divider>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.recordConfig.menuEnable">
                    <span v-text="$t('writeFreq.shiNeng')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.recordConfig.recordEnable" :disabled="recordConfigDisabled">
                    <span v-text="$t('dialog.recording')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.recordConfig.recordFile" :disabled="recordConfigDisabled">
                    <span v-text="$t('writeFreq.recordFile')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.recordConfig.uDiskMode" :disabled="recordConfigDisabled">
                    <span v-text="$t('dialog.uDiskMode')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('dialog.configure')" />
              </el-divider>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.deviceConfig.menuEnable" @change="deviceAllConfigMenuEnableChange">
                    <span v-text="$t('dialog.configure')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.deviceInfo.infoMenuEnable" @change="deviceInfoMenuEnableChange">
                    <span v-text="$t('writeFreq.interphoneInfo')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('writeFreq.interphoneConfig')" />
              </el-divider>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.deviceConfig.deviceSetting" @change="deviceConfigMenuEnableChange">
                    <span v-text="$t('writeFreq.interphoneConfig')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.deviceConfig.offline" :disabled="deviceSettingDisabled">
                    <span v-text="$t('dialog.offNetwork')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.deviceConfig.toneTip" :disabled="deviceSettingDisabled">
                    <span v-text="$t('dialog.toneOrTip')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.deviceConfig.transmitPower" :disabled="deviceSettingDisabled">
                    <span v-text="$t('dialog.txPower')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.deviceConfig.bootInterface" :disabled="deviceSettingDisabled">
                    <span v-text="$t('dialog.bootInterface')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.deviceConfig.keyboardLock" :disabled="deviceSettingDisabled">
                    <span v-text="$t('dialog.keyboardLock')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>

              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.deviceConfig2.ledIndicator" :disabled="deviceSettingDisabled">
                    <span v-text="$t('dialog.ledIndicator')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.deviceConfig2.quieting" :disabled="deviceSettingDisabled">
                    <span v-text="$t('dialog.Quieting')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.deviceConfig2.powerOnPassword" :disabled="menuSettings.powerOnPwd.length !== 6">
                    <span v-text="$t('dialog.powerOnPwd')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.deviceConfig2.locale" :disabled="deviceSettingDisabled">
                    <span v-text="$t('writeFreq.langEnv')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.deviceConfig2.soundCtrl" :disabled="deviceSettingDisabled">
                    <span v-text="$t('dialog.voiceControl')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.deviceConfig2.timeSetting" :disabled="deviceSettingDisabled">
                    <span v-text="$t('dialog.timeSetting')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col v-if="deviceWriteInfo.config.locate" :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.deviceConfig2.locate" :disabled="deviceSettingDisabled">
                    <span v-text="$t('writeFreq.gpsLocate')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.deviceConfig2.workAlone" :disabled="deviceSettingDisabled">
                    <span v-text="$t('writeFreq.workAlone')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>

              <el-col v-if="deviceWriteInfo.config.upendAlarm" :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.deviceConfig3.upendEnable" :disabled="deviceSettingDisabled">
                    <span v-text="$t('writeFreq.upsideDown')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.deviceConfig3.sosRescueEnable" :disabled="deviceSettingDisabled">
                    <span v-text="$t('writeFreq.maydayRescue')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col v-if="deviceWriteInfo.config.shake" :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.deviceConfig3.shakeEnable" :disabled="deviceSettingDisabled">
                    <span v-text="$t('writeFreq.shakeMode')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.deviceConfig3.encryptEnable" :disabled="deviceSettingDisabled">
                    <span v-text="$t('writeFreq.encryption')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col v-if="deviceWriteInfo.config.denoise" :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.deviceConfig3.denoiseEnable" :disabled="deviceSettingDisabled">
                    <span v-text="$t('writeFreq.denoise')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.deviceConfig3.channelLockEnable" :disabled="deviceSettingDisabled">
                    <span v-text="$t('writeFreq.channelLock')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('writeFreq.channelConfig')" />
              </el-divider>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.channelSetting.channelConfigMenuEnable">
                    <span v-text="$t('dialog.chConfigSwitch')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.channelSetting.receivingFreq" :disabled="channelSettingDisabled">
                    <span v-text="$t('dialog.receiveFrequency')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.channelSetting.transmittingFreq" :disabled="channelSettingDisabled">
                    <span v-text="$t('dialog.transFrequency')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.channelSetting.channelName" :disabled="channelSettingDisabled">
                    <span v-text="$t('dialog.chName')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.channelSetting.transmitTimeLimit" :disabled="channelSettingDisabled">
                    <span v-text="$t('dialog.transTimeLimit')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.channelSetting.subAudioSetting" :disabled="channelSettingDisabled">
                    <span v-text="$t('dialog.subaudioSetting')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.channelSetting.launchContact" :disabled="channelSettingDisabled">
                    <span v-text="$t('dialog.launchContact')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.channelSetting.colorCode" :disabled="channelSettingDisabled">
                    <span v-text="$t('dialog.colorCodes')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>

              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.timeSlotSetting.timeSlot" :disabled="channelSettingDisabled">
                    <span v-text="$t('dialog.timeSlots')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.timeSlotSetting.virtualClusterTimeSlot" :disabled="channelSettingDisabled">
                    <span v-text="$t('dialog.virtualTimeSlot')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="menuSettings.timeSlotSetting.receivingList" :disabled="channelSettingDisabled">
                    <span v-text="$t('dialog.receivingList')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
        <el-card v-if="showSignalingSystem" shadow="never" class="write-freq-component">
          <el-form ref="signalingSystem" class="signaling-system-form" :model="signalingSystem" label-position="top" :rules="signalingSystemRules">
            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="signalingSystem.remoteConfig.remoteShutDecodeEnable">
                    <span v-text="$t('writeFreq.deviceRemoteDeadDecode')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="signalingSystem.remoteConfig.remoteMonitorDecode">
                    <span v-text="$t('writeFreq.remoteMonitorDecode')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="signalingSystem.remoteConfig.remoteNoticeDecode">
                    <span v-text="$t('writeFreq.remotePromptDecode')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="signalingSystem.remoteConfig.remoteDetectDecode">
                    <span v-text="$t('writeFreq.remoteDetectionDecode')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="signalingSystem.remoteConfig.remoteEraseDecode">
                    <span v-text="$t('writeFreq.remoteDestructionDecode')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="signalingSystem.remoteConfig.remoteStunWakeupDecode">
                    <span v-text="$t('writeFreq.remoteStunWakeupDecode')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.remoteAlertCount')">
                  <el-input-number v-model="signalingSystem.remoteAlertCount" step-strictly :min="0" :max="255" :step="1" />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.remoteMonitorDuration')">
                  <el-input-number v-model="signalingSystem.remoteMonitorDuration" step-strictly :min="10" :max="120" :step="10" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('writeFreq.airEncryption')" />
              </el-divider>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="signalingSystem.signalingEncrypt.remoteShutEncryptEnable">
                    <span v-text="$t('writeFreq.remoteShutEncryptEnable')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="signalingSystem.signalingEncrypt.remoteMonitorDecodeEnable">
                    <span v-text="$t('writeFreq.remoteMonitorDecodeEnable')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="signalingSystem.signalingEncrypt.remoteEraseDecodeEnable">
                    <span v-text="$t('writeFreq.remoteEraseDecodeEnable')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="signalingSystem.signalingEncrypt.remoteStunDecodeEnable">
                    <span v-text="$t('writeFreq.remoteStunDecodeEnable')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.signalingPwd')" prop="signalingPwd">
                  <el-input v-model="signalingSystem.signalingPwd" type="password" :maxlength="16" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
        <el-card v-if="showAlertConfig" shadow="never" class="write-freq-component">
          <el-form ref="alertConfig" class="signaling-system-form" :model="alertConfig" label-position="top">
            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('writeFreq.workAlone')" />
              </el-divider>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="alertConfig.aloneWorkEnable">
                    <span v-text="$t('writeFreq.workAlone')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.workResTimeAlone')">
                  <el-input-number v-model="alertConfig.responseTime" :disabled="workAloneUnEnable" step-strictly :min="1" :max="255" :step="1" />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.workAloneReminderTime')">
                  <el-input-number v-model="alertConfig.remindTime" :disabled="workAloneUnEnable" step-strictly :min="0" :max="255" :step="1" />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.workResOptAlone')">
                  <el-select
                    v-model="alertConfig.responseOperation"
                    :placeholder="$t('dialog.select')"
                    :disabled="workAloneUnEnable"
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option v-for="(item, i) in aloneWorkOptList" :key="i" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row v-if="deviceWriteInfo.config.upendAlarm" :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon>
                  <CaretBottom />
                </el-icon>
                <span v-text="$t('writeFreq.runBackward')" />
              </el-divider>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="alertConfig.upendEnable">
                    <span v-text="$t('writeFreq.upsideDownSwitch')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.triggerMode')">
                  <el-select
                    v-model="alertConfig.upendConfig.triggerMode"
                    :placeholder="$t('dialog.select')"
                    :disabled="upsideDownUnEnable"
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option v-for="item in triggerModeList" :key="item.label" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.triggerInclination')">
                  <el-select
                    v-model="alertConfig.upendConfig.triggerTilt"
                    :placeholder="$t('dialog.select')"
                    :disabled="upsideDownUnEnable"
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option v-for="item in triggerTiltList" :key="item.label" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.entryDelay')">
                  <el-input-number v-model="alertConfig.entryDelay" :disabled="upsideDownUnEnable" step-strictly :min="5" :max="255" :step="1" />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.exitDelay')">
                  <bf-input-number
                    v-model="alertConfig.quitDelay"
                    :disabled="upsideDownUnEnable"
                    :formatter="val => (val === 255 ? $t('writeFreq.unlimited') : val)"
                    step-strictly
                    :min="0"
                    :max="255"
                    :step="1"
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.promptTimeBackwards')">
                  <el-input-number v-model="alertConfig.upendConfig.preRewindTime" :disabled="upsideDownUnEnable" step-strictly :min="0" :max="10" :step="1" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
        <el-card v-show="showDigitalAlert" shadow="never" class="write-freq-component digital-alert-container">
          <digitalAlert
            ref="digitalAlert"
            v-model="digitalAlertList"
            v-model:alertId="currDigitalAlertId"
            :channelList="channelDataList"
            :limit="digitalAlertListLimit"
            :model="'TD920'"
            @init-data="loadDigitalAlertNode"
            @name-change="updateDigitalAlertNode"
          />
        </el-card>
        <el-card v-if="showGpsSettings" shadow="never" class="write-freq-component">
          <el-form v-if="deviceWriteInfo.config.locate" ref="gpsData" class="gpsSettings-form" :model="gpsSettings" label-position="top">
            <el-row :gutter="20" class="no-margin-x">
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.gpsMode')">
                  <el-select v-model="gpsMode" :placeholder="$t('dialog.select')" filterable :no-match-text="$t('dialog.noMatchText')">
                    <el-option v-for="(item, i) in gpsModeList" :key="i" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.connectionTimes')">
                  <bf-input-number
                    v-model="gpsSettings.connectionCount"
                    :disabled="!gpsSettingsEnable"
                    :formatter="v => (v === 0 ? $t('writeFreq.off') : v)"
                    step-strictly
                    :min="0"
                    :max="255"
                    :step="1"
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.pttTimes')">
                  <bf-input-number
                    v-model="gpsSettings.pttCount"
                    :disabled="!gpsSettingsEnable"
                    :formatter="v => (v === 0 ? $t('writeFreq.off') : v)"
                    step-strictly
                    :min="0"
                    :max="255"
                    :step="1"
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.controlCenter')">
                  <el-input-number v-model="gpsSettings.centerId" :disabled="!gpsSettingsEnable" step-strictly :min="1" :max="16776415" :step="1" />
                </el-form-item>
              </el-col>
              <el-col>
                <el-form-item :label="$t('writeFreq.queryCommand')">
                  <el-input
                    v-model="gpsSettings.queryCmd"
                    :disabled="!gpsSettingsEnable"
                    @input="val => (gpsSettings.queryCmd = gpsSettingsQueryCmdInputEvent(val))"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
        <el-card v-show="showDigitalAddress" shadow="never" class="write-freq-component address-book-container">
          <addressBook
            :ref="addrBookTreeId"
            class="address-book"
            :treeId="addrBookTreeId"
            :maxSize="1600"
            :redrawTree="showDigitalAddress"
            :callTypes="addressBookCallTypes"
            :addressBook="originAddressBook"
            noSysSelect
            @select="selectAddressBooks"
          />
        </el-card>
        <el-card v-show="showAddressGroup" shadow="never" class="write-freq-component address-group-container">
          <addressBookGroup
            ref="addressBookGroup"
            v-model="addressBookGroup"
            v-model:addressBook="selectedAddressBook"
            v-model:dataId="currAddressBookGroupId"
            :limit="addressBookGroupLimit"
            @name-change="updateAddressBookGroupNode"
            @init-data="loadAddressBookGroupNode"
          />
        </el-card>
        <el-card v-show="showPhoneBook" shadow="never" class="write-freq-component phone-book-container">
          <phoneBook :ref="phoneBookTreeId" class="phone-book" :treeId="phoneBookTreeId" :redrawTree="showPhoneBook" @select="selectPhoneBooks" />
        </el-card>
        <el-card v-show="showRxGroup" shadow="never" class="write-freq-component receive-group-container">
          <receiveGroup
            :ref="refReceiveGroup"
            v-model="rxGroupList"
            :channels="selectedChannels"
            :addressTreeId="addrBookTreeId"
            :getDefaultAddress="getDefaultAddress"
            :getAddressName="getAddressNameByDmrId"
            :getOriginAddress="getOriginAddressBook"
          />
        </el-card>
        <el-card v-show="showZoneRootData" shadow="never" class="write-freq-component channel-container">
          <multistageZone
            ref="rootZone"
            v-model="zoneRootDataList"
            v-model:dataId="currZoneRootId"
            :limit="zoneRootLimit"
            :level="1"
            @row-dblclick="zoneRootDataDblclick"
          />
        </el-card>
        <el-card v-show="showZoneParentData" shadow="never" class="write-freq-component channel-container">
          <multistageZone
            ref="parentZone"
            v-model="zoneParentDataList"
            v-model:dataId="currZoneParentId"
            :limit="zoneParentLimit"
            :parentZone="zoneRootDataIndex[currZoneRootId]"
            :level="2"
            @row-dblclick="zoneParentDataDblclick"
          />
        </el-card>
        <el-card v-show="showZoneLeafData" shadow="never" class="write-freq-component channel-container">
          <multistageZone
            ref="leafZone"
            v-model="zoneLeafDataList"
            v-model:dataId="currZoneLeafId"
            :limit="zoneLeafLimit"
            :parentZone="zoneParentDataIndex[currZoneParentId]"
            :level="3"
            @row-dblclick="zoneLeafDataDblclick"
          />
        </el-card>
        <el-card v-show="showZoneLeafChannels" shadow="never" class="write-freq-component channel-container">
          <zoneLeafTable
            ref="zoneLeafChannel"
            v-model="channelDataList"
            :limit="oneZoneLeafChannelLimit"
            :parentZone="zoneLeafDataIndex[currZoneLeafId]"
            :getDefaultChannel="getDefaultChannel"
            @row-dblclick="zoneLeafDataRowDblclick"
          />
        </el-card>
        <el-card v-show="showChannelItem" shadow="never" class="write-freq-component channel-container">
          <el-form ref="channelSetting" class="channel-setting-form" :model="oneChannel" :rules="channelRules" label-position="top">
            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.chName')" prop="chName">
                  <el-input v-model="oneChannel.chName" :maxlength="16" @change="chNameChanged" />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.chType')">
                  <el-select v-model="oneChannel.chType" @change="chTypeChanged">
                    <el-option v-for="(item, i) in chTypeList" :key="i" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col v-if="isAChannel" :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.broadBand')">
                  <el-select v-model="oneChannel.funcConfig.bandwidthFlag">
                    <el-option v-for="(item, i) in bandwidthFlagList" :key="i" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.scanOrRoamList')">
                  <el-select v-model="oneChannel.scanListWrap" :disabled="disableScanList" @change="scanListWrapChange">
                    <el-option v-for="item in chScanRoamGroupList" :key="item.label + item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="oneChannel.scanConfig.autoScan" :disabled="disAutoScan">
                    <span v-text="$t('writeFreq.autoScanning')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col v-if="isDChannel" :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="oneChannel.scanConfig.autoRoam" :disabled="disAutoRoam">
                    <span v-text="$t('writeFreq.autoRoaming')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col v-if="!isDChannel" :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.squelchLevel')">
                  <el-input-number v-model="oneChannel.powerConfig.squelchLevel" step-strictly :min="0" :max="9" :step="1" />
                </el-form-item>
              </el-col>
              <el-col v-if="!isAChannel" :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.colorCodes')">
                  <el-input-number v-model="oneChannel.colorCode" step-strictly :min="0" :max="15" :step="1" />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="oneChannel.scanConfig.allowOfflineSign" :disabled="sameFreq">
                    <span v-text="$t('dialog.allowOffNetwork')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="oneChannel.scanConfig.onlyReceive">
                    <span v-text="$t('dialog.receiveOnly')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col v-if="isDChannel" :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="oneChannel.scanConfig.ipSiteConnect" :disabled="sameFreq || onlyReceive">
                    <span v-text="$t('writeFreq.ipSiteConnection')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col v-if="!isAChannel" :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="oneChannel.timeSlotConfig.priorityInterrupt">
                    <span v-text="$t('writeFreq.priorityInterrupt')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col v-if="isDChannel && deviceWriteInfo.config.sdc" :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="oneChannel.networkConfig.networking">
                    <span v-text="$t('writeFreq.networking')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col v-if="isDChannel && deviceWriteInfo.config.sdc" :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="oneChannel.networkConfig.localCall" :disabled="!isConnectNetworking">
                    <span v-text="$t('writeFreq.localCall')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col v-if="!isAChannel" :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="oneChannel.timeSlotConfig.DCDMEnable" :disabled="!sameFreq">
                    <span v-text="$t('writeFreq.TDMAThroughMode')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col v-if="!isAChannel" :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="oneChannel.networkConfig.directMode">
                    <span v-text="$t('dialog.passThroughMode')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col v-if="!isAChannel" :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="oneChannel.networkConfig.smsUdpCompress">
                    <span v-text="$t('writeFreq.smsUdpCompress')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col v-if="!isAChannel" :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="oneChannel.networkConfig.voiceCallEmbedding">
                    <span v-text="$t('writeFreq.voiceCallEmbedding')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col v-if="!isAChannel" :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.chTimeSlotCalibrator')">
                  <el-select v-model="oneChannel.timeSlotConfig.chSlotAdjust" :disabled="!throughEnable">
                    <el-option v-for="(item, i) in chTimeSlotCalList" :key="i" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col v-if="!isAChannel" :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.slotMode')">
                  <el-select v-model="oneChannel.timeSlotConfig.timeSlot" :placeholder="$t('dialog.select')" :no-match-text="$t('dialog.noMatchText')">
                    <el-option v-for="(item, i) in slotModeList" :key="i" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col v-if="!isAChannel" :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.virtualTimeSlot')">
                  <el-select
                    v-model="oneChannel.timeSlotConfig.virtualTimeSlot"
                    :placeholder="$t('dialog.select')"
                    :disabled="oneChannel.timeSlotConfig.timeSlot !== 2"
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option v-for="(item, i) in virtualTimeSlotList" :key="i" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col v-if="!isAChannel" :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="oneChannel.networkConfig.voiceDuplex" :disabled="onlyReceive || oneChannel.timeSlotConfig.timeSlot !== 2">
                    <span v-text="$t('writeFreq.voiceDuplex')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col v-if="!isAChannel" :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.voicePriority')">
                  <el-input-number v-model="oneChannel.alertConfig.voicePriority" step-strictly :min="0" :max="3" :step="1" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row v-if="isDChannel" :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon class="divider-icon">
                  <CaretBottom />
                </el-icon>
                <span class="divider-label" v-text="$t('writeFreq.encryption')" />
              </el-divider>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="oneChannel.encryptConfig.enable" :disabled="disEncryptConfigEnable">
                    <span v-text="$t('dialog.enable')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.encryptType')">
                  <el-select
                    v-model="oneChannel.encryptConfig.type"
                    :placeholder="$t('dialog.select')"
                    :disabled="disEncryption"
                    :no-match-text="$t('dialog.noMatchText')"
                    @change="encryptTypeChange"
                  >
                    <el-option v-for="(item, i) in encryptTypeList" :key="i" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.encryptionAlgorithm')">
                  <el-select
                    v-model="oneChannel.encryptConfig.algorithm"
                    :placeholder="$t('dialog.select')"
                    :disabled="disEncryption"
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option v-for="(item, i) in algorithmList" :key="i" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="oneChannel.encryptConfig.encryptKeyRandom" :disabled="disEncryption || oneChannel.encryptConfig.type === 0">
                    <span v-text="$t('writeFreq.randomKey')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="oneChannel.encryptConfig.decryptKeyRandom" :disabled="disEncryption || oneChannel.encryptConfig.type === 0">
                    <span v-text="$t('writeFreq.randomizedAlgorithm')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.keyList')">
                  <el-select
                    v-model="oneChannel.encryptListId"
                    :placeholder="$t('dialog.select')"
                    :disabled="disEncryption"
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option v-for="item in encryptKeyList" :key="item.label" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon class="divider-icon">
                  <CaretBottom />
                </el-icon>
                <span class="divider-label" v-text="$t('dialog.receive')" />
              </el-divider>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.rxFrequency')" prop="rxFreq">
                  <frequencyMhz v-model="oneChannel.rxFreq" :maxlength="9" />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <freqMapOffset
                  v-model="freqOffset"
                  v-model:dstFreq="oneChannel.txFreq"
                  :srcFreq="oneChannel.rxFreq"
                  :freqRange="deviceWriteInfo.frequencyRange"
                />
              </el-col>
              <el-col v-if="!isDChannel" :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.decoding')">
                  <el-select v-model="oneChannel.subsonicDecode" filterable :placeholder="$t('dialog.select')" :no-match-text="$t('dialog.noMatchText')">
                    <el-option v-for="item in subtoneCodeDataList" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col v-if="!isDChannel" :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.tailSelection')">
                  <el-select
                    v-model="oneChannel.funcConfig.ctcssRxDps"
                    :disabled="disableCtcssRxDps"
                    :placeholder="$t('dialog.select')"
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option v-for="(item, i) in tailToneList" :key="i" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col v-if="!isAChannel" :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.receiveGroupList')">
                  <el-select v-model="oneChannel.receiveGroup" disabled :placeholder="$t('dialog.select')" :no-match-text="$t('dialog.noMatchText')">
                    <el-option v-for="item in receiveGroupList" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col v-if="!isAChannel" :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="oneChannel.alertConfig.emergencyAlertTip" :disabled="isConnectNetworking">
                    <span v-text="$t('writeFreq.emergencyAlarmIndication')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col v-if="!isAChannel" :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="oneChannel.alertConfig.emergencyAlertConfirm" :disabled="!oneChannel.alertConfig.emergencyAlertTip">
                    <span v-text="$t('writeFreq.emergencyAlarmConfirm')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col v-if="!isAChannel" :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="oneChannel.alertConfig.emergencyCallTip" :disabled="isConnectNetworking">
                    <span v-text="$t('writeFreq.emergencyCallAlert')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-divider>
                <el-icon class="divider-icon">
                  <CaretBottom />
                </el-icon>
                <span class="divider-label" v-text="$t('dialog.emission')" />
              </el-divider>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.txFrequency')" prop="txFreq">
                  <frequencyMhz v-model="oneChannel.txFreq" :maxlength="9" :disabled="onlyReceive" />
                </el-form-item>
              </el-col>
              <el-col v-if="!isDChannel" :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.encoding')">
                  <el-select
                    v-model="oneChannel.subsonicEncode"
                    filterable
                    :placeholder="$t('dialog.select')"
                    :no-match-text="$t('dialog.noMatchText')"
                    :disabled="onlyReceive"
                  >
                    <el-option v-for="item in subtoneCodeDataList" :key="item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col v-if="!isDChannel" :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.plosive')">
                  <el-select
                    :key="Date.now()"
                    v-model="oneChannel.funcConfig.ctcssTxDps"
                    :disabled="disableCtcssTxDps"
                    :placeholder="$t('dialog.select')"
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option v-for="item in tailToneList" :key="item.label + item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col v-if="!isAChannel" :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.sendGroup')">
                  <el-select v-model="oneChannel.defaultAddress" disabled :placeholder="$t('dialog.select')" :no-match-text="$t('dialog.noMatchText')">
                    <el-option v-for="item in defaultAddressList" :key="item.label + item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col v-if="!isAChannel" :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.emergencySystem')">
                  <el-select
                    v-model="oneChannel.emergencySysId"
                    :placeholder="$t('dialog.select')"
                    :no-match-text="$t('dialog.noMatchText')"
                    :disabled="isConnectNetworking || onlyReceive"
                  >
                    <el-option v-for="item in emergencySysIdList" :key="item.label + item.value" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.txPower')">
                  <el-select
                    v-model="oneChannel.powerConfig.powerType"
                    :placeholder="$t('dialog.select')"
                    :no-match-text="$t('dialog.noMatchText')"
                    :disabled="onlyReceive"
                  >
                    <el-option v-for="(item, i) in txPowerTypes" :key="i" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.sendTimeLimiter')">
                  <el-input-number v-model="oneChannel.transmissionLimit" step-strictly :min="15" :max="495" :step="15" :disabled="onlyReceive" />
                </el-form-item>
              </el-col>
              <el-col :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.totPwdUpdateDelay')">
                  <el-input-number v-model="oneChannel.totKeyUpdateDelay" step-strictly :min="0" :max="255" :step="1" :disabled="onlyReceive" />
                </el-form-item>
              </el-col>
              <el-col v-if="!isAChannel" :xs="24" :sm="12">
                <el-form-item :label="$t('dialog.permissionConditions')">
                  <el-select
                    v-model="oneChannel.alertConfig.permitConditions"
                    :placeholder="$t('dialog.select')"
                    :no-match-text="$t('dialog.noMatchText')"
                    :disabled="onlyReceive"
                  >
                    <el-option v-for="(item, i) in permissionConditionsList" :key="i" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col v-if="!isAChannel" :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="oneChannel.alertConfig.singleCallRes" :disabled="onlyReceive">
                    <span v-text="$t('writeFreq.singleCallVoiceConfirm')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col v-if="!isDChannel" :xs="24" :sm="12">
                <el-form-item :label="$t('writeFreq.busyChannelLock')">
                  <el-select
                    v-model="oneChannel.funcConfig.bclFlag"
                    :placeholder="$t('dialog.select')"
                    :no-match-text="$t('dialog.noMatchText')"
                    :disabled="onlyReceive"
                  >
                    <el-option v-for="(item, i) in busyChannelLockList" :key="i" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col v-if="!isDChannel" :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="oneChannel.funcConfig.tailCancellation" :disabled="onlyReceive">
                    <span v-text="$t('dialog.tailCancellation')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col v-if="isDChannel" :xs="24" :sm="12">
                <el-form-item>
                  <el-checkbox v-model="confirmedDataSingleCall">
                    <span v-text="$t('writeFreq.confirmedDataSingleCall')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
        <el-card v-show="showScan" shadow="never" class="write-freq-component scan-container">
          <scanGroup
            ref="scanGroup"
            v-model="scanGroup"
            v-model:dataId="currScanGroupId"
            :channels="channelDataList"
            :limit="scanGroupLimit"
            :is-fullscreen="isFullscreen"
            :model="'TD920'"
            @name-change="updateScanGroupNode"
            @init-data="loadScanGroupNode"
          />
        </el-card>
        <el-card v-show="showRoam" shadow="never" class="write-freq-component roam-container">
          <roamGroup
            v-model="roamGroup"
            ref="roamGroup"
            v-model:dataId="currRoamGroupId"
            :channels="channelDataList"
            :limit="roamGroupLimit"
            :directMode="directMode"
            :currChannel="oneChannel"
            :is-fullscreen="isFullscreen"
            :model="'TD920'"
            autoSiteSearchTimer
            @name-change="updateRoamGroupNode"
            @init-data="loadRoamGroupNode"
          />
        </el-card>
        <el-tabs v-if="showPatrolSystem" v-model="patrolSystemTabsValue" type="border-card" class="write-freq-component patrol-system-container">
          <el-tab-pane :label="$t('dialog.configure')" name="configure" class="h-full">
            <patrolConfig ref="patrolConfig" v-model="patrolConfig" />
          </el-tab-pane>
          <el-tab-pane :label="$t('dialog.emergency')" name="emergency" class="h-full">
            <emergencyAlarmConfig ref="emergencyAlarm" v-model="emergencyAlarm" :addressBooks="selectedAddressBook" :showAutoTrackTime="true" hasSelectedItem />
          </el-tab-pane>
          <el-tab-pane :label="$t('dialog.trailCtrl')" name="trailCtrl" class="h-full">
            <el-form class="patrol-system-trailCtrl-form" :model="trailCtrl" label-width="95px" label-position="top">
              <el-row :gutter="20" class="no-margin-x">
                <el-col :xs="24">
                  <el-form-item>
                    <el-checkbox v-model="trailCtrl.menuConfig.enable">
                      <span v-text="$t('writeFreq.enable')" />
                    </el-checkbox>
                  </el-form-item>
                </el-col>
                <el-col :xs="24">
                  <el-form-item :label="$t('dialog.pollingTimeSlice')">
                    <el-input-number v-model="trailCtrl.rollTime" :disabled="trailCtrlDisable" step-strictly :min="10" :max="9995" :step="5" />
                  </el-form-item>
                </el-col>
                <el-col :xs="24">
                  <el-form-item :label="$t('dialog.shortestDistance')">
                    <el-input-number v-model="trailCtrl.rollDistant" :disabled="trailCtrlDisable" step-strictly :min="0" :max="495" :step="5" />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-tab-pane>
        </el-tabs>
        <el-card v-if="showSosSettings" shadow="never" class="write-freq-component sos-settings-container">
          <el-form ref="sosSettings" class="sos-settings-form" :model="sosCfg" label-width="120px" label-position="top">
            <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
              <el-col :xs="24" :sm="12">
                <el-form-item label-width="0">
                  <el-checkbox v-model="sosCfg.config.enable">
                    <span v-text="$t('writeFreq.sosRescue')" />
                  </el-checkbox>
                </el-form-item>
              </el-col>
              <el-col :xs="24">
                <el-form-item :label="$t('writeFreq.intervalHonkingTime')" class="tone-volume-form-item">
                  <el-checkbox v-model="sosCfg.config.intervalHonking" :disabled="!sosEnable" />
                  <el-input-number
                    v-model="intervalHonkingTime"
                    step-strictly
                    :min="5"
                    :max="60"
                    :step="5"
                    :disabled="!sosEnable || !sosCfg.config.intervalHonking"
                    class="grow"
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24">
                <el-form-item :label="$t('writeFreq.flashlightTime')" class="tone-volume-form-item">
                  <el-checkbox v-model="sosCfg.config.flashlight" :disabled="!sosEnable" />
                  <el-input-number
                    v-model="flashlightTime"
                    step-strictly
                    :min="500"
                    :max="2000"
                    :step="100"
                    :disabled="!sosEnable || !sosCfg.config.flashlight"
                    class="grow"
                  />
                </el-form-item>
              </el-col>
              <el-col :xs="24">
                <el-form-item :label="$t('writeFreq.rescueScanHandUpTime')" class="tone-volume-form-item">
                  <el-input-number v-model="sosCfg.rescueScanHandUpTime" step-strictly :min="0" :max="10000" :step="500" />
                </el-form-item>
              </el-col>
              <el-col :xs="24">
                <el-form-item :label="$t('writeFreq.highPowerSosTime')" class="tone-volume-form-item">
                  <el-input-number v-model="highPowerSosTime" step-strictly :min="3" :max="10" :step="1" :disabled="!sosEnable" />
                </el-form-item>
              </el-col>
              <el-col :xs="24">
                <el-form-item :label="$t('writeFreq.middlePowerSosTime')" class="tone-volume-form-item">
                  <el-input-number v-model="middlePowerSosTime" step-strictly :min="5" :max="15" :step="1" :disabled="!sosEnable" />
                </el-form-item>
              </el-col>
              <el-col :xs="24">
                <el-form-item :label="$t('writeFreq.lowPowerSosTime')" class="tone-volume-form-item">
                  <el-input-number v-model="lowPowerSosTime" step-strictly :min="10" :max="30" :step="1" :disabled="!sosEnable" />
                </el-form-item>
              </el-col>
              <el-col :xs="24">
                <el-form-item :label="$t('writeFreq.sosInfo')" class="tone-volume-form-item">
                  <el-input v-model="sosCfg.sosInfo" type="textarea" resize="none" :maxlength="24" show-word-limit :disabled="!sosEnable" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-card>
      </main>
    </section>
    <write-freq-footer
      :is-reading="isReading"
      :is-writing="isWriting"
      :disable-read="disReadBtn"
      :disable-write="disWriteBtn"
      @new-config="newConfig"
      @read-config="readDataConfig"
      @write-config="writeInFrequency"
      @export-config="exportConfig"
      @import-config="importConfig"
    />
  </div>
</template>

<script>
  import TableTree from '@/components/common/tableTree'
  import { cloneDeep, merge, debounce } from 'lodash'
  import Long from 'long'
  import bftree from '@/utils/bftree'
  import bfutil from '@/utils/bfutil'
  import bfNotify from '@/utils/notify'
  import validateRules from '@/utils/validateRules'
  import { AnalogCodeData, AnalogDigitalCodeData } from '@/writingFrequency/interphone/511SDC00'
  import {
    filterChannelIdWhenDeviceChanged,
    fixPowerOnPassword,
    fixProgramPassword,
    generateEnumObject,
    gpsSettingsQueryCmdInputEvent,
    resetDigitalAlertReplyChannel,
  } from '@/writingFrequency/interphone/common'
  import commonToolMixin from '@/writingFrequency/interphone/commonToolMixin'
  import { ButtonKeys, CallType, getClassInstance, Model, SoftKeyCallType, TableIndex } from '@/writingFrequency/interphone/TD920'
  import wfTool from '@/writingFrequency/interphone/tool'
  import deviceInfo from '@/platform/dataManage/deviceManage/common/deviceInfo'
  import selectDevice from '@/platform/dataManage/deviceManage/common/selectDevice'
  import WriteFreqFooter from '@/platform/dataManage/deviceManage/common/writeFreqFooter.vue'
  import { defineAsyncComponent } from 'vue'

  function getTimeZoneOffsetObject(TimeZoneOffset = new Date().getTimezoneOffset()) {
    return {
      timeZoneHour: ((TimeZoneOffset * -1) / 60) | 0,
      timeZoneMinute: Math.abs(TimeZoneOffset) % 60,
    }
  }

  const Password = {
    md5Key: '',
  }
  const MenuTreeContextmenu = generateEnumObject({
    ADD: 1,
    DELETE: 2,
    ORDER: 3,
  })
  const now = new Date()
  const GeneralSettings = {
    deviceName: '',
    intDmrId: 0,
    repeaterId: 16777215,
    sendLeadCodeTime: 960,
    offlineGroupCallHungTime: 2000,
    offlineSingleCallHungTime: 2000,
    locale: 0,
    baseSettings: {
      rejectUnfamiliarCall: false,
      pttAlone: false,
      allowedSelfDestruct: true,
      allowErasing: false,
    },
    savePowerMode: 2,
    savePowerDelayTime: 5,
    // LED相关
    LEDConfig: {
      disabledAllLED: false,
    },
    soundSettings: {
      muteAll: false,
      voiceNotice: true,
      channelFreeNotice: false,
      allowCallInstruction: 2,
    },
    powerInfoAlert: 120,
    shakeSettings: {
      shakeEnable: false,
      singleCallShake: false,
      messageShake: false,
      callNoticeShake: false,
    },
    soundCtrlLevel: 0,
    soundCtrlDelay: 500,
    stealthSettings: {
      stealthModeEnable: false,
      stealthModeHeadsetMute: false,
    },
    ...getTimeZoneOffsetObject(),
    syncTime: false,
    year: now.getUTCFullYear(),
    month: now.getUTCMonth() + 1,
    day: now.getUTCDate(),
    hour: now.getUTCHours() + getTimeZoneOffsetObject().timeZoneHour,
    minute: now.getUTCMinutes(),
    second: now.getUTCSeconds(),
    // 卫星定位
    gpsSettings: {
      gpsEnable: true,
      beiDouEnable: true,
    },
    recordSettings: {
      enable: false,
      compressionRatio: 0,
    },
    // TTS
    ttsVoice: 0,
    ttsSpeed: 5,
    ttsTune: 5,
    ttsVolume: 5,
    // 蓝牙相关
    bluetoothSettings: {
      enable: false,
      pttKeepEnable: false,
    },
    // 降噪相关
    denoiseSettings: {
      enable: false,
    },
    powerTransfer: {
      enable: false,
    },
    highPowerLimit: -75,
    middlePowerLimit: -70,
    lowPowerLimit: -65,
    // U盘模式密码
    usbFlashDiskPassword: '',
  }
  const ButtonDefined = {
    longPressTime: 1000,
    // 长按、短按
    sideKey: [
      // s1
      {
        short: ButtonKeys.NONE,
        long: ButtonKeys.NONE,
      },
      // s2
      {
        short: ButtonKeys.NONE,
        long: ButtonKeys.NONE,
      },
      // p1
      {
        short: ButtonKeys.BATTERY_CHANGE,
        long: ButtonKeys.NONE,
      },
      // p2
      {
        short: ButtonKeys.NONE,
        long: ButtonKeys.KEYLOCK,
      },
    ],
    // 单键功能呼叫
    singleKeyCall: [
      {
        callMode: 0,
        callId: 65535,
        callType: 1,
        smsId: 255,
      },
      {
        callMode: 0,
        callId: 65535,
        callType: 1,
        smsId: 255,
      },
      {
        callMode: 0,
        callId: 65535,
        callType: 1,
        smsId: 255,
      },
      {
        callMode: 0,
        callId: 65535,
        callType: 1,
        smsId: 255,
      },
      {
        callMode: 0,
        callId: 65535,
        callType: 1,
        smsId: 255,
      },
      {
        callMode: 0,
        callId: 65535,
        callType: 1,
        smsId: 255,
      },
    ],
    // 长按数字键功能呼叫
    longPressNumCallTable: [
      {
        callMode: 0,
        callId: 65535,
        callType: 1,
        smsId: 255,
      },
      {
        callMode: 0,
        callId: 65535,
        callType: 1,
        smsId: 255,
      },
      {
        callMode: 0,
        callId: 65535,
        callType: 1,
        smsId: 255,
      },
      {
        callMode: 0,
        callId: 65535,
        callType: 1,
        smsId: 255,
      },
      {
        callMode: 0,
        callId: 65535,
        callType: 1,
        smsId: 255,
      },
      {
        callMode: 0,
        callId: 65535,
        callType: 1,
        smsId: 255,
      },
      {
        callMode: 0,
        callId: 65535,
        callType: 1,
        smsId: 255,
      },
      {
        callMode: 0,
        callId: 65535,
        callType: 1,
        smsId: 255,
      },
      {
        callMode: 0,
        callId: 65535,
        callType: 1,
        smsId: 255,
      },
      {
        callMode: 0,
        callId: 65535,
        callType: 1,
        smsId: 255,
      },
    ],
    // 预设信道
    defaultChannel: [
      {
        rootId: 0xffff,
        parentId: 0xffff,
        zoneId: 0xffff,
        channelId: 0xffff,
      },
      {
        rootId: 0xffff,
        parentId: 0xffff,
        zoneId: 0xffff,
        channelId: 0xffff,
      },
      {
        rootId: 0xffff,
        parentId: 0xffff,
        zoneId: 0xffff,
        channelId: 0xffff,
      },
      {
        rootId: 0xffff,
        parentId: 0xffff,
        zoneId: 0xffff,
        channelId: 0xffff,
      },
    ],
  }
  const EncryptConfig = {
    config: {
      // 加密使能
      encryptEnable: false,
    },
  }

  const MenuSettings = {
    hangTime: 10,
    lcdHangTime: 10,
    keyboardHangTime: 5,
    baseSetting: {
      menuOff: false,
      chDisplayMode: 2,
      callDisplayMode: 2,
      numberKeyFastDial: false,
      callDirectionEnable: false,
    },
    powerOnPwd: '',
    chConfigPwd: '',
    contactConfig: {
      contacts: true,
      contactList: true,
      contactGroup: true,
      manualDialing: true,
      newContact: true,
      groupManagement: true,
    },
    contactOperation: {
      editEnable: true,
      deleteEnable: true,
      groupJoinOrExit: true,
      sendSms: true,
      callPrompt: true,
      deviceDetect: false,
      remoteMonitor: false,
      deviceActive: false,
    },
    deviceControl: {
      deviceRemoteDeath: false,
      deviceRemoteKill: false,
      deviceRemoteStun: false,
      deviceRemoteWakeup: false,
    },
    contactGroup: {
      singleDial: true,
      groupDial: true,
      newSingleContact: true,
      newGroupContact: true,
      newGroup: true,
      deleteGroup: true,
      editGroup: true,
    },
    phoneConfig: {
      enable: true,
      phoneList: true,
      manualDial: true,
      newContact: true,
      editContact: false,
      deleteContact: false,
    },
    zoneConfig: {
      enable: true,
    },
    scanConfig: {
      menuEnable: true,
      scanEnable: true,
      editScanList: true,
    },
    roamConfig: {
      menuEnable: true,
      roamEnable: true,
      editRoamList: true,
      lockSite: true,
      manualRoam: true,
    },
    smsConfig: {
      enable: true,
      newSms: true,
      presetSms: true,
      receiveBox: true,
      sendBox: true,
      clearSms: true,
    },
    smsOperation: {
      tts: true,
      reply: true,
      forward: true,
      resend: true,
      delete: true,
    },
    callConfig: {
      callRecord: true,
      unReceivedCall: true,
      receivedCall: true,
      callOut: true,
      clearCallRecord: true,
    },
    bluetoothConfig: {
      enable: true,
    },
    recordConfig: {
      menuEnable: true,
      recordEnable: true,
      recordFile: true,
      uDiskMode: true,
    },
    deviceConfig: {
      menuEnable: true,
      deviceSetting: true,
      offline: true,
      toneTip: true,
      transmitPower: true,
      backLight: true,
      bootInterface: true,
      keyboardLock: true,
    },
    deviceConfig2: {
      ledIndicator: true,
      quieting: true,
      powerOnPassword: false,
      locale: true,
      soundCtrl: true,
      timeSetting: true,
      locate: true,
      workAlone: true,
    },
    deviceConfig3: {
      upendEnable: true,
      sosRescueEnable: true,
      hideMode: true,
      shakeEnable: true,
      encryptEnable: true,
      denoiseEnable: true,
      channelLockEnable: true,
    },
    deviceInfo: {
      infoMenuEnable: true,
    },
    channelSetting: {
      channelConfigMenuEnable: true,
      receivingFreq: true,
      transmittingFreq: true,
      channelName: true,
      transmitTimeLimit: true,
      subAudioSetting: true,
      launchContact: true,
      colorCode: true,
    },
    timeSlotSetting: {
      timeSlot: true,
      virtualClusterTimeSlot: true,
      receivingList: true,
    },
  }
  const SignalingSystem = {
    remoteConfig: {
      remoteShutDecodeEnable: true,
      remoteMonitorDecode: false,
      urgentRemoteMonitorDecode: false,
      remoteNoticeDecode: false,
      remoteDetectDecode: false,
      remoteEraseDecode: false,
      remoteStunWakeupDecode: true,
    },
    remoteMonitorDuration: 10,
    remoteAlertCount: 10,
    signalingEncrypt: {
      remoteShutEncryptEnable: false,
      remoteMonitorDecodeEnable: false,
      remoteEraseDecodeEnable: false,
      remoteStunDecodeEnable: false,
    },
    signalingPwd: '',
  }
  const AlertConfig = {
    aloneWorkEnable: false,
    responseTime: 10,
    remindTime: 10,
    responseOperation: 0,
    upendEnable: false,
    entryDelay: 10,
    quitDelay: 10,
    upendConfig: {
      preRewindTime: 5,
      triggerTilt: 0,
      triggerMode: 0,
    },
  }

  // 信道数据
  const Channel = {
    // 信道 ID
    chId: 0,
    zoneId: 0,
    chType: 0,
    scanListWrap: 0xff,
    scanList: 0xff,
    scanConfig: {
      listType: 0,
      autoScan: false,
      onlyReceive: false,
      ipSiteConnect: false,
      autoRoam: false,
      allowOfflineSign: false,
    },
    powerConfig: {
      powerType: 1,
      squelchLevel: 3,
    },
    rxFreq: 136000000,
    txFreq: 174000000,

    // 数字信道
    colorCode: 0,
    timeSlotConfig: {
      timeSlot: 0,
      virtualTimeSlot: 0,
      DCDMEnable: false,
      chSlotAdjust: 0,
      priorityInterrupt: false,
    },
    alertConfig: {
      voicePriority: 1,
      emergencyAlertTip: false,
      emergencyAlertConfirm: false,
      emergencyCallTip: false,
      permitConditions: 1,
      singleCallRes: false,
    },
    networkConfig: {
      networking: true,
      localCall: false,
      directMode: false,
      voiceDuplex: false,
      unConfirmSingleCall: false,
      smsUdpCompress: false,
      voiceCallEmbedding: false,
    },
    encryptConfig: {
      enable: false,
      type: 0,
      algorithm: 0,
      encryptKeyRandom: false,
      decryptKeyRandom: false,
      smsEncrypt: false,
    },
    encryptListId: 0xff,
    receiveGroup: 0xff,
    defaultAddress: 0xffff,
    emergencySysId: 0xff,

    // 模拟信道
    funcConfig: {
      bandwidthFlag: 1,
      beatFreq: 0,
      preEmphasis: 0,
      companding: 0,
      scrambleEnable: 0,
      signalingSystem: 0,
      tailCancellation: true,
      ctcssTxDps: 0,
      ctcssRxDps: 0,
      bclFlag: 0,
    },
    scramble: 3300,
    subsonicDecode: 0xffff,
    subsonicEncode: 0xffff,
    rxSignallingSystem: 0,
    txSignallingSystem: 0,

    transmissionLimit: 300,
    totKeyUpdateDelay: 0,
    chName: '',
  }
  // 救援/求救
  const SosCfg = {
    config: {
      enable: true,
      intervalHonking: false,
      flashlight: false,
      sendGps: false,
    },
    intervalHonkingTime: 0,
    flashlightTime: 1,
    rescueScanHandUpTime: 5000,
    highPowerSosTime: 0,
    middlePowerSosTime: 0,
    lowPowerSosTime: 0,
    sosInfo: '',
  }
  let lastClickNodeKey = null
  // 巡逻系统: 跟踪监控: 启用1/0,轮询时间片s,最小距离m
  const TrailCtrl = {
    menuConfig: {
      enable: true,
    },
    rollTime: 300,
    rollDistant: 50,
  }
  // 卫星定位设置
  const GpsSettings = {
    baseConfig: {
      enable: false,
      mode: 1,
    },
    connectionCount: 8,
    pttCount: 10,
    centerId: 1,
    queryCmd: '',
  }
  const FrequencyRange = [
    {
      min: 400,
      max: 480,
    },
    {
      min: 136,
      max: 174,
    },
  ]
  const defaultNodeData = {
    key: Date.now() + '',
    title: 'Unknown',
    icon: false,
    folder: true,
  }
  const pwReg = /[^0-9]/g

  export default {
    name: 'TD920',
    mixins: [commonToolMixin, wfTool],
    data() {
      return {
        selectedDeviceDmrId: '',
        isReading: false,
        isWriting: false,
        menuTreeId: 'TD910MenuTree',
        selectMenu: 'TD910DeviceInfo',

        // 设备信息
        deviceModel: Model,
        deviceWriteInfo: {
          frequencyRange: cloneDeep(FrequencyRange),
          config: {
            denoise: true,
            sdc: true,

            locate: true,
            recording: true,
            upendAlarm: true,
            bluetooth: true,
            tts: true,
            shake: true,
          },
        },
        resourceVersion: {
          verInfo: '',
        },
        // 身份信息
        identityInfo: {},
        // 编程密码
        passwordInfo: cloneDeep(Password),
        // 常规设置
        generalSettings: cloneDeep(GeneralSettings),
        // 按键设置
        buttonDefined: cloneDeep(ButtonDefined),
        // 短信
        refSms: 'shortMessage',
        smsMaxSize: 100,
        smsContent: [],
        // 加密配置
        encryptConfig: cloneDeep(EncryptConfig),
        // 加密列表 最大数量: 32
        encryptListLimit: 32,
        encryptXORList: [],
        encryptARC4List: [],
        encryptAES256List: [],
        // 菜单设置
        menuSettings: cloneDeep(MenuSettings),
        // 卫星定位
        gpsSettings: cloneDeep(GpsSettings),
        chConfigPwdMode: 0,
        usbFlashDiskPasswordMode: 0,
        // 信令系统
        signalingSystem: cloneDeep(SignalingSystem),
        // 警报配置
        alertConfig: cloneDeep(AlertConfig),
        // 数字报警
        digitalAlertList: [],
        currDigitalAlertId: -1,
        digitalAlertListLimit: 32,
        // 数字通讯录
        selectedAddressBook: [],
        originAddressBook: [],
        addrBookTreeId: 'TD910AddressBookTree',
        // 数字通讯录群组
        addressBookGroup: [],
        currAddressBookGroupId: 0,
        addressBookGroupLimit: 10,
        // 电话本
        phoneBookTreeId: 'TD910PhoneBook',
        phoneBook: [],
        // 接收组
        rxGroupList: [],
        refReceiveGroup: 'receiveGroup',
        // 信道
        channelDataList: [],
        channelDataListCache: [],
        oneZoneLeafChannelLimit: 3773,
        oneZoneChannelLimit: 64,
        currChannelId: 0,
        oneChannel: this.getDefaultChannel(),
        // 频率偏移值
        freqOffset: 10,

        // 一级区域
        zoneRootDataList: [],
        zoneRootLimit: 4,
        currZoneRootId: -1,
        // 二级区域
        zoneParentDataList: [],
        zoneParentLimit: 1000,
        currZoneParentId: -1,
        // 三级区域
        zoneLeafDataList: [],
        zoneLeafLimit: 2000,
        currZoneLeafId: -1,
        // 扫描组
        scanGroup: [],
        currScanGroupId: 0,
        scanGroupLimit: 32,
        // 漫游
        roamGroup: [],
        currRoamGroupId: 0,
        roamGroupLimit: 32,
        // 系统功能巡查配置
        patrolConfig: {},
        // 系统功能紧急报警,
        emergencyAlarm: {
          alarmConfig: {
            alarmEnable: true,
            alarmType: 0,
          },
        },
        patrolSystemTabsValue: 'configure',
        trailCtrl: cloneDeep(TrailCtrl),
        // 救援/求救配置
        sosCfg: cloneDeep(SosCfg),
        defaultChannelNames: ['145.5MHz', '435.5MHz'],
        sosChannelList: [],
        sosChannelId: 0,
        sosChannel: {},
      }
    },
    methods: {
      scanListWrapChange(val) {
        // 高位字节: listType 低位字节 : 扫描/漫游列表的id
        this.oneChannel.scanConfig.listType = val >>> 8
        this.oneChannel.scanList = val & 0xff
        this.$forceUpdate()
      },
      // 加密类型，0 基础，1 高级 default = 0
      encryptTypeChange(type) {
        this.oneChannel.encryptConfig.algorithm = 0

        // 类型变更为基础类型，则重置密钥列表
        // 变更为高级类型，则根据算法重置为ARC4或AES256
        if (type === 0) {
          this.oneChannel.encryptListId = this.encryptXORList[0]?.keyId ?? 0xff
        } else if (this.oneChannel.encryptConfig.algorithm === 0) {
          this.oneChannel.encryptListId = this.encryptARC4List[0]?.keyId ?? 0xff
        } else {
          this.oneChannel.encryptListId = this.encryptAES256List[0]?.keyId ?? 0xff
        }
      },
      // 菜单配置的配置开关变更事件
      deviceAllConfigMenuEnableChange(menuEnable) {
        this.menuSettings.deviceConfig.deviceSetting = menuEnable
        this.menuSettings.deviceInfo.infoMenuEnable = menuEnable
        this.menuSettings.channelSetting.channelConfigMenuEnable = menuEnable
      },
      // 菜单配置的对讲机设置开关变更事件
      deviceConfigMenuEnableChange(menuEnable) {
        if (menuEnable || !this.menuSettings.deviceInfo.infoMenuEnable) {
          this.menuSettings.deviceConfig.menuEnable = menuEnable
        }
      },
      // 对讲机信息菜单变更事件
      deviceInfoMenuEnableChange(menuEnable) {
        if (menuEnable || !this.menuSettings.deviceConfig.deviceSetting) {
          this.menuSettings.deviceConfig.menuEnable = menuEnable
        }
      },
      gpsSettingsQueryCmdInputEvent,
      buttonDefinedCallIdChange(row, callId) {
        if (callId === 0xffff) {
          row.callType = CallType.GROUP
          row.smsId = 0xff
        }
      },
      buttonDefinedCallTypeChange(row, callType) {
        if (callType === SoftKeyCallType.MSG) {
          row.smsId = this.smsContent[0]?.msgId ?? 0xff
        } else {
          row.smsId = 0xff
        }
      },
      defaultChannelZoneRootIdChange(row, rootId) {
        if (rootId === 0xffff) {
          row.parentId = 0xffff
          row.zoneId = 0xffff
          row.channelId = 0xffff
        }
      },
      defaultChannelZoneParentIdChange(row, parentId) {
        if (parentId === 0xffff) {
          row.zoneId = 0xffff
          row.channelId = 0xffff
        }
      },
      defaultChannelZoneIdChange(row, zoneId) {
        if (zoneId === 0xffff) {
          row.channelId = 0xffff
        }
      },
      // 同步本机时间
      syncTimeChange(val) {
        if (!val) return

        const now = new Date()
        const timeZone = getTimeZoneOffsetObject()
        Object.assign(this.generalSettings, timeZone, {
          year: now.getUTCFullYear(),
          month: now.getUTCMonth() + 1,
          day: now.getUTCDate(),
          hour: now.getUTCHours() + getTimeZoneOffsetObject().timeZoneHour,
          minute: now.getUTCMinutes(),
          second: now.getUTCSeconds(),
        })
      },
      passwordModeChange(target, pwdProp, mode) {
        // 禁用或独立设置,都清除密码, 否则为同开机密码
        target[pwdProp] = mode === 2 ? this.menuSettings.powerOnPwd : ''
      },
      fixPowerOnValue(value) {
        this.menuSettings.powerOnPwd = fixPowerOnPassword(value, pwReg)
      },
      fixPowerValueOnBlur() {
        this.fixPowerOnValue(this.menuSettings.powerOnPwd)
        const powerOnPwd = this.menuSettings.powerOnPwd
        this.menuSettings.powerOnPwd = powerOnPwd.length === 6 ? powerOnPwd : ''

        // 处理信道配置密码和U盘密码, 无效开机密码, 重置关联的密码
        if (this.menuSettings.powerOnPwd.length < 6) {
          if (this.chConfigPwdMode === 2) {
            this.menuSettings.chConfigPwd = ''
            this.chConfigPwdMode = 0
          }
          if (this.usbFlashDiskPasswordMode === 2) {
            this.generalSettings.usbFlashDiskPassword = ''
            this.usbFlashDiskPasswordMode = 0
          }
        } else {
          // 密码可能变更,需要同步关联的密码
          if (this.chConfigPwdMode === 2) {
            this.menuSettings.chConfigPwd = this.menuSettings.powerOnPwd
          }
          if (this.usbFlashDiskPasswordMode === 2) {
            this.generalSettings.usbFlashDiskPassword = this.menuSettings.powerOnPwd
          }
        }
      },
      fixChConfigPwd(value) {
        this.menuSettings.chConfigPwd = fixPowerOnPassword(value, pwReg)
      },
      fixUsbFlashDiskPassword(value) {
        this.generalSettings.usbFlashDiskPassword = fixPowerOnPassword(value, pwReg)
      },
      frequencyHz2Mhz: bfutil.frequencyHz2Mhz,
      frequencyMhz2Hz: bfutil.frequencyMhz2Hz,
      fixMd5KeyValue(value) {
        this.passwordInfo.md5Key = fixProgramPassword(value)
      },
      initSosChannelList() {
        const channel = this.getDefaultChannel()
        this.sosChannelList = this.defaultChannelNames.map((name, index) => {
          const sosChannel = cloneDeep(channel)
          sosChannel.chName = name
          sosChannel.chId = index
          sosChannel.rxFreq = sosChannel.txFreq = bfutil.frequencyMhz2Hz(name.slice(0, -3))
          return sosChannel
        })
        this.sosChannel = this.sosChannelList[this.sosChannelId]
      },
      sosChannelNameChanged(val) {
        if (!val) {
          this.sosChannel.chName = `${this.defaultChannelNames[this.sosChannel.chId]}`
        }

        this.updateNodeTitle({
          key: this.generateNodeKey(this.getTableIndexLabel(TableIndex.SosChData), this.sosChannel.chId),
          title: this.sosChannel.chName,
          data: this.sosChannel,
        })
      },
      // 切换选中的设备时，同步界面中的配置数据
      mergeGeneralSettings(device) {
        this.generalSettings = Object.assign(this.generalSettings, {
          deviceName: device.selfId,
          intDmrId: parseInt(device.dmrId, 16),
        })
      },
      getOldChannelFromCache(chId) {
        for (let i = 0; i < this.channelDataListCache.length; i++) {
          const data = this.channelDataListCache[i]
          if (data.chId === chId) {
            return cloneDeep(data)
          }
        }
        return {}
      },
      initChannels() {
        // 如果没有合法有效的信道，则向用户提示
        if (!this.selectedChannels.length) {
          this.$nextTick(() => {
            bfNotify.messageBox(this.$t('writeFreq.notHaveValidChannel'), 'error')
          })
          return
        }

        // 生成信道与对应的三级区域数据
        const zoneLeafDataMap = {}
        const zoneLeafNodes = {}
        const channelsMap = {}
        const zoneConfigCache = {}
        // 默认最小频率
        const minFreq = this.frequencyMhz2Hz(this.deviceWriteInfo.minFrequency)

        for (let i = 0; i < this.selectedChannels.length; i++) {
          const channel = this.selectedChannels[i]
          // 缓存信道区域配置，同一个区域配置只查找一次
          const zoneLeaf = zoneConfigCache[channel.zoneRid] || bfglob.gchannelZone.get(channel.zoneRid)
          if (!zoneLeaf || zoneLeaf.zoneLevel !== 4) {
            continue
          }
          zoneConfigCache[channel.zoneRid] = zoneLeaf

          // 生成三级区域数据
          let zoneLeafData = zoneLeafDataMap[channel.zoneRid]
          if (!zoneLeafData) {
            zoneLeafData = this.leafZoneComp?.addOneZoneData()
          }
          // 每个三级区域最多有64个信道
          if (!zoneLeafData || zoneLeafData.usedList.length >= this.oneZoneChannelLimit) {
            continue
          }

          // 生成信道的节点数据
          if (!this.zoneLeafChannelComp) {
            continue
          }
          let channelData = this.zoneLeafChannelComp.addOneChannel()
          if (!channelData) {
            continue
          }
          const chId = channel.no - 1
          // 判断信道ID是否超出机型的信道范围
          if (chId >= this.oneZoneLeafChannelLimit) {
            continue
          }
          // 合并缓存参数
          channelData = merge(channelData, this.getOldChannelFromCache(chId) || cloneDeep(this.oneChannel))
          // 重置信道ID和名称
          channelData.chId = chId
          channelData.chName = `${this.$t('dialog.channel')} ${channel.no}`
          // 设置接收组和发射组ID
          channelData.receiveGroup = this.getRxGroupId(channelData.chId)
          channelData.defaultAddress = this.getDefaultAddress(channel.sendGroup)
          // 重置信道默认频率
          channelData.rxFreq = minFreq || channelData.rxFreq
          channelData.txFreq = minFreq || channelData.txFreq

          // 设置三级区域管辖信道数据参数
          zoneLeafData.usedList.push(channelData.chId)
          zoneLeafData.usedFlag = this.setZoneChFlag(zoneLeafData.usedList)
          this.setZoneDataName(zoneLeafData, zoneLeaf)

          // 缓存三级区域的ID到信道中
          channelData.zoneId = zoneLeafData.zoneId
          channelData.zoneRid = channel.zoneRid

          // 生成fancytree节点
          if (!zoneLeafNodes[channel.zoneRid]) {
            zoneLeafNodes[channel.zoneRid] = this.createZoneLeafNodeData(zoneLeafData)
          }
          const zoneLeafNode = zoneLeafNodes[channel.zoneRid]
          zoneLeafNode.children.push(this.createChannelNodeData(channelData))

          // 缓存三级区域和信道数据
          zoneLeafDataMap[channel.zoneRid] = zoneLeafData
          channelsMap[channelData.chId] = channelData
        }

        const showData = this.channelDataList.find(data => data.chId === this.currChannelId)
        if (showData) {
          this.oneChannel = showData
        }

        // 生成三级区域对应的二级区域数据
        const zoneParentDataMap = {}
        const zoneParentNodes = {}
        const zoneLeafDataMapKeys = Object.keys(zoneLeafDataMap)
        for (let i = 0; i < zoneLeafDataMapKeys.length; i++) {
          const key = zoneLeafDataMapKeys[i]
          const zoneLeafData = zoneLeafDataMap[key]

          // 未生成二级区域节点，则先创建
          const zoneParent = zoneLeafData.origin.zoneParent
          let zoneParentData = zoneParentDataMap[zoneParent]
          if (!zoneParentData) {
            const zoneParentOriginData = bfglob.gchannelZone.get(zoneParent)
            // 未查找到本地二级区域数据，跳过创建
            if (!zoneParentOriginData || zoneParentOriginData.zoneLevel !== 3) {
              continue
            }

            zoneParentData = this.parentZoneComp.addOneZoneData()
            if (!zoneParentData) {
              continue
            }
            this.setZoneDataName(zoneParentData, zoneParentOriginData)
            zoneParentDataMap[zoneParent] = zoneParentData

            // 生成fancytree节点
            if (!zoneParentNodes[zoneParent]) {
              zoneParentNodes[zoneParent] = this.createZoneParentNodeData(zoneParentData)
            }
          }

          // 同步子级区域的上下级关系ID
          zoneLeafData.parentId = zoneParentData.zoneId
          // 关联子级区域fancytree节点
          const zoneParentNode = zoneParentNodes[zoneParent]
          zoneParentNode.children.push(zoneLeafNodes[zoneLeafData.origin.rid])
        }

        // 生成二级区域对应的一级区域数据
        const zoneRootDataMap = {}
        const zoneRootNodes = {}
        const zoneParentDataMapKeys = Object.keys(zoneParentDataMap)
        for (let i = 0; i < zoneParentDataMapKeys.length; i++) {
          const key = zoneParentDataMapKeys[i]
          const zoneParentData = zoneParentDataMap[key]

          // 未生成一级区域节点，则先创建
          const zoneParent = zoneParentData.origin.zoneParent
          let zoneRootData = zoneRootDataMap[zoneParent]
          if (!zoneRootData) {
            const zoneRootOriginData = bfglob.gchannelZone.get(zoneParent)
            // 未查找到本地一级区域数据，跳过创建
            if (!zoneRootOriginData || zoneRootOriginData.zoneLevel !== 2) {
              continue
            }

            zoneRootData = this.rootZoneComp.addOneZoneData()
            if (!zoneRootData) {
              continue
            }
            this.setZoneDataName(zoneRootData, zoneRootOriginData)
            zoneRootDataMap[zoneRootOriginData.rid] = zoneRootData

            // 生成fancytree节点
            if (!zoneRootNodes[zoneRootOriginData.rid]) {
              zoneRootNodes[zoneRootOriginData.rid] = this.createZoneRootNodeData(zoneRootData)
            }
          }

          // 同步子级区域的上下级关系ID
          zoneParentData.rootId = zoneRootData.zoneId
          // 关联子级区域fancytree节点
          const zoneRootNode = zoneRootNodes[zoneParent]
          zoneRootNode.children.push(zoneParentNodes[zoneParentData.origin.rid])
        }
        // 同步三级区域的一级区域ID
        for (let i = 0; i < zoneLeafDataMapKeys.length; i++) {
          const key = zoneLeafDataMapKeys[i]
          const zoneLeafData = zoneLeafDataMap[key]
          const zoneParentData = zoneParentDataMap[zoneLeafData.origin.zoneParent]
          if (!zoneParentData) {
            continue
          }
          zoneLeafData.rootId = zoneParentData.rootId
        }

        // 加载fancytree节点数据到DOM元素上
        // 信道设置最顶级根节点，如果不存在，则无法加载所有区域、信道节点
        const channelRootNode = this.getNodeByKey(this.getTableIndexLabel(TableIndex.Channel) + '_root')
        if (!channelRootNode) {
          return
        }
        this.addNodeChildren(
          channelRootNode,
          Object.keys(zoneRootNodes).map(key => {
            return zoneRootNodes[key]
          })
        )
      },
      filterScanListChannelId() {
        const channelIdList = this.currentChannelIdList
        this.scanGroup = this.scanGroup.map(item => {
          item.membersList = filterChannelIdWhenDeviceChanged(channelIdList, item.membersList)
          item.channelCount = item.membersList.length
          return item
        })
      },
      filterRoamListChannelId() {
        const channelIdList = this.currentChannelIdList
        this.roamGroup = this.roamGroup.map(item => {
          item.roamChList = filterChannelIdWhenDeviceChanged(channelIdList, item.roamChList)
          item.roamChCount = item.roamChList.length
          return item
        })
      },
      resetDigitalAlarmReplyChannel() {
        const channelIdList = this.currentChannelIdList
        this.digitalAlertList = this.digitalAlertList.map(data => {
          data.replyChannel = resetDigitalAlertReplyChannel(channelIdList, data.replyChannel)
          return data
        })
      },
      async syncDeviceDataIntoConfig(device) {
        this.mergeGeneralSettings(device)

        await this.initRxGroupList()
        // 区域、信道
        this.initChannels()
        // // 扫描组过滤不存在的信道ID
        this.filterScanListChannelId()
        // 漫游组过滤不存在的信道ID
        this.filterRoamListChannelId()
        // 数字报警重置回复信道参数
        this.resetDigitalAlarmReplyChannel()

        this.$nextTick(() => {
          if (!lastClickNodeKey) {
            return
          }
          this.manuallyTriggerNodeClick(this.getNodeByKey(lastClickNodeKey))
        })
      },
      // 清除通讯录被禁用的状态
      clearTreeNodeUnselectable() {
        // 清除通讯录被禁用和非系统通讯数据的节点
        this.addressBookComp && this.addressBookComp.removeNotInSystemNodes()
        this.selectedAddressBook.forEach(item => {
          bftree.nodeUnselectable(this.addrBookTreeId, item.nodeKey)
        })
      },
      clearPrivateConfig() {
        // 清除常规设置的设备名称
        this.generalSettings.deviceName = ''
        this.generalSettings.intDmrId = 0
        // 接收组
        this.receiveGroupComp && this.receiveGroupComp.resetRxGroupList()
        // 区域
        this.initZoneDataList()
        // 信道
        this.initChannelDataList()
        this.clearTreeNodeUnselectable()
        if (this.phoneBookComp) {
          this.phoneBookComp.removeNotInSystemNodes()
        }
      },
      // cleanAll 标记是否清除全部配置
      async clearDeviceDataConfig(cleanAll = false) {
        // 必须清除的数据，接收组、区域、信道等私有数据，包括一些标记参数
        this.clearPrivateConfig()

        this.initScanGroupList()
        this.initRoamGroupList()
        this.initDigitAlertList()

        // 可选的清除数据，常规设置、菜单、按键定义、警报、通讯录、电话本、短信等通用数据
        if (!cleanAll) {
          return
        }
        this.originAddressBook = []
        this.selectedAddressBook = []
        this.addressBookComp && this.addressBookComp.treeReload(true)
        this.initAddressBookGroup()
        this.phoneBook = []
        this.phoneBookComp && this.phoneBookComp.treeReload(true)
      },
      initSmsData() {
        this.smsContent = []
      },
      initEncryptConfig() {
        this.encryptConfig = cloneDeep(EncryptConfig)
        this.encryptXORList = []
        this.encryptARC4List = []
        this.encryptAES256List = []
      },
      initRxGroupList() {
        if (!this.receiveGroupComp) {
          return []
        }
        return this.receiveGroupComp.initRxGroupList(this.selectedChannels)
      },
      initScanGroupList() {
        // 删除节点，通过父节点，直接删除所有子节点数据
        const parentNodeKey = this.getTableIndexLabel(TableIndex.Scan)
        this.removeMenuTreeNodeChildren(parentNodeKey)
        // 重新初始化数据
        if (this.scanGroupComp) {
          this.scanGroupComp.initDataList()
        }
      },
      initRoamGroupList() {
        // 删除节点
        const parentNodeKey = this.getTableIndexLabel(TableIndex.Roam)
        this.removeMenuTreeNodeChildren(parentNodeKey)
        // 重新初始化数据
        if (this.roamGroupComp) {
          this.roamGroupComp.initDataList()
        }
      },
      initDigitAlertList() {
        // 删除节点
        const parentNodeKey = this.getTableIndexLabel(TableIndex.DigitalAlert)
        this.removeMenuTreeNodeChildren(parentNodeKey)
        // 重新初始化数据
        if (this.digitalAlertComp) {
          this.digitalAlertComp.initDigitalAlert()
        }
      },
      initZoneDataList() {
        if (this.rootZoneComp) {
          this.rootZoneComp.initZoneData()
        }
        if (this.parentZoneComp) {
          this.parentZoneComp.initZoneData()
        }
        if (this.leafZoneComp) {
          this.leafZoneComp.initZoneData()
        }
      },
      initChannelDataList() {
        if (this.channelDataList.length) {
          this.channelDataListCache = cloneDeep(this.channelDataList)
        }
        // 删除信道菜单节点下所有子节点
        const parentNodeKey = this.getTableIndexLabel(TableIndex.Channel) + '_root'
        this.removeMenuTreeNodeChildren(parentNodeKey)
        // 重新初始化数据
        if (this.zoneLeafChannelComp) {
          this.zoneLeafChannelComp.initDataList()
        }
      },
      initAddressBookGroup() {
        // 删除节点
        const parentNodeKey = this.getTableIndexLabel(TableIndex.AddressGroup)
        this.removeMenuTreeNodeChildren(parentNodeKey)
        // 重新初始化数据
        if (this.addressBookGroupComp) {
          this.addressBookGroupComp.initDataList()
        }
      },
      initPatrolConfig(flag = false) {
        if (!flag && this.patrolConfigComp) {
          this.patrolConfigComp.initData()
        }
      },
      initEmergencyAlarm(flag = false) {
        if (!flag && this.emergencyAlarmComp) {
          this.emergencyAlarmComp.initData()
        }
      },
      removeMenuTreeNodeChildren(key) {
        const parentNode = this.getNodeByKey(key)
        if (parentNode) {
          parentNode.expanded = true
          parentNode.removeChildren()
          this.updateViewport()
        }
      },

      // 区域数据
      loadZoneNodes({ dataList = [], parentKeyPrefix = '', parentKeyProp, createNode }) {
        if (typeof createNode !== 'function' || !dataList.length) {
          return
        }

        const zonesMap = {}
        for (let i = 0; i < dataList.length; i++) {
          const data = dataList[i]
          let parentKey = parentKeyPrefix
          if (typeof parentKeyProp !== 'undefined') {
            parentKey += `:${data[parentKeyProp]}`
          }
          if (!zonesMap[parentKey]) {
            zonesMap[parentKey] = []
          }
          zonesMap[parentKey].push(createNode(data))
        }

        const parentNodeCache = {}
        const zonesMapKeys = Object.keys(zonesMap)
        for (let i = 0; i < zonesMapKeys.length; i++) {
          const key = zonesMapKeys[i]
          const parentNode = parentNodeCache[key] || (parentNodeCache[key] = this.getNodeByKey(key))
          if (!parentNode) {
            continue
          }
          this.addNodeChildren(parentNode, zonesMap[key])
        }
      },
      setZoneDataName(target, data) {
        target.zoneName = data.zoneTitle
        target.origin = cloneDeep(data)
      },
      zoneDataDblclick(key) {
        bftree.gotoNodeLocation(this.menuTreeId, key, { isActive: true })
      },
      zoneRootDataDblclick(row) {
        this.zoneDataDblclick(this.generateNodeKey(this.getTableIndexLabel(TableIndex.ZoneRoot), row.zoneId))
      },
      createZoneRootNodeData(data) {
        const parentNodeKey = this.getTableIndexLabel(TableIndex.ZoneRoot)
        const key = this.generateNodeKey(parentNodeKey, data.zoneId)
        return {
          title: data.zoneName,
          folder: true,
          expanded: true,
          key: key,
          icon: false,
          selected: false,
          origin: data,
          children: [],
        }
      },
      loadZoneRootNodes(dataList = this.zoneRootDataList) {
        this.loadZoneNodes({
          dataList,
          parentKeyPrefix: this.getTableIndexLabel(TableIndex.Channel) + '_root',
          createNode: this.createZoneRootNodeData,
        })
      },
      zoneParentDataDblclick(row) {
        this.zoneDataDblclick(this.generateNodeKey(this.getTableIndexLabel(TableIndex.ZoneParent), row.zoneId))
      },
      createZoneParentNodeData(data) {
        const parentNodeKey = this.getTableIndexLabel(TableIndex.ZoneParent)
        const key = this.generateNodeKey(parentNodeKey, data.zoneId)
        return {
          title: data.zoneName,
          folder: true,
          expanded: true,
          key: key,
          icon: false,
          selected: false,
          origin: data,
          children: [],
        }
      },
      loadZoneParentNodes(dataList = this.zoneParentDataList) {
        this.loadZoneNodes({
          dataList,
          parentKeyPrefix: this.getTableIndexLabel(TableIndex.ZoneRoot),
          parentKeyProp: 'rootId',
          createNode: this.createZoneParentNodeData,
        })
      },
      zoneLeafDataDblclick(row) {
        this.zoneDataDblclick(this.generateNodeKey(this.getTableIndexLabel(TableIndex.ZoneLeaf), row.zoneId))
      },
      createZoneLeafNodeData(data) {
        const parentNodeKey = this.getTableIndexLabel(TableIndex.ZoneLeaf)
        const key = this.generateNodeKey(parentNodeKey, data.zoneId)
        return {
          title: data.zoneName,
          folder: true,
          expanded: true,
          key: key,
          icon: false,
          selected: false,
          origin: data,
          children: [],
        }
      },
      loadZoneLeafNodes(dataList = this.zoneLeafDataList) {
        this.loadZoneNodes({
          dataList,
          parentKeyPrefix: this.getTableIndexLabel(TableIndex.ZoneParent),
          parentKeyProp: 'parentId',
          createNode: this.createZoneLeafNodeData,
        })
      },

      // 信道设置
      getDefaultChannel(type = 0) {
        const channel = cloneDeep(Channel)
        channel.chName = `${this.$t('dialog.channel')} 1`

        return channel
      },
      zoneLeafDataRowDblclick(row) {
        const showChannel = () => {
          this.setChannelData(row)
          this.zoneDataDblclick(this.generateNodeKey(this.getTableIndexLabel(TableIndex.Channel), row.chId))
        }
        if (row.chId === this.oneChannel.chId) {
          showChannel()
          return
        }
        // 如果信道设置的数据在信道列表中，则需要验证表单通过后再切换信道
        this.validateOneChannel()
          .then(() => {
            showChannel()
          })
          .catch(() => {
            // 提示对应的三级区域有信道未命名
            const zoneLeafData = this.zoneLeafDataIndex[this.oneChannel.zoneId]
            if (zoneLeafData) {
              bfNotify.messageBox(
                this.$t('writeFreq.correctChDataTip', {
                  name: zoneLeafData.zoneName,
                }),
                'warning'
              )
            }
          })
      },
      createChannelNodeData(data) {
        const parentNodeKey = this.getTableIndexLabel(TableIndex.Channel)
        const key = this.generateNodeKey(parentNodeKey, data.chId)
        return {
          title: data.chName,
          folder: false,
          expanded: false,
          key: key,
          icon: false,
          selected: false,
          origin: data,
        }
      },
      loadChannelNodes(dataList = this.channelDataList) {
        this.loadZoneNodes({
          dataList,
          parentKeyPrefix: this.getTableIndexLabel(TableIndex.ZoneLeaf),
          parentKeyProp: 'zoneId',
          createNode: this.createChannelNodeData,
        })
      },
      updateOneChannelNode(data) {
        this.updateNodeTitle({
          key: this.generateNodeKey(this.getTableIndexLabel(TableIndex.Channel), data.chId),
          title: data.chName,
          data,
        })
      },
      chNameChanged(val) {
        if (!val) {
          this.oneChannel.chName = `${this.$t('dialog.channel')} ${this.oneChannel.chId + 1}`
        }
        this.updateOneChannelNode(this.oneChannel)
      },
      chTypeChanged(val) {
        // 非数字信道，有亚音频参数，需要重置为默认值
        if (val === 0) {
          this.oneChannel.subsonicDecode = 0xffff
          this.oneChannel.subsonicEncode = 0xffff
        } else if (val >= 1) {
          this.oneChannel.subsonicDecode = 0xffff
          this.oneChannel.subsonicEncode = 0x1514

          // 模拟信道，取消IP站点连接
          this.oneChannel.scanConfig.ipSiteConnect = false
        }
      },
      getFreqRangeMaxValue(freq, unsigned = true) {
        for (let i = 0; i < this.deviceWriteInfo.frequencyRange.length; i++) {
          const range = this.deviceWriteInfo.frequencyRange[i]
          const min = bfutil.frequencyMhz2Hz(range.min)
          const max = bfutil.frequencyMhz2Hz(range.max)
          if (freq >= min && freq <= max) {
            return unsigned ? max : min
          }
        }
        return freq
      },
      subsonicDecodeChange(val) {
        this.oneChannel.subsonicDecode = this.encodeSubsonic2String(val)
      },
      subsonicEncodeChange(val) {
        this.oneChannel.subsonicEncode = this.encodeSubsonic2String(val)
      },
      setChannelData(data) {
        this.oneChannel = data
        // 纯模拟信道可自定义参数，为确保参数正确显示，先将参数设置为0,再重置
        if (this.oneChannel.chType === 1) {
          // 纯模拟信道可自定义参数，需要将值转换成字符串显示
          let subsonicDecode = this.oneChannel.subsonicDecode
          let subsonicEncode = this.oneChannel.subsonicEncode
          const codeList = this.subtoneCodeDataList
          if (codeList.filter(item => item.value === subsonicDecode).length === 0 && typeof subsonicDecode === 'number') {
            subsonicDecode = this.decodeSubsonic(subsonicDecode)
          }
          if (codeList.filter(item => item.value === subsonicEncode).length === 0 && typeof subsonicEncode === 'number') {
            subsonicEncode = this.decodeSubsonic(subsonicEncode)
          }

          this.oneChannel.subsonicDecode = 0
          this.oneChannel.subsonicEncode = 0
          this.$nextTick(() => {
            this.oneChannel.subsonicDecode = subsonicDecode
            this.oneChannel.subsonicEncode = subsonicEncode
          })
        }
        this.oneChannel.scanListWrap = (this.oneChannel.scanConfig.listType << 8) + this.oneChannel.scanList
      },
      validateOneChannel() {
        if (!this.channelSettingComp) {
          return Promise.reject()
        }
        return this.channelSettingComp.validate()
      },
      nextId(usedId, limit = 32) {
        let id = 0
        while (id < limit) {
          if (!usedId.includes(id)) {
            return id
          }
          id++
        }
        return id
      },

      // 按键设置
      getKeyName(index) {
        const names = {
          0: this.$t('writeFreq.customKey', { name: 'S1' }),
          1: this.$t('writeFreq.customKey', { name: 'S2' }),
          2: this.$t('writeFreq.customKey', { name: 'P1' }),
          3: this.$t('writeFreq.customKey', { name: 'P2' }),
        }
        return names[index] || ''
      },
      syncLongPressDefine(row) {
        // 如果短按是紧急模式开启,则长按自动设置为紧急模式关闭
        if (row.short === ButtonKeys.WARNING_ON) {
          row.long = ButtonKeys.WARNING_OFF
        } else if (row.long === ButtonKeys.WARNING_OFF) {
          row.long = ButtonKeys.NONE
        }
      },
      getSoftKeyFuncDefine(type = 0) {
        let unusedList = []
        switch (type) {
          case 0:
            unusedList = [ButtonKeys.WARNING_OFF, ButtonKeys.LONGMONI]
            break
          case 1:
            unusedList = [ButtonKeys.WARNING_ON]
            break
        }
        return this.softKeyFuncDefine.filter(item => {
          return !unusedList.includes(item.value)
        })
      },

      // 通讯录
      getSelectedAddress(id) {
        return this.selectedAddressBook.find(item => item.id === id)
      },
      getSoftKeyCallTypeList(data) {
        const list = []
        const address = this.getSelectedAddress(data.callId)
        if (!address) {
          return []
        }
        const hasSms = this.smsContent.length > 0
        if (hasSms) {
          list.push(SoftKeyCallType[SoftKeyCallType.MSG])
        }
        if (address.callType === CallType.SINGLE) {
          list.push(SoftKeyCallType[SoftKeyCallType.SINGLE])
          list.push(SoftKeyCallType[SoftKeyCallType.TIP])
          if (data.callType === SoftKeyCallType.GROUP) {
            data.callType = SoftKeyCallType.SINGLE
          }
        } else {
          list.push(SoftKeyCallType[SoftKeyCallType.GROUP])
        }

        return list.map(key => {
          return {
            label: this.$t(`writeFreq.softKeyCallType.${key}`),
            value: SoftKeyCallType[key],
          }
        })
      },
      detectButtonDefined() {
        const fixedFn = item => {
          // 如果对应的通讯录对象不存在，则将按键功能重置为默认值
          // 从缓存通讯录中查找的通讯录记录不在新的通讯录列表中，则重置按键功能
          const contactCache = this.getAddressBookFromCache(item.callId)
          if (!contactCache) {
            this.resetButtonDefined(item)
            return
          }
          const contact = this.getSelectedAddressByDmrId(contactCache.dmrId)
          if (!contact) {
            this.resetButtonDefined(item)
            return
          }
          // 如果存在但ID已经变更，则更新按键功能通讯录ID
          if (contactCache.id !== contact.id) {
            item.callId = contact.id
          }
        }
        this.buttonDefined.singleKeyCall.forEach(item => fixedFn(item))
        this.buttonDefined.longPressNumCallTable.forEach(item => fixedFn(item))
      },
      selectAddressBooks(books) {
        this.selectedAddressBook = books
        // 通讯录变化时，检测按键定义中单键呼叫功能的设置
        this.detectButtonDefined()
      },

      // 电话簿
      selectPhoneBooks(books) {
        this.phoneBook = books
      },
      getOriginAddressBook(id) {
        return this.originAddressBook.find(item => item.id === id)
      },
      getAddressNameByDmrId(dmrId) {
        // 从读取回来的通讯录中查找对应的dmrId的通讯录名称
        const item = this.originAddressBook.find(item => item.dmrId === dmrId)
        if (item) return item.name

        // 在通讯录中无法找到数据，则从本地的数据中查找
        const org = bfglob.gorgData.getDataByIndex(dmrId)
        return org ? org.orgShortName : ''
      },
      getSelectedAddressByDmrId(dmrId) {
        return this.selectedAddressBook.find(item => item.dmrId === dmrId)
      },
      getDefaultAddress(dmrId) {
        const address = this.getSelectedAddressByDmrId(dmrId)
        return address ? address.id : 0xffff
      },
      resetButtonDefined(target) {
        target.callId = 0xffff
        target.callType = SoftKeyCallType.GROUP
        target.smsId = 0xff

        return target
      },
      getAddressBookFromCache(addrId) {
        return this.originAddressBook.find(item => item.id === addrId)
      },
      getSmsById(msgId) {
        for (let i = 0; i < this.smsContent.length; i++) {
          const sms = this.smsContent[i]
          if (sms.msgId === msgId) {
            return sms
          }
        }
        return undefined
      },
      detectButtonDefinedFromSmsChange() {
        const fixedFn = item => {
          if (item.callType !== SoftKeyCallType.MSG) {
            return
          }
          if (this.getSmsById(item.smsId)) {
            return
          }

          // 如果按键定义中的短信ID对应的短信内容已经不存在，则使用第一个短信，
          if (this.smsContent.length > 0) {
            item.smsId = this.smsContent[0].msgId
            return
          }

          // 如果都没有短信数据，则按键功能短信ID重置为默认值
          item.smsId = 0xff

          // 如果对应的通讯录对象不存在，则将按键功能重置为默认值
          const contact = this.getSelectedAddress(item.callId)
          if (!contact) {
            this.resetButtonDefined(item)
            return
          }

          // 重置按键功能类型参数
          if (contact.callType === CallType.SINGLE) {
            item.callType = SoftKeyCallType.SINGLE
          } else {
            item.callType = SoftKeyCallType.GROUP
          }
        }
        this.buttonDefined.singleKeyCall.forEach(item => fixedFn(item))
        this.buttonDefined.longPressNumCallTable.forEach(item => fixedFn(item))
      },

      /* 菜单树节点操作功能 */
      updateNodeTitleFromLocaleChange() {
        const updateNodeTitle = nodeData => {
          const node = this.menuTreeRef.getNodeByKey(nodeData.key)
          if (!node) {
            return
          }
          node.setTitle(nodeData.title)
          if (nodeData.children) {
            for (let i = 0; i < nodeData.children.length; i++) {
              updateNodeTitle(nodeData.children[i])
            }
          }
        }
        for (let i = 0; i < this.menuTreeSource.length; i++) {
          updateNodeTitle(this.menuTreeSource[i])
        }
      },
      treeLoaded() {
        bftree.gotoNodeLocation(this.menuTreeId, this.getTableIndexLabel(TableIndex.TD920DeviceInfo), { isActive: true })
      },
      menuTreeNodeClick(event, data) {
        const node = data.node
        if (!node) {
          return
        }
        lastClickNodeKey = node.key
        const cNodeKeys = this.hasChildrenNodeKeys
        let [parentNodeKey, dataId] = node.key.split(':')
        this.selectMenu = parentNodeKey
        // 没有数据的ID，为基础的菜单节点
        if (typeof dataId === 'undefined') {
          // 再判断是否为动态数据节点的父节点，如果是则该节点不显示组件
          if (cNodeKeys.includes(parentNodeKey)) {
            this.selectMenu = ''
          }
          // 如果是信道基础节点，则需要加载显示一级区域
          if (parentNodeKey === this.getTableIndexLabel(TableIndex.Channel) + '_root') {
            this.selectMenu = parentNodeKey
          }
        } else {
          dataId = parseInt(dataId)
          switch (parentNodeKey) {
            // 数字紧急报警
            case this.getTableIndexLabel(TableIndex.DigitalAlert):
              this.currDigitalAlertId = dataId
              break
            // 通讯录群组
            case this.getTableIndexLabel(TableIndex.AddressGroup):
              this.currAddressBookGroupId = dataId
              break
            // 一级区域数据
            case this.getTableIndexLabel(TableIndex.ZoneRoot):
              this.currZoneRootId = dataId
              break
            // 二级区域数据
            case this.getTableIndexLabel(TableIndex.ZoneParent):
              this.currZoneParentId = dataId
              break
            // 三级区域数据
            case this.getTableIndexLabel(TableIndex.ZoneLeaf):
              this.currZoneLeafId = dataId
              break
            // 信道
            case this.getTableIndexLabel(TableIndex.Channel):
              if (dataId === this.oneChannel.chId) {
                this.setChannelData(this.channelsIndex[dataId])
              } else {
                this.validateOneChannel()
                  .then(() => {
                    this.currChannelId = dataId
                    this.setChannelData(this.channelsIndex[dataId])
                  })
                  .catch(() => {
                    this.zoneDataDblclick(this.generateNodeKey(this.getTableIndexLabel(TableIndex.Channel), this.oneChannel.chId))
                  })
              }
              break
            // 扫描
            case this.getTableIndexLabel(TableIndex.Scan):
              this.currScanGroupId = dataId
              break
            // 漫游
            case this.getTableIndexLabel(TableIndex.Roam):
              this.currRoamGroupId = dataId
              break
            default:
          }
        }
      },
      contextmenuBeforeOpen(event, ui) {
        const menuTreeRef = this.menuTreeRef
        const node = $.ui.fancytree.getNode(ui.target)
        if (!node) {
          return false
        }

        // 判断哪个节点有右键菜单功能，没有的返回false以阻止打开右键菜单
        const [nodeKey, dataId] = node.key.split(':')
        const cNodeKeys = this.hasChildrenNodeKeys
        if (!cNodeKeys.includes(nodeKey)) {
          return false
        }

        node.setActive()
        const dataNotExist = typeof dataId === 'undefined'
        const status = {
          add: true,
          delete: true,
        }
        let contextmenu = this.menuTreeContextmenu
        switch (nodeKey) {
          // 数字紧急报警
          case this.getTableIndexLabel(TableIndex.DigitalAlert):
            if (dataNotExist) {
              status.add = this.digitalAlertList.length < this.digitalAlertListLimit
              contextmenu = status.add ? contextmenu.filter(item => item.cmd === MenuTreeContextmenu.ADD) : []
            } else {
              status.delete = this.digitalAlertList.length > 1
              contextmenu = status.delete ? contextmenu.filter(item => item.cmd === MenuTreeContextmenu.DELETE) : []
            }
            menuTreeRef.replaceMenu(contextmenu)
            break
          // 通讯录群组
          case this.getTableIndexLabel(TableIndex.AddressGroup):
            if (dataNotExist) {
              status.add = this.addressBookGroup.length < this.addressBookGroupLimit
              contextmenu = status.add ? contextmenu.filter(item => item.cmd === MenuTreeContextmenu.ADD) : []
            } else {
              status.delete = this.addressBookGroup.length > 1
              contextmenu = status.delete ? contextmenu.filter(item => item.cmd === MenuTreeContextmenu.DELETE) : []
            }
            menuTreeRef.replaceMenu(contextmenu)
            break
          // 扫描
          case this.getTableIndexLabel(TableIndex.Scan):
            if (dataNotExist) {
              status.add = this.scanGroup.length < this.scanGroupLimit
              contextmenu = status.add ? contextmenu.filter(item => item.cmd === MenuTreeContextmenu.ADD) : []
            } else {
              status.delete = this.scanGroup.length > 1
              contextmenu = status.delete ? contextmenu.filter(item => item.cmd === MenuTreeContextmenu.DELETE) : []
            }
            menuTreeRef.replaceMenu(contextmenu)
            break
          // 漫游
          case this.getTableIndexLabel(TableIndex.Roam):
            if (dataNotExist) {
              status.add = this.roamGroup.length < this.roamGroupLimit
              contextmenu = status.add ? contextmenu.filter(item => item.cmd === MenuTreeContextmenu.ADD) : []
            } else {
              status.delete = this.roamGroup.length > 1
              contextmenu = status.delete ? contextmenu.filter(item => item.cmd === MenuTreeContextmenu.DELETE) : []
            }
            menuTreeRef.replaceMenu(contextmenu)
            break
          default:
            // 信道和区域数据以系统内数据为准，不支持右键菜单
            return false
        }
      },
      contextmenuSelect(event, ui) {
        const node = $.ui.fancytree.getNode(ui.target)
        if (!node) {
          return
        }

        const cmd = parseInt(ui.cmd)
        const [nodeKey, dataId] = node.key.split(':')
        switch (nodeKey) {
          // 数字紧急报警
          case this.getTableIndexLabel(TableIndex.DigitalAlert):
            this.digitalAlertContextmenuEvent(cmd, nodeKey, dataId)
            break
          // 通讯录群组
          case this.getTableIndexLabel(TableIndex.AddressGroup):
            this.addressBookGroupContextmenuEvent(cmd, nodeKey, dataId)
            break
          // 扫描
          case this.getTableIndexLabel(TableIndex.Scan):
            this.scanGroupContextmenuEvent(cmd, nodeKey, dataId)
            break
          // 漫游
          case this.getTableIndexLabel(TableIndex.Roam):
            this.roamGroupContextmenuEvent(cmd, nodeKey, dataId)
            break
          default:
        }
      },
      manuallyTriggerNodeClick(node) {
        if (!node || !node.tr) {
          return
        }

        node.tr.click()
      },
      updateViewport() {
        this.menuTreeRef.updateViewport()
      },
      getNodeByKey(key) {
        return this.menuTreeRef.getNodeByKey(key)
      },
      removeNode(key) {
        return this.menuTreeRef.removeNode(key)
      },
      updateNodeTitle({ key, title, data }) {
        const node = this.getNodeByKey(key)
        if (!node) {
          return
        }

        node.title = title
        node.data.origin = data
        node.renderTitle()
      },
      addNodeChildren(parentNode, children) {
        return this.menuTreeRef.addNodeChildren(parentNode, children)
      },
      gotoMenuNode(key) {
        bftree.gotoNodeLocation(this.menuTreeId, key, { isActive: true })
      },

      // 数字紧急报警节点操作
      deleteDigitalAlertNode(data) {
        const key = this.generateNodeKey(this.getTableIndexLabel(TableIndex.DigitalAlert), data.id)
        this.removeNode(key)
      },
      addDigitalAlertNode(data) {
        const parentNodeKey = this.getTableIndexLabel(TableIndex.DigitalAlert)
        const parentNode = this.getNodeByKey(parentNodeKey)
        const node = this.addNodeChildren(parentNode, this.createDigitalAlertNode(data))
        this.manuallyTriggerNodeClick(node)
      },
      createDigitalAlertNode(data) {
        const parentNodeKey = this.getTableIndexLabel(TableIndex.DigitalAlert)
        const key = `${parentNodeKey}:${data.id}`

        return {
          title: data.name,
          folder: false,
          expanded: false,
          key,
          icon: false,
          selected: false,
          origin: data,
        }
      },
      updateDigitalAlertNode(data) {
        this.updateNodeTitle({
          key: this.generateNodeKey(this.getTableIndexLabel(TableIndex.DigitalAlert), data.id),
          title: data.name,
          data,
        })
      },
      loadDigitalAlertNode() {
        const parentNodeKey = this.getTableIndexLabel(TableIndex.DigitalAlert)
        const parentNode = this.getNodeByKey(parentNodeKey)
        if (!parentNode) {
          return
        }
        const children = []
        for (let i = 0; i < this.digitalAlertList.length; i++) {
          const data = this.digitalAlertList[i]
          const node = this.getNodeByKey(`${parentNodeKey}:${data.id}`)
          if (node) {
            this.updateDigitalAlertNode(data)
            continue
          }
          children.push(this.createDigitalAlertNode(data))
        }
        this.addNodeChildren(parentNode, children)
      },
      addDigitalAlertData() {
        if (!this.digitalAlertComp) {
          return false
        }
        const data = this.digitalAlertComp.addDigitalAlert()
        this.addDigitalAlertNode(data)
      },
      deleteDigitalAlertData(nodeKey, dataId) {
        const dataLen = this.digitalAlertList.length
        for (let i = 0; i < dataLen; i++) {
          const data = this.digitalAlertList[i]
          if (data.id !== dataId) {
            continue
          }

          // 删除菜单树节点，删除数据源数据
          this.deleteDigitalAlertNode(data)
          this.digitalAlertList.splice(i, 1)
          // 将下一个数据显示在表单中
          const nextDataIndex = i === dataLen - 1 ? 0 : i
          let nextData = this.digitalAlertList[nextDataIndex]
          if (!nextData) {
            nextData = this.digitalAlertList[0]
          }
          this.currDigitalAlertId = nextData.id
          const parentNodeKey = this.getTableIndexLabel(TableIndex.DigitalAlert)
          const nextNodeKey = this.generateNodeKey(parentNodeKey, nextData.id)
          this.gotoMenuNode(nextNodeKey)
          break
        }
      },
      digitalAlertContextmenuEvent(cmd, nodeKey, dataId) {
        const dataNotExist = typeof dataId === 'undefined'
        if (!dataNotExist) {
          dataId = parseInt(dataId)
        }

        switch (cmd) {
          case MenuTreeContextmenu.ADD:
            this.addDigitalAlertData()
            break
          case MenuTreeContextmenu.DELETE:
            this.deleteDigitalAlertData(nodeKey, dataId)
            break
          default:
        }
      },

      // 通讯录群组节点操作
      deleteAddressBookGroupNode(data) {
        const key = this.generateNodeKey(this.getTableIndexLabel(TableIndex.AddressGroup), data.id)
        this.removeNode(key)
      },
      addAddressBookGroupNode(data) {
        const parentNodeKey = this.getTableIndexLabel(TableIndex.AddressGroup)
        const parentNode = this.getNodeByKey(parentNodeKey)
        const node = this.addNodeChildren(parentNode, this.createAddressBookGroupNode(data))
        this.manuallyTriggerNodeClick(node)
      },
      updateAddressBookGroupNode(data) {
        this.updateNodeTitle({
          key: this.generateNodeKey(this.getTableIndexLabel(TableIndex.AddressGroup), data.id),
          title: data.name,
          data,
        })
      },
      createAddressBookGroupNode(data) {
        const parentNodeKey = this.getTableIndexLabel(TableIndex.AddressGroup)
        const key = this.generateNodeKey(parentNodeKey, data.id)

        return {
          title: data.name,
          folder: false,
          expanded: false,
          key,
          icon: false,
          selected: false,
          origin: data,
        }
      },
      loadAddressBookGroupNode() {
        const parentNodeKey = this.getTableIndexLabel(TableIndex.AddressGroup)
        const parentNode = this.getNodeByKey(parentNodeKey)
        if (!parentNode) {
          return
        }
        const children = []
        for (let i = 0; i < this.addressBookGroup.length; i++) {
          const data = this.addressBookGroup[i]
          const node = this.getNodeByKey(`${parentNodeKey}:${data.id}`)
          if (node) {
            this.updateAddressBookGroupNode(data)
            continue
          }
          children.push(this.createAddressBookGroupNode(data))
        }
        this.addNodeChildren(parentNode, children)
      },
      addAddressBookGroupData() {
        if (!this.addressBookGroupComp) {
          return false
        }
        const data = this.addressBookGroupComp.addOneBookGroup()
        this.addAddressBookGroupNode(data)
      },
      deleteAddressBookGroupData(nodeKey, dataId) {
        const dataLen = this.addressBookGroup.length
        for (let i = 0; i < dataLen; i++) {
          const data = this.addressBookGroup[i]
          if (data.id !== dataId) {
            continue
          }

          // 删除菜单树节点，删除数据源数据
          this.deleteAddressBookGroupNode(data)
          this.addressBookGroup.splice(i, 1)
          // 将下一个数据显示在表单中
          const nextDataIndex = i === dataLen - 1 ? 0 : i
          let nextData = this.addressBookGroup[nextDataIndex]
          if (!nextData) {
            nextData = this.addressBookGroup[0]
          }
          this.currAddressBookGroupId = nextData.id
          const parentNodeKey = this.getTableIndexLabel(TableIndex.AddressGroup)
          const nextNodeKey = this.generateNodeKey(parentNodeKey, nextData.id)
          this.gotoMenuNode(nextNodeKey)

          break
        }
      },
      addressBookGroupContextmenuEvent(cmd, nodeKey, dataId) {
        const dataNotExist = typeof dataId === 'undefined'
        if (!dataNotExist) {
          dataId = parseInt(dataId)
        }

        switch (cmd) {
          case MenuTreeContextmenu.ADD:
            this.addAddressBookGroupData()
            break
          case MenuTreeContextmenu.DELETE:
            this.deleteAddressBookGroupData(nodeKey, dataId)
            break
          default:
        }
      },

      // 扫描组节点操作
      deleteScanGroupNode(data) {
        const key = this.generateNodeKey(this.getTableIndexLabel(TableIndex.Scan), data.scanId)
        this.removeNode(key)
      },
      addScanGroupNode(data) {
        const parentNodeKey = this.getTableIndexLabel(TableIndex.Scan)
        const parentNode = this.getNodeByKey(parentNodeKey)
        const node = this.addNodeChildren(parentNode, this.createScanGroupNode(data))
        this.manuallyTriggerNodeClick(node)
      },
      updateScanGroupNode(data, node) {
        this.updateNodeTitle({
          key: this.generateNodeKey(this.getTableIndexLabel(TableIndex.Scan), data.scanId),
          title: data.name,
          data,
        })
      },
      createScanGroupNode(data) {
        const parentNodeKey = this.getTableIndexLabel(TableIndex.Scan)
        const key = this.generateNodeKey(parentNodeKey, data.scanId)

        return {
          title: data.name,
          folder: false,
          expanded: false,
          key,
          icon: false,
          selected: false,
          origin: data,
        }
      },
      loadScanGroupNode() {
        const parentNodeKey = this.getTableIndexLabel(TableIndex.Scan)
        const parentNode = this.getNodeByKey(parentNodeKey)
        if (!parentNode) {
          bfglob.console.warn('扫描组节点不存在')
          return
        }
        const children = []
        for (let i = 0; i < this.scanGroup.length; i++) {
          const data = this.scanGroup[i]
          const node = this.getNodeByKey(`${parentNodeKey}:${data.scanId}`)
          if (node) {
            this.updateScanGroupNode(data)
            continue
          }
          children.push(this.createScanGroupNode(data))
        }
        this.addNodeChildren(parentNode, children)
      },
      addScanGroupData() {
        if (!this.scanGroupComp) {
          return false
        }
        const data = this.scanGroupComp.addOneScanGroup()
        this.addScanGroupNode(data)
      },
      deleteScanGroupData(nodeKey, dataId) {
        const dataLen = this.scanGroup.length
        for (let i = 0; i < dataLen; i++) {
          const data = this.scanGroup[i]
          if (data.scanId !== dataId) {
            continue
          }

          // 删除菜单树节点，删除数据源数据
          this.deleteScanGroupNode(data)
          this.scanGroup.splice(i, 1)
          // 将下一个数据显示在表单中
          const nextDataIndex = i === dataLen - 1 ? 0 : i
          let nextData = this.scanGroup[nextDataIndex]
          if (!nextData) {
            nextData = this.scanGroup[0]
          }
          this.currScanGroupId = nextData.scanId
          const parentNodeKey = this.getTableIndexLabel(TableIndex.Scan)
          const nextNodeKey = this.generateNodeKey(parentNodeKey, nextData.scanId)
          this.gotoMenuNode(nextNodeKey)
          break
        }
      },
      scanGroupContextmenuEvent(cmd, nodeKey, dataId) {
        const dataNotExist = typeof dataId === 'undefined'
        if (!dataNotExist) {
          dataId = parseInt(dataId)
        }

        switch (cmd) {
          case MenuTreeContextmenu.ADD:
            this.addScanGroupData()
            break
          case MenuTreeContextmenu.DELETE:
            this.deleteScanGroupData(nodeKey, dataId)
            break
          default:
        }
      },

      // 漫游组节点操作
      deleteRoamGroupNode(data) {
        const key = this.generateNodeKey(this.getTableIndexLabel(TableIndex.Roam), data.roamId)
        this.removeNode(key)
      },
      addRoamGroupNode(data) {
        const parentNodeKey = this.getTableIndexLabel(TableIndex.Roam)
        const parentNode = this.getNodeByKey(parentNodeKey)
        const node = this.addNodeChildren(parentNode, this.createRoamGroupNode(data))
        this.manuallyTriggerNodeClick(node)
      },
      updateRoamGroupNode(data, node) {
        this.updateNodeTitle({
          key: this.generateNodeKey(this.getTableIndexLabel(TableIndex.Roam), data.roamId),
          title: data.name,
          data,
        })
      },
      createRoamGroupNode(data) {
        const parentNodeKey = this.getTableIndexLabel(TableIndex.Roam)
        const key = this.generateNodeKey(parentNodeKey, data.roamId)

        return {
          title: data.name,
          folder: false,
          expanded: false,
          key,
          icon: false,
          selected: false,
          origin: data,
        }
      },
      loadRoamGroupNode() {
        const parentNodeKey = this.getTableIndexLabel(TableIndex.Roam)
        const parentNode = this.getNodeByKey(parentNodeKey)
        if (!parentNode) {
          bfglob.console.warn('漫游组节点不存在')
          return
        }
        const children = []
        for (let i = 0; i < this.roamGroup.length; i++) {
          const data = this.roamGroup[i]
          const node = this.getNodeByKey(`${parentNodeKey}:${data.roamId}`)
          if (node) {
            this.updateRoamGroupNode(data)
            continue
          }
          children.push(this.createRoamGroupNode(data))
        }
        this.addNodeChildren(parentNode, children)
      },
      addRoamGroupData() {
        if (!this.roamGroupComp) {
          return false
        }
        const data = this.roamGroupComp.addOneRoamGroup()
        this.addRoamGroupNode(data)
      },
      deleteRoamGroupData(nodeKey, dataId) {
        const dataLen = this.roamGroup.length
        for (let i = 0; i < dataLen; i++) {
          const data = this.roamGroup[i]
          if (data.roamId !== dataId) {
            continue
          }

          // 删除菜单树节点，删除数据源数据
          this.deleteRoamGroupNode(data)
          this.roamGroup.splice(i, 1)
          // 将下一个数据显示在表单中
          const nextDataIndex = i === dataLen - 1 ? 0 : i
          let nextData = this.roamGroup[nextDataIndex]
          if (!nextData) {
            nextData = this.roamGroup[0]
          }
          this.currRoamGroupId = nextData.roamId
          const parentNodeKey = this.getTableIndexLabel(TableIndex.Roam)
          const nextNodeKey = this.generateNodeKey(parentNodeKey, nextData.roamId)
          bftree.gotoNodeLocation(this.menuTreeId, nextNodeKey, {
            isActive: true,
          })

          break
        }
      },
      roamGroupContextmenuEvent(cmd, nodeKey, dataId) {
        const dataNotExist = typeof dataId === 'undefined'
        if (!dataNotExist) {
          dataId = parseInt(dataId)
        }

        switch (cmd) {
          case MenuTreeContextmenu.ADD:
            this.addRoamGroupData()
            break
          case MenuTreeContextmenu.DELETE:
            this.deleteRoamGroupData(nodeKey, dataId)
            break
          default:
        }
      },

      /* 同步界面表单数据 */
      asyncDataList(originList, dataList, indexList, key = 'id', mapCallBack) {
        const needDelList = []
        const list = originList.map(item => {
          if (typeof mapCallBack === 'function') {
            item = mapCallBack(item)
          }
          const cache = indexList[item[key]]
          if (cache) {
            needDelList.push(cache)
          }
          return merge(cache, item)
        })
        while (needDelList.length) {
          dataList.splice(dataList.indexOf(needDelList.pop()), 1)
        }
        return dataList.concat(list)
      },
      asyncDeviceWriteInfo(res) {
        if (!res || !Array.isArray(res.result)) {
          return
        }
        this.deviceWriteInfo = Object.assign(this.deviceWriteInfo, res.result[0])
        // this.saveDefaultFrequency(this.deviceWriteInfo.minFrequency)
      },
      asyncResourceVersion(res) {
        if (!res || !Array.isArray(res.result)) {
          return
        }
        this.resourceVersion = Object.assign(this.resourceVersion, res.result[0])
      },
      asyncIdentityInfo(res) {
        if (!res || !Array.isArray(res.result)) {
          return
        }

        const settings = res.result[0]
        this.identityInfo = merge(this.identityInfo, settings)

        // 显示序列号
        this.deviceWriteInfo['serizeNumber'] = this.identityInfo.serizeNumber
      },
      asyncGeneralSettings(res) {
        if (!res || !Array.isArray(res.result)) {
          return
        }

        const settings = res.result[0]
        this.generalSettings = merge(this.generalSettings, settings)
      },
      asyncButtonsDefine(res) {
        if (!res || !Array.isArray(res.result)) {
          return
        }

        const settings = res.result[0]
        this.buttonDefined = merge(this.buttonDefined, settings)
      },
      asyncShortMessage(res) {
        if (!res || !Array.isArray(res.result)) {
          return
        }

        for (let i = 0; i < res.result.length; i++) {
          const sms = res.result[i]
          const index = this.smsContent.findIndex(item => item.msgId === sms.msgId)
          if (index === -1) {
            this.smsContent.push(sms)
          } else {
            this.smsContent[index] = sms
          }
        }
      },
      asyncEncryptConfig(res) {
        if (!res || !Array.isArray(res.result)) {
          return
        }

        const settings = res.result[0]
        this.encryptConfig = merge(this.encryptConfig, settings)
      },
      asyncEncryptList(res) {
        if (!res || !Array.isArray(res.result)) {
          return
        }
        this.encryptXORList = this.asyncDataList(res.result, this.encryptXORList, this.encryptListIndex, 'keyId')
      },
      asyncEncryptARC4List(res) {
        if (!res || !Array.isArray(res.result)) {
          return
        }
        this.encryptARC4List = this.asyncDataList(res.result, this.encryptARC4List, this.encryptARC4ListIndex, 'keyId')
      },
      asyncEncryptAES256List(res) {
        if (!res || !Array.isArray(res.result)) {
          return
        }
        this.encryptAES256List = this.asyncDataList(res.result, this.encryptAES256List, this.encryptAES256ListIndex, 'keyId')
      },
      asyncGpsSettings(res) {
        if (!res || !Array.isArray(res.result)) {
          return
        }

        this.gpsSettings = Object.assign(this.gpsSettings, res.result[0])
      },
      asyncMenuSettings(res) {
        if (!res || !Array.isArray(res.result)) {
          return
        }

        const settings = res.result[0]
        this.menuSettings = merge(this.menuSettings, settings)
        // 读取到了信道配置密码,将信道配置密码模式设为独立设置
        this.chConfigPwdMode = this.menuSettings.chConfigPwd ? (this.menuSettings.powerOnPwd === this.menuSettings.chConfigPwd ? 2 : 1) : 0
        this.usbFlashDiskPasswordMode = this.generalSettings.usbFlashDiskPassword
          ? this.menuSettings.powerOnPwd === this.generalSettings.usbFlashDiskPassword
            ? 2
            : 1
          : 0
      },
      asyncSignalingSystemSettings(res) {
        if (!res || !Array.isArray(res.result)) {
          return
        }

        const settings = res.result[0]
        this.signalingSystem = merge(this.signalingSystem, settings)
      },
      asyncAlertConfigSettings(res) {
        if (!res || !Array.isArray(res.result)) {
          return
        }

        const settings = res.result[0]
        this.alertConfig = merge(this.alertConfig, settings)
      },
      asyncDigitalAlertList(res) {
        if (!res || !Array.isArray(res.result)) {
          return
        }

        this.digitalAlertList = this.asyncDataList(res.result, this.digitalAlertList, this.digitalAlertListIndex)
        if (this.digitalAlertList.length && this.currDigitalAlertId < 0) {
          this.currDigitalAlertId = 0
        }
        this.loadDigitalAlertNode()
      },
      asyncAddressBookGroup(res) {
        if (!res || !Array.isArray(res.result)) {
          return
        }

        const mapCb = item => {
          item.dmrIdList = item.dmrIdList || []
          return item
        }
        this.addressBookGroup = this.asyncDataList(res.result, this.addressBookGroup, this.addressBookGroupIndex, 'id', mapCb)
        // 同步生成节点
        this.loadAddressBookGroupNode()
      },
      asyncAddressBook(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }

        // 同步本地通讯录树
        if (this.addressBookComp) {
          this.addressBookComp.asyncNodeSelectStatus(res.result)
        }
        this.originAddressBook = this.originAddressBook.concat(res.result)
        // 关联群组数据
        res.result.forEach(item => {
          const data = this.addressBookGroupIndex[item.groupId]
          if (data) {
            data.dmrIdList.push(item.dmrId)
            data.memberList = data.memberList ?? []
            data.memberList.push(item.id)
          }
        })
      },
      asyncPhoneBook(res) {
        if (!res || !Array.isArray(res.result)) {
          return
        }

        // 保存本地数据
        for (let i = 0; i < res.result.length; i++) {
          const pb = res.result[i]
          const index = this.phoneBook.findIndex(item => item.phoneId === pb.phoneId)
          if (index === -1) {
            this.phoneBook.push(pb)
          } else {
            this.phoneBook[index] = pb
          }
        }
        if (this.phoneBookComp) {
          this.phoneBookComp.asyncNodeSelectStatus(res.result)
        }
      },
      asyncRxGroup(res) {
        if (!res || !Array.isArray(res.result)) {
          return
        }

        // 处理读取回来的接收组数据
        if (this.receiveGroupComp) {
          this.receiveGroupComp.asyncRxGroup(res.result)
        }
      },
      asyncZoneRootDataList(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }
        this.zoneRootDataList = this.asyncDataList(res.result, this.zoneRootDataList, this.zoneRootDataIndex, 'zoneId')
        this.loadZoneRootNodes(res.result)
      },
      asyncZoneParentDataList(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }
        this.zoneParentDataList = this.asyncDataList(res.result, this.zoneParentDataList, this.zoneParentDataIndex, 'zoneId')
        this.loadZoneParentNodes(res.result)
      },
      getZoneChIdList(zoneData) {
        if (!zoneData || !Array.isArray(zoneData.usedList)) {
          return []
        }
        // 每个区域下只有64个信道，信道使用标志右移后按位与0x01,结果为1则启用该信道
        let i = 0
        const chIdList = []
        const bits = zoneData.usedFlag.toString(2)
        while (i < bits.length) {
          if (parseInt(bits[i]) === 0x01) {
            chIdList.push(zoneData.usedList[i])
          }
          i++
        }
        return chIdList
      },
      setZoneChFlag(chIdList) {
        if (!Array.isArray(chIdList) || !chIdList.length) {
          return 0
        }
        // 生成二进制的字符串，
        let bitStr = ''
        let i = 1
        while (i <= chIdList.length) {
          bitStr = bitStr.padStart(i, 1)
          i++
        }
        // 从二进制中生成long类型
        return Long.fromString(bitStr, true, 2)
      },
      asyncZoneLeafDataList(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }
        // 保留区域实际的信道id列表
        const syncCb = item => {
          item.usedList = this.getZoneChIdList(item)
          return item
        }
        this.zoneLeafDataList = this.asyncDataList(res.result, this.zoneLeafDataList, this.zoneLeafDataIndex, 'zoneId', syncCb)
        this.loadZoneLeafNodes(res.result)
      },
      asyncChannelDataList(res) {
        if (!res || !Array.isArray(res.result)) {
          return
        }
        this.channelDataList = this.asyncDataList(res.result, this.channelDataList, this.channelsIndex, 'chId')
        this.loadChannelNodes(res.result)

        // 处理按键定义的预设信道的区域数据
        // buttonDefaultChannelSet缓存的是预设信道的索引,信道可以重复设置
        this.buttonDefaultChannelSet = this.buttonDefaultChannelSet ?? new Set()
        if (this.buttonDefaultChannelSet.size >= this.buttonDefined.defaultChannel.length) {
          this.buttonDefaultChannelSet = undefined
          return
        }
        const setDefaultChannelZones = (defaultChannel, channel, index) => {
          // 信道所属的3级区域
          const zoneId = channel.zoneId
          const zone3th = this.zoneLeafDataList.find(item => item.zoneId === zoneId)
          if (!zone3th) return

          // 更新预设信道的区域辅助参数
          defaultChannel.rootId = zone3th.rootId
          defaultChannel.parentId = zone3th.parentId
          defaultChannel.zoneId = zoneId
          this.buttonDefaultChannelSet.add(index)
        }
        const setDefaultChannel = (data, index) => {
          for (let i = 0; i < res.result.length; i++) {
            const channel = res.result[i]
            if (data.channelId === channel.chId) {
              setDefaultChannelZones(data, channel, index)
            }
          }
        }
        for (let i = 0; i < this.buttonDefined.defaultChannel.length; i++) {
          const preChannel = this.buttonDefined.defaultChannel[i]
          if (this.buttonDefaultChannelSet.has(i)) continue
          setDefaultChannel(preChannel, i)
        }
      },
      asyncScanGroup(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }

        const mapCb = item => {
          item.membersList = item.membersList.slice(0, item.memberCount)
          return item
        }
        this.scanGroup = this.asyncDataList(res.result, this.scanGroup, this.scanGroupIndex, 'scanId', mapCb)
        if (this.scanGroup.length && this.currScanGroupId < 0) {
          this.currScanGroupId = 0
        }
        this.loadScanGroupNode()
      },
      asyncRoamGroup(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }

        const mapCb = item => {
          item.roamChList = item.roamChList.slice(0, item.roamChCount)
          return item
        }
        this.roamGroup = this.asyncDataList(res.result, this.roamGroup, this.roamGroupIndex, 'roamId', mapCb)
        if (this.roamGroup.length && this.currRoamGroupId < 0) {
          this.currRoamGroupId = 0
        }
        this.loadRoamGroupNode()
      },
      asyncPatrolConfig(res) {
        if (!res || !Array.isArray(res.result)) {
          return
        }

        const settings = res.result[0]
        this.patrolConfig = merge(this.patrolConfig, settings)
      },
      asyncEmergencyAlarm(res) {
        if (!res || !Array.isArray(res.result)) {
          return
        }

        const settings = res.result[0]
        this.emergencyAlarm = merge(this.emergencyAlarm, settings)
      },
      asyncAutoPosMonitor(res) {
        if (!res || !Array.isArray(res.result)) {
          return
        }

        const settings = res.result[0]
        this.trailCtrl = merge(this.trailCtrl, settings)
      },
      syncSosConfig(res) {
        if (!res || !Array.isArray(res.result)) {
          return
        }

        const settings = res.result[0]
        this.sosCfg = merge(this.sosCfg, settings)
      },
      syncSosChannels(res) {
        if (!res || !Array.isArray(res.result)) {
          return
        }
        this.sosChannelList = merge(this.sosChannelList, res.result)
        this.sosChannel = this.sosChannelList[this.sosChannelId]
      },
      asyncLocalConfig(data) {
        switch (data.type) {
          // 设备信息
          case TableIndex.TD920DeviceInfo:
            this.asyncDeviceWriteInfo(data)
            // 不存在/存在 定位功能,删除/添加 卫星定位设置节点
            this.treeValidaNode(this.deviceWriteInfo.config.locate, TableIndex.GpsSettings, TableIndex.DigitalAddress, this.GpsSettingsNodeData)
            // 不存在/存在 sdc, 删除/添加 巡逻系统节点
            this.treeValidaNode(this.deviceWriteInfo.config.sdc, TableIndex.PatrolConfig, TableIndex.SosCfg, this.PatrolSystemNodeData)
            break
          // 资源版本信息
          case TableIndex.ResourceVersion:
            this.asyncResourceVersion(data)
            break
          // 身份信息
          case TableIndex.IdentityInfo:
            this.asyncIdentityInfo(data)
            break
          // 总体设置
          case TableIndex.GeneralSettings:
            this.asyncGeneralSettings(data)
            // 判断是否使用本机时间
            this.syncTimeChange(this.generalSettings.syncTime)
            break
          // 5: 按键设置
          case TableIndex.ButtonSettings:
            this.asyncButtonsDefine(data)
            break
          // 短信
          case TableIndex.ShortMessage:
            this.asyncShortMessage(data)
            break
          // 加密配置
          case TableIndex.EncryptSettings:
            this.asyncEncryptConfig(data)
            break
          // 密钥列表
          case TableIndex.EncryptList:
            this.asyncEncryptList(data)
            break
          // ARC4密钥列表
          case TableIndex.ARC4List:
            this.asyncEncryptARC4List(data)
            break
          // AES256密钥列表
          case TableIndex.AES256List:
            this.asyncEncryptAES256List(data)
            break
          // 卫星定位
          case TableIndex.GpsSettings:
            this.asyncGpsSettings(data)
            break
          // 菜单设置
          case TableIndex.MenuSettings:
            this.asyncMenuSettings(data)
            break
          //  信令系统
          case TableIndex.SignalingSystem:
            this.asyncSignalingSystemSettings(data)
            break
          //  警报设置
          case TableIndex.AlertConfig:
            this.asyncAlertConfigSettings(data)
            break
          // 数字警报
          case TableIndex.DigitalAlert:
            this.asyncDigitalAlertList(data)
            break
          // 数字通讯录群组列表
          case TableIndex.AddressGroup:
            this.asyncAddressBookGroup(data)
            break
          // 数字通信录
          case TableIndex.DigitalAddress:
            this.asyncAddressBook(data)
            break
          // 电话簿
          case TableIndex.PhoneBook:
            this.asyncPhoneBook(data)
            break
          // 接收组列表
          case TableIndex.RxGroup:
            this.asyncRxGroup(data)
            break
          // 一级区域
          case TableIndex.ZoneRoot:
            this.asyncZoneRootDataList(data)
            break
          // 二级区域
          case TableIndex.ZoneParent:
            this.asyncZoneParentDataList(data)
            break
          // 三级区域
          case TableIndex.ZoneLeaf:
            this.asyncZoneLeafDataList(data)
            break
          // 信道数据
          case TableIndex.Channel:
            this.asyncChannelDataList(data)
            break
          // 扫描组列表
          case TableIndex.Scan:
            this.asyncScanGroup(data)
            break
          // 漫游组列表
          case TableIndex.Roam:
            this.asyncRoamGroup(data)
            break
          // 巡查系统配置
          case TableIndex.PatrolConfig:
            this.asyncPatrolConfig(data)
            break
          // 紧急报警
          case TableIndex.EmergencyAlarm:
            this.asyncEmergencyAlarm(data)
            break
          // 自动定位监控,
          case TableIndex.AutoPosMonitor:
            this.asyncAutoPosMonitor(data)
            break
          // 救援信道配置
          case TableIndex.SosCfg:
            this.syncSosConfig(data)
            break
          // 救援信道
          case TableIndex.SosChData:
            this.syncSosChannels(data)
            break
        }
      },
      readDataConfig() {
        if (!this.canRead() || this.isReading) {
          return
        }
        // 开始读取数据提示
        bfNotify.messageBox(this.$t('msgbox.startReading'))
        if (this.selectedDeviceDmrId) {
          this.selectedDeviceDmrId = ''
        } else {
          this.clearDeviceDataConfig()
        }
        this.sendAuthentication()
      },

      /* 写入数据操作 */
      checkDeviceFreqValid(deviceInfo) {
        if (!deviceInfo || !Array.isArray(deviceInfo.result)) {
          return false
        }
        const info = deviceInfo.result[0]
        if (!info || !Array.isArray(info.frequencyRange)) {
          return false
        }
        if (
          info.frequencyRange.filter(item => {
            return this.deviceWriteInfo.frequencyRange.some(v => {
              return item.min !== v.min || item.max !== v.max
            })
          }).length === 0
        ) {
          this.showWriteInFailInfo(this.$t('writeFreq.frequencyRangeError'))
          this.setWriteEndStatus()
          return false
        }
        return true
      },
      getBeforeEncodeData(type) {
        switch (type) {
          // 设备信息
          case TableIndex.TD920DeviceInfo:
            return cloneDeep(this.deviceWriteInfo)
          // 资源版本信息
          case TableIndex.ResourceVersion:
            return cloneDeep(this.resourceVersion)
          // 身份信息
          case TableIndex.SecondIdentityInfo:
            return cloneDeep(this.identityInfo)
          // 通讯密码
          case TableIndex.Password:
            return cloneDeep(this.passwordInfo)
          // 总体设置
          case TableIndex.GeneralSettings:
            return cloneDeep(this.generalSettings)
          // 按键设置
          case TableIndex.ButtonSettings:
            return cloneDeep(this.buttonDefined)
          // 短信
          case TableIndex.ShortMessage:
            return cloneDeep(this.smsContent)
          // 加密配置
          case TableIndex.EncryptSettings:
            return cloneDeep(this.encryptConfig)
          // 加密列表
          case TableIndex.EncryptList:
            return cloneDeep(this.encryptXORList)
          // ARC4加密列表
          case TableIndex.ARC4List:
            return cloneDeep(this.encryptARC4List)
          // AES256加密列表
          case TableIndex.AES256List:
            return cloneDeep(this.encryptAES256List)
          // 卫星定位
          case TableIndex.GpsSettings:
            return cloneDeep(this.gpsSettings)
          // 菜单设置
          case TableIndex.MenuSettings:
            return cloneDeep(this.menuSettings)
          // 信令系统
          case TableIndex.SignalingSystem:
            return cloneDeep(this.signalingSystem)
          // 警报配置
          case TableIndex.AlertConfig:
            return cloneDeep(this.alertConfig)
          // 数字紧急报警
          case TableIndex.DigitalAlert:
            return cloneDeep(this.digitalAlertList)
          // 通讯录群组
          case TableIndex.AddressGroup:
            return cloneDeep(this.addressBookGroup)
          // 通讯录
          case TableIndex.DigitalAddress:
            return cloneDeep(this.selectedAddressBook)
          // 电话簿
          case TableIndex.PhoneBook:
            return this.phoneBook.filter(item => {
              return !!bfglob.gphoneBook.getDataByIndex(item.phoneNo)
            })
          // 接收组列表
          case TableIndex.RxGroup:
            return this.receiveGroupComp ? this.receiveGroupComp.getWriteRxGroupList() : []
          // 一级区域
          case TableIndex.ZoneRoot:
            return cloneDeep(this.zoneRootDataList)
          // 二级区域
          case TableIndex.ZoneParent:
            return cloneDeep(this.zoneParentDataList)
          // 三级区域
          case TableIndex.ZoneLeaf:
            return cloneDeep(this.zoneLeafDataList)
          // 信道
          case TableIndex.Channel:
            return cloneDeep(this.channelDataList)
          // 扫描列表
          case TableIndex.Scan:
            return cloneDeep(this.scanGroup)
          // 漫游列表
          case TableIndex.Roam:
            return cloneDeep(this.roamGroup)
          // 配置
          case TableIndex.PatrolConfig:
            return cloneDeep(this.patrolConfig)
          // 紧急报警
          case TableIndex.EmergencyAlarm:
            return cloneDeep(this.emergencyAlarm)
          // 自动定位监控
          case TableIndex.AutoPosMonitor:
            return cloneDeep(this.trailCtrl)
          // 救援配置
          case TableIndex.SosCfg:
            return cloneDeep(this.sosCfg)
          // 救援信道
          case TableIndex.SosChData:
            return cloneDeep(this.sosChannelList)
        }
      },
      validateAllRules() {
        return new Promise((resolve, reject) => {
          const validateList = [
            {
              ref: 'generalSettings',
              msg: this.$t('writeFreq.generalSettingsFormValidate'),
            },
            // { ref: 'digitAlarm', msg: this.$t('writeFreq.digitalAlarmFormValidate') },
            // { ref: 'scanGroup', msg: this.$t('writeFreq.scanGroupFormValidate') }
          ]
          // 信道列表中有数据时才需要验证
          if (this.channelDataList.length) {
            // if (!this.channelDataList.includes(this.channelData)) {
            //   this.channelData = this.channelDataList[0]
            // }
            // validateList.push({ ref: 'channelData', msg: this.$t('writeFreq.channelDataFormValidate') })
          }
          const iterator = validateList[Symbol.iterator]()

          const validate = item => {
            if (item.done) {
              resolve()
            }
            const { ref, msg } = item.value
            this.formValidate(ref)
              .then(() => {
                validate(iterator.next())
              })
              .catch(() => {
                reject(msg)
              })
          }

          validate(iterator.next())
        })
      },
      writeInFrequency() {
        this.writeDataConfig()
      },

      exportConfig() {
        // 只导出设备的写频配置，不导出设备的接收组、信道数据、设备的dmrId等
        const dataStructType = [1, 2, 4, 5, 6, 7, 8, 14, 15, 18, 19, 20, 21, 23, 24, 25, 26, 27, 28, 29, 31]
        const jsonData = dataStructType
          .map(type => {
            let config = this.getBeforeEncodeData(type)
            // 删除一些不需要导出的参数
            switch (type) {
              case 4:
                // 将常规设置中的设备dmrId和设备名称去掉
                delete config.deviceName
                delete config.dmrIdLabel
                delete config.intDmrId
                break
            }
            if (!Array.isArray(config)) {
              config = [config]
            }
            return { [type]: config }
          })
          .reduce((p, c) => {
            return Object.assign(p, c)
          }, {})

        this.exportData(jsonData)
      },
      newConfig() {
        this.selectedDeviceDmrId = ''
        this.clearDeviceDataConfig(true)
        // 清除设备频率信息
        this.deviceWriteInfo = Object.assign(this.deviceWriteInfo, {
          frequencyRange: cloneDeep(FrequencyRange),
        })
        // 常规设置
        this.generalSettings = cloneDeep(GeneralSettings)
        // 按键设置
        this.buttonDefined = cloneDeep(ButtonDefined)
        // 菜单设置
        this.menuSettings = cloneDeep(MenuSettings)
        // 信令系统
        this.signalingSystem = cloneDeep(SignalingSystem)
        this.oneChannel = this.getDefaultChannel()
        this.initPatrolConfig(false)
        this.initEmergencyAlarm(false)
        this.initSmsData()
        this.initEncryptConfig()
      },
      redrawViewport(retry = 0) {
        if (retry > 10) {
          return
        }
        this.menuTreeRef.updateViewport()
        setTimeout(() => {
          const viewport = this.menuTreeRef.getTree().viewport
          const count = this.menuTreeRef.getViewportCount()
          if (isNaN(viewport.count) || isNaN(viewport.start) || count !== viewport.count) {
            this.redrawViewport(++retry)
          }
        }, 0)
      },
      treeValidaNode(locateOrSdc, index, beforeIndex, insertNode) {
        const targetKey = this.getTableIndexLabel(index)
        // 没有开放对应的配置，则清除菜单节点
        if (!locateOrSdc) {
          this.removeNode(targetKey)
          return
        }

        // 有对应的配置，创建可能缺失的菜单节点
        const targetNode = this.getNodeByKey(targetKey)
        if (targetNode) {
          return
        }

        const beforeKey = this.getTableIndexLabel(beforeIndex)
        const rootNode = this.menuTreeRef.getRootNode()
        this.menuTreeRef.addNodeChildren(rootNode, insertNode, beforeKey)
      },
      getTableIndexLabel(index) {
        return TableIndex[index]
      },
      generateNodeKey(key, dataId) {
        return `${key}:${dataId}`
      },
    },
    computed: {
      confirmedDataSingleCall: {
        get() {
          return !this.oneChannel.networkConfig.unConfirmSingleCall
        },
        set() {
          this.oneChannel.networkConfig.unConfirmSingleCall = !this.oneChannel.networkConfig.unConfirmSingleCall
        },
      },
      gpsMode: {
        get() {
          if (!this.gpsSettings.baseConfig.enable) {
            return 0xff
          }

          return this.gpsSettings.baseConfig.mode
        },
        set(val) {
          // 关闭GPS
          if (val === 0xff) {
            this.gpsSettings.baseConfig.enable = false
            this.gpsSettings.baseConfig.mode = 0
          } else {
            this.gpsSettings.baseConfig.enable = true
            this.gpsSettings.baseConfig.mode = val
          }
        },
      },
      currentChannelIdList() {
        return this.channelDataList.map(item => item.chId)
      },
      // 0=5秒，间隔5秒，范围5-60秒，默认0
      intervalHonkingTime: {
        get() {
          return this.sosCfg.intervalHonkingTime * 5 + 5
        },
        set(val) {
          this.sosCfg.intervalHonkingTime = (val - 5) / 5
        },
      },
      // 0=500毫秒，间隔100毫秒，范围500-2000毫秒，默认1
      flashlightTime: {
        get() {
          return this.sosCfg.flashlightTime * 100 + 500
        },
        set(val) {
          this.sosCfg.flashlightTime = (val - 500) / 100
        },
      },
      // 0=3分钟，间隔1分钟，范围3-10分钟，默认0
      highPowerSosTime: {
        get() {
          return this.sosCfg.highPowerSosTime + 3
        },
        set(val) {
          this.sosCfg.highPowerSosTime = val - 3
        },
      },
      // 0=5分钟，间隔1分钟，范围5-15分钟，默认0
      middlePowerSosTime: {
        get() {
          return this.sosCfg.middlePowerSosTime + 5
        },
        set(val) {
          this.sosCfg.middlePowerSosTime = val - 5
        },
      },
      // 0=10分钟，间隔1分钟，范围10-30分钟，默认0
      lowPowerSosTime: {
        get() {
          return this.sosCfg.lowPowerSosTime + 10
        },
        set(val) {
          this.sosCfg.lowPowerSosTime = val - 10
        },
      },

      menuTreeRef() {
        return this.$refs[this.menuTreeId]
      },
      hasChildrenNodeKeys() {
        return [
          this.getTableIndexLabel(TableIndex.DigitalAlert),
          this.getTableIndexLabel(TableIndex.AddressGroup),
          this.getTableIndexLabel(TableIndex.ZoneRoot),
          this.getTableIndexLabel(TableIndex.ZoneParent),
          this.getTableIndexLabel(TableIndex.ZoneLeaf),
          this.getTableIndexLabel(TableIndex.Channel),
          this.getTableIndexLabel(TableIndex.Scan),
          this.getTableIndexLabel(TableIndex.Roam),
        ]
      },
      GpsSettingsNodeData() {
        return {
          ...defaultNodeData,
          key: this.getTableIndexLabel(TableIndex.GpsSettings),
          title: this.$t('dialog.satellitePositionSetting'),
        }
      },
      PatrolSystemNodeData() {
        return {
          ...defaultNodeData,
          key: this.getTableIndexLabel(TableIndex.PatrolConfig),
          title: this.$t('dialog.patrolSystem'),
        }
      },
      menuTreeSource() {
        return [
          {
            ...defaultNodeData,
            key: this.getTableIndexLabel(TableIndex.TD920DeviceInfo),
            title: this.$t('dialog.deviceInfo'),
          },
          {
            ...defaultNodeData,
            key: this.getTableIndexLabel(TableIndex.GeneralSettings),
            title: this.$t('dialog.generalSetting'),
          },
          {
            ...defaultNodeData,
            key: this.getTableIndexLabel(TableIndex.ButtonSettings),
            title: this.$t('dialog.buttonDefinition'),
          },
          {
            ...defaultNodeData,
            key: this.getTableIndexLabel(TableIndex.ShortMessage),
            title: this.$t('dialog.smsContent'),
          },
          {
            ...defaultNodeData,
            key: this.getTableIndexLabel(TableIndex.EncryptSettings),
            title: this.$t('writeFreq.encryptionConfig'),
          },
          {
            ...defaultNodeData,
            key: this.getTableIndexLabel(TableIndex.MenuSettings),
            title: this.$t('dialog.menuSettings'),
          },
          {
            ...defaultNodeData,
            key: this.getTableIndexLabel(TableIndex.SignalingSystem),
            title: this.$t('writeFreq.signalingSystem'),
          },
          {
            ...defaultNodeData,
            key: this.getTableIndexLabel(TableIndex.AlertConfig) + '_root',
            title: this.$t('writeFreq.theAlarm'),
            expanded: true,
            children: [
              {
                ...defaultNodeData,
                key: this.getTableIndexLabel(TableIndex.AlertConfig),
                title: this.$t('writeFreq.alarmSetting'),
              },
              {
                ...defaultNodeData,
                key: this.getTableIndexLabel(TableIndex.DigitalAlert),
                title: this.$t('writeFreq.digitalEmergencyAlarm'),
                expanded: true,
              },
            ],
          },
          this.GpsSettingsNodeData,
          {
            ...defaultNodeData,
            key: this.getTableIndexLabel(TableIndex.DigitalAddress),
            title: this.$t('dialog.addressBook'),
          },
          {
            ...defaultNodeData,
            key: this.getTableIndexLabel(TableIndex.AddressGroup),
            title: this.$t('writeFreq.addressBookGroup'),
            expanded: true,
          },
          {
            ...defaultNodeData,
            key: this.getTableIndexLabel(TableIndex.PhoneBook),
            title: this.$t('dialog.phoneBook'),
          },
          {
            ...defaultNodeData,
            key: this.getTableIndexLabel(TableIndex.RxGroup),
            title: this.$t('dialog.rxGroup'),
          },
          {
            ...defaultNodeData,
            key: this.getTableIndexLabel(TableIndex.Channel) + '_root',
            title: this.$t('dialog.channelSetting'),
            expanded: true,
          },
          {
            ...defaultNodeData,
            key: this.getTableIndexLabel(TableIndex.Scan),
            title: this.$t('writeFreq.scan'),
            expanded: true,
          },
          {
            ...defaultNodeData,
            key: this.getTableIndexLabel(TableIndex.Roam),
            title: this.$t('writeFreq.roaming'),
            expanded: true,
          },
          this.PatrolSystemNodeData,
          {
            ...defaultNodeData,
            key: this.getTableIndexLabel(TableIndex.SosCfg),
            title: this.$t('writeFreq.sosRescueCfg'),
          },
        ]
      },
      menuTreeOpts() {
        return {
          source: this.menuTreeSource,
          checkbox: false,
          click: this.menuTreeNodeClick,
        }
      },
      menuTreeContextmenu() {
        return [
          {
            title: this.$t('dialog.add'),
            cmd: MenuTreeContextmenu.ADD,
          },
          {
            title: this.$t('dialog.delete'),
            cmd: MenuTreeContextmenu.DELETE,
          },
        ]
      },
      menuTreeContextmenuOption() {
        return {
          menu: this.menuTreeContextmenu,
          beforeOpen: this.contextmenuBeforeOpen,
          select: this.contextmenuSelect,
        }
      },
      btnGroupType() {
        return 'small'
      },

      hasFreqRange() {
        // TD910有多个频率，frequencyRange为数组，[{},{}...]
        return this.deviceWriteInfo.frequencyRange.some(frequency => {
          return frequency.max > 0 && frequency.min > 0
        })
      },
      disWriteBtn() {
        return this.disReadBtn || !this.selectedDeviceDmrId || !this.hasFreqRange || this.selectedChannels.length === 0
      },
      disReadBtn() {
        return this.noQWebServer || this.noDevice || this.isWriting || this.isReading
      },
      noQWebServer() {
        return !this.QWebServer
      },

      showTD910DeviceInfo() {
        return this.selectMenu === this.getTableIndexLabel(TableIndex.TD920DeviceInfo)
      },
      showGeneralSettings() {
        return this.selectMenu === this.getTableIndexLabel(TableIndex.GeneralSettings)
      },
      showButtonSettings() {
        return this.selectMenu === this.getTableIndexLabel(TableIndex.ButtonSettings)
      },
      showShortMessage() {
        return this.selectMenu === this.getTableIndexLabel(TableIndex.ShortMessage)
      },
      showEncryptSettings() {
        return this.selectMenu === this.getTableIndexLabel(TableIndex.EncryptSettings)
      },
      showMenuSettings() {
        return this.selectMenu === this.getTableIndexLabel(TableIndex.MenuSettings)
      },
      showSignalingSystem() {
        return this.selectMenu === this.getTableIndexLabel(TableIndex.SignalingSystem)
      },
      showAlertConfig() {
        return this.selectMenu === this.getTableIndexLabel(TableIndex.AlertConfig)
      },
      showDigitalAlert() {
        return this.selectMenu === this.getTableIndexLabel(TableIndex.DigitalAlert)
      },
      showGpsSettings() {
        return this.selectMenu === this.getTableIndexLabel(TableIndex.GpsSettings)
      },
      showDigitalAddress() {
        return this.selectMenu === this.getTableIndexLabel(TableIndex.DigitalAddress)
      },
      showAddressGroup() {
        return this.selectMenu === this.getTableIndexLabel(TableIndex.AddressGroup)
      },
      showPhoneBook() {
        return this.selectMenu === this.getTableIndexLabel(TableIndex.PhoneBook)
      },
      showRxGroup() {
        return this.selectMenu === this.getTableIndexLabel(TableIndex.RxGroup)
      },
      showZoneRootData() {
        return this.selectMenu === this.getTableIndexLabel(TableIndex.Channel) + '_root'
      },
      showZoneParentData() {
        return this.selectMenu === this.getTableIndexLabel(TableIndex.ZoneRoot)
      },
      showZoneLeafData() {
        return this.selectMenu === this.getTableIndexLabel(TableIndex.ZoneParent)
      },
      showZoneLeafChannels() {
        return this.selectMenu === this.getTableIndexLabel(TableIndex.ZoneLeaf)
      },
      showChannelItem() {
        return this.selectMenu === this.getTableIndexLabel(TableIndex.Channel)
      },
      showScan() {
        return this.selectMenu === this.getTableIndexLabel(TableIndex.Scan)
      },
      showRoam() {
        return this.selectMenu === this.getTableIndexLabel(TableIndex.Roam)
      },
      showPatrolSystem() {
        return this.selectMenu === this.getTableIndexLabel(TableIndex.PatrolConfig)
      },
      showSosSettings() {
        return this.selectMenu === this.getTableIndexLabel(TableIndex.SosCfg)
      },

      addressBookCallTypes() {
        return CallType
      },

      generalSettingsRules() {
        const pwdRule = [validateRules.mustLength(['change', 'blur'], 6), validateRules.mustNumber(['change', 'blur'])]

        return {
          deviceName: [validateRules.required(), validateRules.maxLen(['change', 'blur'], 16)],
          powerOnPassword: pwdRule,
          channelConfigPassword: pwdRule,
        }
      },
      signalingSystemRules() {
        return {
          signalingPwd: [validateRules.mustLength('blur', 6), validateRules.mustNumber('blur')],
        }
      },
      savePowerModeList() {
        return [
          {
            label: this.$t('dialog.nothing'),
            value: 0,
          },
          {
            label: '1:1',
            value: 1,
          },
          {
            label: '1:2',
            value: 2,
          },
          {
            label: '1:3',
            value: 3,
          },
          {
            label: '1:4',
            value: 4,
          },
        ]
      },
      allowCallInstructionList() {
        // 呼叫允许指示 000无,001 模拟,010模拟和数字,011数字
        return [
          {
            label: this.$t('dialog.nothing'),
            value: 0,
          },
          {
            label: this.$t('dialog.analog'),
            value: 1,
          },
          {
            label: this.$t('dialog.analogAndDigital'),
            value: 2,
          },
          {
            label: this.$t('dialog.digital'),
            value: 3,
          },
        ]
      },
      recordCompressionRatioList() {
        // 录音压缩比 0 不压缩, 1 3.5倍  当录音使能==0时不可用
        return [
          {
            label: this.$t('dialog.nonCompacting'),
            value: 0,
          },
          {
            label: '3.5',
            value: 1,
          },
        ]
      },
      // 常规设置下信道配置密码和U盘密码设置模式列表
      passwordModeList() {
        const baseList = [
          {
            label: this.$t('writeFreq.disable'),
            value: 0,
          },
          {
            label: this.$t('writeFreq.independentSettings'),
            value: 1,
          },
        ]

        // 6位有效密码
        if (this.menuSettings.powerOnPwd.length < 6) return baseList

        return baseList.concat([
          {
            label: this.$t('writeFreq.samePowerPassword'),
            value: 2,
          },
        ])
      },
      muteAll() {
        return this.generalSettings.soundSettings.muteAll
      },
      sosEnable() {
        return this.sosCfg.config.enable
      },
      syncTime() {
        return this.generalSettings.syncTime
      },
      dmrIdLabel() {
        if (this.generalSettings.intDmrId === 0) {
          return ''
        }
        const dmrId = bfutil.int32Dmrid2Hex(this.generalSettings.intDmrId)
        return dmrId ? ` ${dmrId} / ${this.generalSettings.intDmrId}` : ''
      },
      // month: {
      //   get() {
      //     return this.generalSettings.month + 1
      //   },
      //   set(val) {
      //     this.generalSettings.month = val - 1
      //   },
      // },
      // hours: {
      //   get() {
      //     return this.generalSettings.hour + this.generalSettings.timeZoneHour
      //   },
      //   set(val) {
      //     this.generalSettings.hour = val - this.generalSettings.timeZoneHour
      //   },
      // },
      minute: {
        get() {
          return this.generalSettings.minute + this.generalSettings.timeZoneMinute
        },
        set(val) {
          this.generalSettings.minute = val - this.generalSettings.timeZoneMinute
        },
      },
      maxDate() {
        const dates = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31]
        return dates[this.generalSettings.month]
      },
      powerTransferEnable() {
        return !this.generalSettings.powerTransfer.enable
      },

      buttonDefineLabelWidth() {
        return this.isEn ? '170px' : '130px'
      },
      softKeyFuncDefine() {
        return Object.keys(ButtonKeys)
          .filter(key => {
            return this.$te(`writeFreq.softKeyFuncDefine.${key}`)
          })
          .map(key => {
            return {
              label: this.$t(`writeFreq.softKeyFuncDefine.${key}`),
              value: ButtonKeys[key],
            }
          })
      },
      SoftKeyCallType() {
        return SoftKeyCallType
      },
      buttonDefineAddressList() {
        const def = [
          {
            label: this.$t('dialog.nothing'),
            value: 0xffff,
          },
        ]
        return def.concat(
          this.selectedAddressBook
            .filter(address => address.dmrId !== bfglob.fullCallDmrId)
            .map(address => {
              return {
                label: address.name,
                value: address.id,
              }
            })
        )
      },
      buttonDefineZoneRootList() {
        const defaultList = [
          {
            label: this.$t('dialog.nothing'),
            value: 0xffff,
          },
        ]
        return defaultList.concat(
          this.zoneRootDataList.map(item => {
            return {
              label: item.zoneName,
              value: item.zoneId,
            }
          })
        )
      },
      buttonDefineZoneParentList() {
        const emptyList = [
          {
            label: this.$t('dialog.nothing'),
            value: 0xffff,
          },
        ]
        // rootId => []
        const zoneCache = {
          0xffff: emptyList,
        }
        this.zoneParentDataList.forEach(item => {
          zoneCache[item.rootId] = zoneCache[item.rootId] || [...emptyList]
          zoneCache[item.rootId].push({
            label: item.zoneName,
            value: item.zoneId,
            parent: item.rootId,
          })
        })
        return zoneCache
      },
      buttonDefineZoneLeafList() {
        const emptyList = [
          {
            label: this.$t('dialog.nothing'),
            value: 0xffff,
          },
        ]
        // parentId => []
        const zoneCache = {
          0xffff: emptyList,
        }
        this.zoneLeafDataList.forEach(item => {
          zoneCache[item.parentId] = zoneCache[item.parentId] || [...emptyList]
          zoneCache[item.parentId].push({
            label: item.zoneName,
            value: item.zoneId,
            parent: item.parentId,
          })
        })
        return zoneCache
      },
      buttonDefineChannelList() {
        const emptyList = [
          {
            label: this.$t('dialog.nothing'),
            value: 0xffff,
          },
        ]
        // zoneId => []
        const zoneCache = {
          0xffff: emptyList,
        }
        this.channelDataList.forEach(item => {
          zoneCache[item.zoneId] = zoneCache[item.zoneId] || [...emptyList]
          zoneCache[item.zoneId].push({
            label: item.chName,
            value: item.chId,
            zoneId: item.zoneId,
          })
        })
        return zoneCache
      },

      encryptListActiveWidth() {
        return this.isFR ? '120px' : '100px'
      },
      encryptEnable() {
        return this.encryptConfig.config.encryptEnable
      },
      menuHangTimeLabelWidth() {
        return this.isEn ? '160px' : '120px'
      },
      freqDisplayList() {
        // 信道显示模式，0 频率显示，1 信道显示，2 频率+信道显示
        return [
          {
            label: this.$t('dialog.freqDisplay'),
            value: 0,
          },
          {
            label: this.$t('dialog.chDisplay'),
            value: 1,
          },
          {
            label: this.$t('dialog.freqAndChDisplay'),
            value: 2,
          },
        ]
      },
      callDisplayModeList() {
        // 呼叫显示模式，0 显示名字，1 显示号码，2 显示名字或别名，默认2
        return [
          {
            label: this.$t('writeFreq.displayName'),
            value: 0,
          },
          {
            label: this.$t('writeFreq.displayNumber'),
            value: 1,
          },
          {
            label: this.$t('writeFreq.displayNameNumber'),
            value: 2,
          },
        ]
      },

      digitalAlertComp() {
        return this.$refs.digitalAlert
      },
      addressBookGroupComp() {
        return this.$refs.addressBookGroup
      },
      addressBookComp() {
        return this.$refs[this.addrBookTreeId]
      },
      phoneBookComp() {
        return this.$refs[this.phoneBookTreeId]
      },
      receiveGroupComp() {
        return this.$refs[this.refReceiveGroup]
      },
      scanGroupComp() {
        return this.$refs.scanGroup
      },
      roamGroupComp() {
        return this.$refs.roamGroup
      },
      rootZoneComp() {
        return this.$refs.rootZone
      },
      parentZoneComp() {
        return this.$refs.parentZone
      },
      leafZoneComp() {
        return this.$refs.leafZone
      },
      zoneLeafChannelComp() {
        return this.$refs.zoneLeafChannel
      },

      // 列表类型数据索引
      encryptListIndex() {
        return this.createDataListIndex(this.encryptXORList, 'id')
      },
      encryptARC4ListIndex() {
        return this.createDataListIndex(this.encryptARC4List, 'id')
      },
      encryptAES256ListIndex() {
        return this.createDataListIndex(this.encryptAES256List, 'id')
      },
      digitalAlertListIndex() {
        return this.createDataListIndex(this.digitalAlertList)
      },
      addressBookGroupIndex() {
        return this.createDataListIndex(this.addressBookGroup)
      },
      zoneRootDataIndex() {
        return this.createDataListIndex(this.zoneRootDataList, 'zoneId')
      },
      zoneParentDataIndex() {
        return this.createDataListIndex(this.zoneParentDataList, 'zoneId')
      },
      zoneLeafDataIndex() {
        return this.createDataListIndex(this.zoneLeafDataList, 'zoneId')
      },
      channelsIndex() {
        return this.createDataListIndex(this.channelDataList, 'chId')
      },
      scanGroupIndex() {
        return this.createDataListIndex(this.scanGroup, 'scanId')
      },
      roamGroupIndex() {
        return this.createDataListIndex(this.roamGroup, 'roamId')
      },

      isDChannel() {
        return this.oneChannel.chType === 0
      },
      isAChannel() {
        return this.oneChannel.chType === 1
      },
      isConnectNetworking() {
        return this.oneChannel.networkConfig.networking
      },
      sameFreq() {
        return this.oneChannel.rxFreq === this.oneChannel.txFreq
      },
      throughEnable() {
        return this.oneChannel.timeSlotConfig.DCDMEnable
      },
      onlyReceive() {
        return this.oneChannel.scanConfig.onlyReceive
      },
      disAutoScan() {
        return this.isConnectNetworking || this.oneChannel.scanConfig.ipSiteConnect || this.oneChannel.scanList === 0xff
      },
      disAutoRoam() {
        return !this.oneChannel.scanConfig.ipSiteConnect || this.oneChannel.scanList === 0xff
      },
      disEncryptConfigEnable() {
        return !this.encryptConfig.config.encryptEnable
      },
      disEncryption() {
        return this.disEncryptConfigEnable || !this.oneChannel.encryptConfig.enable
      },
      freqOffsetRules() {
        return []
      },
      channelRules() {
        // const defFreq = this.defaultFrequency
        // const minFrequency = this.deviceWriteInfo.minFrequency
        // const maxFrequency = this.deviceWriteInfo.maxFrequency
        // const min = minFrequency ? bfutil.frequencyMhz2Hz(minFrequency) : defFreq.value
        // const max = maxFrequency ? bfutil.frequencyMhz2Hz(maxFrequency) : defFreq.value + 80000000
        // const smg = `${minFrequency || bfutil.frequencyHz2Mhz(defFreq.value) || 0}~${maxFrequency ||
        // bfutil.frequencyHz2Mhz(defFreq.value + 80000000)}`
        // const frequency = [
        //   validateRules.required(['blur']),
        //   validateRules.range(['blur'], min, max, smg)
        // ]
        return {
          chName: [validateRules.required(['blur'])],
          // rxFreq: frequency,
          // txFreq: frequency
        }
      },
      chTypeList() {
        // 暂时不支持兼容信道
        return [
          {
            label: this.$t('dialog.digitalChannel'),
            value: 0,
          },
          {
            label: this.$t('dialog.analogChannel'),
            value: 1,
          },
          // { label: this.$t('writeFreq.digitalCompatibleAnalog'), value: 2 },
          // { label: this.$t('writeFreq.analogCompatibleDigital'), value: 3 }
        ]
      },
      bandwidthFlagList() {
        return [
          {
            label: this.$t('writeFreq.broadBand'),
            value: 0,
          },
          {
            label: this.$t('writeFreq.narrowBand'),
            value: 1,
          },
        ]
      },
      disableScanList() {
        if (this.oneChannel.scanConfig.ipSiteConnect) {
          return false
        }

        return this.isConnectNetworking
      },
      chScanRoamGroupList() {
        const list = [
          {
            label: this.$t('dialog.nothing'),
            value: 0xff,
          },
          ...this.scanGroup.map(item => {
            return {
              label: item.name,
              value: item.scanId,
            }
          }),
        ]

        if (this.oneChannel.scanConfig.ipSiteConnect) {
          return list.concat(
            this.roamGroup.map(item => {
              return {
                label: item.name,
                value: (1 << 8) + item.roamId,
              }
            })
          )
        }

        return list
      },
      chTimeSlotCalList() {
        return [
          {
            label: this.$t('dataTable.fail'),
            value: 0,
          },
          {
            label: this.$t('dataTable.pass'),
            value: 1,
          },
          {
            label: this.$t('writeFreq.firstChoice'),
            value: 2,
          },
        ]
      },
      slotModeList() {
        return [
          {
            label: '1',
            value: 0,
          },
          {
            label: '2',
            value: 1,
          },
          {
            label: this.$t('dialog.virtualCluster'),
            value: 2,
          },
        ]
      },
      virtualTimeSlotList() {
        return [
          {
            label: this.$t('dialog.nothing'),
            value: 0,
          },
          {
            label: '1',
            value: 1,
          },
          {
            label: '2',
            value: 2,
          },
        ]
      },
      preEmphasisList() {
        return [
          {
            label: this.$t('dialog.nothing'),
            value: 0,
          },
          {
            label: this.$t('writeFreq.deEmphasisAndPreEmphasis'),
            value: 1,
          },
        ]
      },
      encryptTypeList() {
        // 加密类型，0 基础，1 高级 default = 0
        return [
          {
            label: this.$t('writeFreq.base'),
            value: 0,
          },
          {
            label: this.$t('writeFreq.advanced'),
            value: 1,
          },
        ]
      },
      algorithmList() {
        // 加密算法，
        // 基础类型：0 异或，1 增强异或，2 ARC4，3 AES256，default=0
        // 高级类型：0 ARC4，1 AES256
        if (this.oneChannel.encryptConfig.type === 0) {
          return [
            {
              label: this.$t('writeFreq.xor'),
              value: 0,
            },
            {
              label: this.$t('writeFreq.enhancedXor'),
              value: 1,
            },
            {
              label: 'ARC4',
              value: 2,
            },
            {
              label: 'AES256',
              value: 3,
            },
          ]
        }

        return [
          {
            label: 'ARC4',
            value: 0,
          },
          {
            label: 'AES256',
            value: 1,
          },
        ]
      },
      encryptKeyList() {
        // 密钥列表序号，根据加密类型不同选择不同密钥列表

        const defaultList = [
          {
            label: this.$t('dialog.nothing'),
            value: 0xff,
          },
        ]

        // 基础类型：对应基础密钥列表
        if (this.oneChannel.encryptConfig.type === 0) {
          return defaultList.concat(
            this.encryptXORList.map(item => {
              return {
                label: item.name,
                value: item.id,
              }
            })
          )
        }

        // 高级类型：ARC4对应ARC4密钥列表；AES256对应AES256列表
        if (this.oneChannel.encryptConfig.algorithm === 0) {
          return defaultList.concat(
            this.encryptARC4List.map(item => {
              return {
                label: item.name,
                value: item.id,
              }
            })
          )
        }

        return defaultList.concat(
          this.encryptAES256List.map(item => {
            return {
              label: item.name,
              value: item.id,
            }
          })
        )
      },
      subtoneCodeDataList() {
        const def = [
          {
            label: this.$t('dialog.nothing'),
            value: 0xffff,
          },
        ]
        const getList = codes => {
          return Object.keys(codes).map(key => {
            return {
              label: key,
              value: codes[key],
            }
          })
        }

        switch (this.oneChannel.chType) {
          case 0:
            return def
          case 1:
            return def.concat(getList(AnalogCodeData))
          default:
            return getList(AnalogDigitalCodeData)
        }
      },
      tailToneList() {
        return [
          {
            label: this.$t('writeFreq.noSubtone'),
            value: 0,
          },
          {
            label: this.$t('writeFreq.standardPhase'),
            value: 1,
          },
          {
            label: this.$t('writeFreq.nonstandardPhase'),
            value: 2,
          },
        ]
      },
      disableCtcssRxDps() {
        // 亚音码高字节的最高2位为数字亚音标识
        return this.oneChannel.subsonicDecode === 0xffff || this.oneChannel.subsonicDecode >> 14 > 0
      },
      disableCtcssTxDps() {
        // 亚音码高字节的最高2位为数字亚音标识
        return this.onlyReceive || this.oneChannel.subsonicEncode === 0xffff || this.oneChannel.subsonicEncode >> 14 > 0
      },
      receiveGroupList() {
        const def = [
          {
            label: this.$t('dialog.nothing'),
            value: 0xff,
          },
        ]
        const rxGroupList = this.rxGroupList.map(item => {
          return {
            label: item.groupName,
            value: item.groupId,
          }
        })
        return def.concat(rxGroupList)
      },
      defaultAddressList() {
        const cache = []
        return [
          {
            label: this.$t('dialog.nothing'),
            value: 0xffff,
          },
        ].concat(
          this.selectedAddressBook
            .concat(this.originAddressBook)
            .filter(address => {
              if (cache.includes(address.id)) {
                return false
              }
              cache.push(address.id)
              return true
            })
            .map(address => {
              return {
                label: address.name,
                value: address.id,
              }
            })
        )
      },
      emergencySysIdList() {
        const def = [
          {
            label: this.$t('dialog.nothing'),
            value: 0xff,
          },
        ]
        const digitAlarmList = this.digitalAlertList.map(item => {
          return {
            label: item.name,
            value: item.id,
          }
        })
        return def.concat(digitAlarmList)
      },
      txPowerTypes() {
        // 0 低功率 1 高功率 2 中功率
        return [
          {
            label: this.$t('dialog.low'),
            value: 0,
          },
          {
            label: this.$t('dialog.mid'),
            value: 2,
          },
          {
            label: this.$t('dialog.high'),
            value: 1,
          },
        ]
      },
      permissionConditionsList() {
        // 0 可用彩色码 1 始终 2 信道空闲
        return [
          {
            label: this.$t('dialog.availableColorCode'),
            value: 0,
          },
          {
            label: this.$t('writeFreq.always'),
            value: 1,
          },
          {
            label: this.$t('dialog.channelIdle'),
            value: 2,
          },
        ]
      },
      busyChannelLockList() {
        return [
          {
            label: this.$t('dialog.off'),
            value: 0,
          },
          {
            label: this.$t('writeFreq.carrier'),
            value: 1,
          },
          {
            label: 'CTCSS/CDCSS',
            value: 2,
          },
        ]
      },
      channelSettingComp() {
        return this.$refs.channelSetting
      },
      patrolConfigComp() {
        return this.$refs.patrolConfig
      },
      emergencyAlarmComp() {
        return this.$refs.emergencyAlarm
      },
      directMode() {
        return this.generalSettings.baseSettings.directMode
      },
      contactsDisabled() {
        return !this.menuSettings.contactConfig.contacts
      },
      phoneConfigDisabled() {
        return !this.menuSettings.phoneConfig.enable
      },
      scanConfigDisabled() {
        return !this.menuSettings.scanConfig.menuEnable
      },
      roamConfigDisabled() {
        return !this.menuSettings.roamConfig.menuEnable
      },
      smsConfigDisabled() {
        return !this.menuSettings.smsConfig.enable
      },
      callConfigDisabled() {
        return !this.menuSettings.callConfig.callRecord
      },
      recordConfigDisabled() {
        return !this.menuSettings.recordConfig.menuEnable
      },
      deviceSettingDisabled() {
        return !this.menuSettings.deviceConfig.deviceSetting
      },
      channelSettingDisabled() {
        return !this.menuSettings.channelSetting.channelConfigMenuEnable
      },

      workAloneUnEnable() {
        return !this.alertConfig.aloneWorkEnable
      },
      aloneWorkOptList() {
        return [
          {
            label: this.$t('writeFreq.button'),
            value: 0,
          },
          {
            label: this.$t('writeFreq.voiceLaunch'),
            value: 1,
          },
        ]
      },
      writeDataOption() {
        return [
          {
            type: TableIndex.GeneralSettings,
            failedMsg: this.$t('msgbox.writeRegularSettingsFailed'),
          },
          {
            type: TableIndex.ButtonSettings,
            failedMsg: this.$t('msgbox.writeDeySettingsFailed'),
          },
          {
            type: TableIndex.ShortMessage,
            failedMsg: this.$t('msgbox.writeSMSFailed'),
            option: { limit: 1 },
          },
          {
            type: TableIndex.EncryptSettings,
            failedMsg: this.$t('msgbox.writeEncryptConfigFailed'),
          },
          {
            type: TableIndex.EncryptList,
            failedMsg: this.$t('msgbox.writeEncryptKeyFailed'),
          },
          {
            type: TableIndex.ARC4List,
            failedMsg: this.$t('msgbox.writeEncryptionARC4KeyFailed'),
          },
          {
            type: TableIndex.AES256List,
            // 3个密钥一个数据包，超出了320字节
            option: { limit: 2 },
            failedMsg: this.$t('msgbox.writeEncryptionAES256KeyFailed'),
          },
          {
            type: TableIndex.GpsSettings,
            failedMsg: this.$t('msgbox.writeGpsDataFailed'),
          },
          {
            type: TableIndex.MenuSettings,
            failedMsg: this.$t('msgbox.writeMenuFailed'),
          },
          {
            type: TableIndex.SignalingSystem,
            failedMsg: this.$t('msgbox.writeSignalingSystemFailed'),
          },
          {
            type: TableIndex.AlertConfig,
            failedMsg: this.$t('msgbox.writeAlarmConfigFailed'),
          },
          {
            type: TableIndex.DigitalAlert,
            failedMsg: this.$t('msgbox.writeDigitalAlarmFailed'),
          },
          {
            type: TableIndex.AddressGroup,
            failedMsg: this.$t('msgbox.writeAddressBookGroupFailed'),
          },
          {
            type: TableIndex.DigitalAddress,
            failedMsg: this.$t('msgbox.writeAddressBookFailed'),
          },
          {
            type: TableIndex.PhoneBook,
            failedMsg: this.$t('msgbox.writePhoneBookFailed'),
          },
          {
            type: TableIndex.RxGroup,
            failedMsg: this.$t('msgbox.writeReceivingGroupFailed'),
          },
          {
            type: TableIndex.ZoneRoot,
            failedMsg: this.$t('msgbox.writeLevel1ZoneFailed'),
          },
          {
            type: TableIndex.ZoneParent,
            failedMsg: this.$t('msgbox.writeLevel2ZoneFailed'),
          },
          {
            type: TableIndex.ZoneLeaf,
            failedMsg: this.$t('msgbox.writeLevel3ZoneFailed'),
            option: { limit: 2 },
          },
          {
            type: TableIndex.Channel,
            failedMsg: this.$t('msgbox.writeChannelDataFailed'),
            option: { limit: 2 },
          },
          {
            type: TableIndex.Scan,
            failedMsg: this.$t('msgbox.writeScanListFailed'),
          },
          {
            type: TableIndex.Roam,
            failedMsg: this.$t('msgbox.writeRoamListFailed'),
          },
          {
            type: TableIndex.PatrolConfig,
            failedMsg: this.$t('msgbox.writePatrolSystemConfigFailed'),
          },
          {
            type: TableIndex.EmergencyAlarm,
            failedMsg: this.$t('msgbox.writeEmergencyAlarmConfigFailed'),
          },
          {
            type: TableIndex.AutoPosMonitor,
            failedMsg: this.$t('msgbox.writeTraceMonitorConfigFailed'),
          },
          {
            type: TableIndex.SosCfg,
            failedMsg: this.$t('msgbox.writeRescueAndSOSConfigFailed'),
          },
          {
            type: TableIndex.SosChData,
            failedMsg: this.$t('msgbox.writeRescueAndSOSChannelFailed'),
          },
          // 最后写入编程密码
          {
            type: TableIndex.Password,
            failedMsg: this.$t('msgbox.writeProgrammingPwdFailed'),
          },
        ]
      },
      getClassInstance() {
        return getClassInstance
      },
      Model() {
        return Model
      },
      notSettable() {
        return true
      },
      upsideDownUnEnable() {
        return !this.alertConfig.upendEnable
      },
      triggerModeList() {
        // 触发方式 "0：仅倾斜 1：仅运动检测 2：倾斜或运动检测"
        return [
          {
            label: this.$t('writeFreq.onlyTilt'),
            value: 0,
          },
          {
            label: this.$t('writeFreq.motionDetectionOnly'),
            value: 1,
          },
          {
            label: this.$t('writeFreq.tiltOrMotionDetection'),
            value: 2,
          },
        ]
      },
      triggerTiltList() {
        // 触发倾斜度 "0:60度 1:45度 2:30度"
        return [
          {
            label: 60,
            value: 0,
          },
          {
            label: 45,
            value: 1,
          },
          {
            label: 30,
            value: 2,
          },
        ]
      },
      gpsModeList() {
        // GPS工作模式 0:省电模式, 1:高性能模式
        return [
          {
            label: this.$t('writeFreq.off'),
            value: 0xff,
          },
          {
            label: this.$t('writeFreq.powerSavingMode'),
            value: 0,
          },
          {
            label: this.$t('writeFreq.highPerformanceMode'),
            value: 1,
          },
        ]
      },
      gpsSettingsEnable() {
        return this.gpsSettings.baseConfig.enable
      },
      trailCtrlDisable() {
        return !this.trailCtrl.menuConfig.enable
      },
      tableIndex() {
        return TableIndex
      },
    },
    watch: {
      sosChannelId(val) {
        this.sosChannel = this.sosChannelList[val]
      },
      smsContent: {
        deep: true,
        handler(data) {
          // 短信内容变化时，检测按键定义中单键呼叫功能的设置
          this.detectButtonDefinedFromSmsChange()
        },
      },
      // mixins计算属性
      selectDeviceData(data) {
        this.clearPrivateConfig()
        this.$nextTick(() => {
          if (data) {
            // 将选中的设备中关于信道等数据同步到界面中
            this.syncDeviceDataIntoConfig(data)
          }
        })
      },
      '$i18n.locale'(val) {
        // 切换语言后，重新更新菜单树节点名称
        this.updateNodeTitleFromLocaleChange()
        this.treeLoaded()
      },
      disEncryptConfigEnable(val) {
        if (val) {
          if (!this.isAChannel && this.oneChannel.encryptConfig) {
            this.oneChannel.encryptConfig.enable = false
          }
        }
      },
      isConnectNetworking(val) {
        if (val) {
          this.oneChannel.scanConfig.autoScan = false
          this.oneChannel.scanConfig.autoRoam = false
          this.oneChannel.scanListWrap = 0xff
          if (this.oneChannel.alertConfig) {
            this.oneChannel.alertConfig.emergencyAlertTip = false
            this.oneChannel.alertConfig.emergencyAlertConfirm = false
            this.oneChannel.alertConfig.emergencyCallTip = false
          }
          this.oneChannel.emergencySysId = 0xff
        } else {
          if (this.oneChannel.networkConfig) {
            this.oneChannel.networkConfig.localCall = false
          }
        }
      },
      onlyReceive(val) {
        if (val) {
          this.oneChannel.scanConfig.ipSiteConnect = false
          this.oneChannel.networkConfig.voiceDuplex = false
        }
      },
      'oneChannel.timeSlotConfig.timeSlot'(val) {
        if (!this.isAChannel) {
          if (val !== 2 && this.oneChannel.timeSlotConfig) {
            this.oneChannel.timeSlotConfig.virtualTimeSlot = 0
          }
        }
      },
      'oneChannel.scanConfig.ipSiteConnect'(val) {
        this.oneChannel.scanListWrap = 0xff
        // IP站点连接未勾选，则扫描列表不可能选择漫游组和自动漫游配置
        if (!val) {
          this.oneChannel.scanConfig.autoRoam = false
        }
      },
      'oneChannel.timeSlotConfig.throughEnable'(val) {
        if (!val) {
          this.oneChannel.timeSlotConfig.chSlotAdjust = 0
        } else {
          // TDMA直通模式开启后，彩色码最大只能为14，如果超出则需修正
          if (this.oneChannel.colorCode > 14) {
            this.oneChannel.colorCode = 0
          }
        }
      },
      sameFreq(val) {
        if (val) {
          this.oneChannel.scanConfig.ipSiteConnect = false
          this.oneChannel.scanConfig.allowOfflineSign = false
        } else {
          if (this.oneChannel.timeSlotConfig) {
            this.oneChannel.timeSlotConfig.throughEnable = false
          }
        }
      },
      'alertConfig.aloneWorkTime'(val) {
        const aloneWorkTime = val * 60
        if (this.alertConfig.remindTime > aloneWorkTime) {
          this.alertConfig.remindTime = aloneWorkTime
        }
      },
    },
    components: {
      WriteFreqFooter,
      selectDevice,
      deviceInfo,
      TableTree,
      bfInputNumber: defineAsyncComponent(() => import('@/components/common/bfInputNumber')),
      addressBook: defineAsyncComponent(() => import('@/platform/dataManage/deviceManage/common/addressBook.vue')),
      phoneBook: defineAsyncComponent(() => import('@/platform/dataManage/deviceManage/common/phoneBook.vue')),
      digitalAlert: defineAsyncComponent(() => import('@/platform/dataManage/deviceManage/common/digitalAlert')),
      frequencyMhz: defineAsyncComponent(() => import('@/components/common/FrequencyMhz')),
      shortMessage: defineAsyncComponent(() => import('@/platform/dataManage/deviceManage/common/shortMessage')),
      receiveGroup: defineAsyncComponent(() => import('@/platform/dataManage/deviceManage/common/receiveGroup')),
      patrolConfig: defineAsyncComponent(() => import('@/platform/dataManage/deviceManage/common/patrolConfig')),
      emergencyAlarmConfig: defineAsyncComponent(() => import('@/platform/dataManage/deviceManage/common/emergencyAlarmConfig')),
      addressBookGroup: defineAsyncComponent(() => import('@/platform/dataManage/deviceManage/common/addressBookGroup')),
      scanGroup: defineAsyncComponent(() => import('@/platform/dataManage/deviceManage/common/scanGroup')),
      roamGroup: defineAsyncComponent(() => import('@/platform/dataManage/deviceManage/common/roamGroup')),
      multistageZone: defineAsyncComponent(() => import('@/platform/dataManage/deviceManage/common/multistageZone')),
      zoneLeafTable: defineAsyncComponent(() => import('@/platform/dataManage/deviceManage/common/zoneLeafTable')),
      freqMapOffset: defineAsyncComponent(() => import('@/platform/dataManage/deviceManage/common/freqMapOffset')),
      encryptSettings: defineAsyncComponent(() => import('@/platform/dataManage/deviceManage/common/encryptSettings.vue')),
    },
    mounted() {
      this.detectButtonDefinedFromSmsChange = debounce(this.detectButtonDefinedFromSmsChange, 500)
      bfglob.on('wf:redrawTree', this.redrawViewport)

      // 常规配置的语言选项，根据语言选择重置默认值
      if (this.generalSettings.locale !== undefined) {
        const localeList = this.localeList ?? []
        const localeOption = localeList.find(opt => opt.value === this.generalSettings.locale)
        this.generalSettings.locale = localeOption?.value ?? localeList[0]?.value ?? 0
      }
    },
    beforeUnmount() {
      bfglob.off('wf:redrawTree', this.redrawViewport)
    },
    beforeMount() {
      // this.initSosChannelList()
    },
  }
</script>

<style lang="scss">
  @use '@/css/interphoneWf/button-define.scss' as *;
  @use '@/css/interphoneWf/tree-card-layout.scss' as *;
</style>
