<template>
  <el-header class="top-0 !h-[146.5px] w-full flex" :class="props.showHeadBtnGroup ? 'justify-center' : 'justify-start'">
    <!-- 左 -->
    <div class="relative w-[512px]">
      <div
        class="absolute top-4 left-16 ml-[10px] w-[294px] h-[111px]"
        :class="isLogin ? 'bg-no-repeat bg-center bg-contain' : 'bf-logo'"
        :style="getLogoStyle()"
      />
    </div>

    <!-- 中 -->
    <div class="w-[896px] text-white text-center flex flex-col align-center">
      <!-- 滚动标题 -->
      <ScrollingText v-if="shouldShowScrollingTitle" ref="scrollingTextRef" :title="defaultTitle" :scrollStatus="shouldShowScrollingTitle" />
      <!-- 普通标题 -->
      <EllipsisText
        v-else
        class="you-she-biao-ti-hei text-[44px] m-0 !w-[644px] !h-[50px] !leading-[50px]"
        :content="defaultTitle"
        style="background: linear-gradient(to bottom, #ffffff 32%, #245398 100%); -webkit-background-clip: text; background-clip: text; color: transparent"
      />
      <EllipsisText class="tracking-[2px] !w-[644px] !mb-[16px]" :content="defaultSubTitle" />
      <div class="head-menu w-[777px] h-[50.5px] flex justify-center gap-2" v-if="props.showHeadMenu">
        <div
          v-for="item in headerMenuItems"
          :key="item.name"
          class="cursor-pointer menu-item you-she-biao-ti-hei text-[21px] w-[152px] h-[31.5px] mt-[6px]"
          :class="{ 'menu-selected': isSelected(item.path) }"
          @click="router.push({ name: item.name })"
        >
          <EllipsisText :content="unref(item.meta?.navItemConfig).label ?? item.name" />
        </div>
      </div>
    </div>

    <!-- 右 -->
    <div class="w-[512px] text-white flex justify-start gap-12" v-if="props.showHeadBtnGroup">
      <div class="w-[124px] h-[44px] mt-[40px] ml-[30px] flex align-center gap-4 border-r-1 border-[#2A7CAE]">
        <img :src="serverStatusImg" class="!w-6 !h-6" />

        <div class="!h-[44px] flex flex-col items-end text-[14px]">
          <!-- TODO 获取天气信息应该从服务器获取 -->
          <p class="m-0">多云转晴</p>
          <p class="m-0">
            {{ todayInChineseManual }}
          </p>
        </div>
      </div>
      <div class="w-[289px] h-[44px] mt-[40px] flex align-center gap-4">
        <SwitchLangs class="!w-[36px] !h-[36px]" />

        <div class="cursor-pointer w-[36px] h-[36px]" @click="openSettingsDialog">
          <img src="@/assets/images/common/setting_btn.svg" />
        </div>

        <div class="flex items-center gap-2 text-[14px]">
          <img :src="serverStatusImg" class="!w-6 !h-6" />
          <div class="!h-[44px] flex flex-col text-[14px]">
            <p class="m-0">{{ userTitle }}： {{ userName }}</p>
            <p class="m-0">
              {{ currentTime }}
            </p>
          </div>
        </div>
      </div>
    </div>

    <settingsComp v-if="settingsCompLoaded" v-model="settingsCompVisible" />
  </el-header>
</template>

<script setup>
  import { ref, computed, onMounted, onUnmounted, defineAsyncComponent, unref, reactive } from 'vue'
  import EllipsisText from '@/components/common/EllipsisText.vue'
  import ScrollingText from '@/components/common/scrollingText.vue'
  import SwitchLangs from '@/components/common/switchLangs.vue'
  import serverStatusReady from '@/assets/images/loginBg/server_status_ready.svg'
  import serverStatus from '@/assets/images/loginBg/server_status.svg'
  import { useRoute, useRouter } from 'vue-router'
  import { DateMask, TimeMask, nowLocalTime } from '@/utils/time.js'
  import dayjs from 'dayjs'
  import { useI18n } from 'vue-i18n'
  import { DataManageRouteName } from '@/router'

  const { t } = useI18n()
  const route = useRoute()
  const router = useRouter()
  const firstLevelRoute = route.path.split('/')[1]

  const StorageHeadInfoKey = 'storage-head-info'

  // 常量定义
  const DEFAULT_SUBTITLE = 'Smart Digital Connection System Platform'
  const DEFAULT_LOGO_2X = new URL('@/assets/images/loginBg/2x/logo.webp', import.meta.url).href
  const DEFAULT_LOGO_1X = new URL('@/assets/images/loginBg/1x/logo.webp', import.meta.url).href

  const headInfo = reactive({
    moveTitle: '',
    subTitle: '',
    scroll: false,
    logoImage: '',
  })

  // 通用的优先级获取函数
  const getPriorityValue = (systemValue, headInfoValue, defaultValue) => {
    if (isLogin.value) {
      return systemValue || headInfoValue || defaultValue
    } else {
      return headInfoValue || defaultValue
    }
  }

  // 通用的 headInfo 更新函数
  const updateHeadInfo = (key, value, defaultValue = '') => {
    headInfo[key] = value || defaultValue
    saveHeadInfoToStorage()
  }

  const isAdmin = computed(() => {
    return firstLevelRoute === DataManageRouteName
  })

  const userTitle = computed(() => {
    return isAdmin.value ? t('header.admin') : t('header.dispatcher')
  })

  const userName = computed(() => {
    return bfglob.userInfo?.name
  })

  const headerMenuItems = computed(() => {
    const route = router.getRoutes().find(route => route.name === firstLevelRoute)

    if (isAdmin.value) {
      return [route]
    }
    // 调度平台使用子路由
    return route?.children || []
  })

  const todayInChineseManual = computed(() => {
    const weekdays = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']
    const dayIndex = dayjs().day()
    return t(`dialog.${weekdays[dayIndex]}`)
  })

  const props = defineProps({
    showHeadMenu: {
      type: Boolean,
      default: true,
    },
    showHeadBtnGroup: {
      type: Boolean,
      default: true,
    },
  })

  // 响应式的默认标题 - 优先使用系统设置，然后使用 headInfo，最后使用默认值
  const defaultTitle = computed(() => getPriorityValue(bfglob.systemSetting?.clientTitle, headInfo.moveTitle, t('header.moveTitle')))

  // 响应式的副标题 - 优先使用系统设置，然后使用 headInfo，最后使用默认值
  const defaultSubTitle = computed(() => getPriorityValue(bfglob.systemSetting?.clientSubTitle, headInfo.subTitle, DEFAULT_SUBTITLE))

  // 滚动文本组件引用
  const scrollingTextRef = ref(null)

  // 响应式的登录状态和用户设置
  const isLogin = ref(bfglob.isLogin)

  // 是否显示滚动标题 - 直接使用优先级函数
  const shouldShowScrollingTitle = computed(() => getPriorityValue(bfglob.userInfo?.setting?.scrollMoveTitle, headInfo.scroll, false))

  // 自定义 Logo 图片路径（仅在登录后使用）
  const customLogoImage = ref(null)

  // 更新 logo 图片的函数
  const updateLogoImage = customLogo => {
    if (isLogin.value) {
      // 登录后：优先使用系统设置中的 logo，然后是传入的自定义 logo，最后是默认 logo
      customLogoImage.value = customLogo || bfglob.systemSetting?.clientLogo
      headInfo.logoImage = customLogo || bfglob.systemSetting?.clientLogo || ''
    } else {
      // 未登录时：直接使用传入的自定义 logo
      headInfo.logoImage = customLogo || ''
    }
    saveHeadInfoToStorage()
  }

  // 获取 logo 样式的函数
  const getLogoStyle = () => {
    const defaultLogo = isLogin.value ? DEFAULT_LOGO_2X : DEFAULT_LOGO_1X
    const systemLogo = isLogin.value ? bfglob.systemSetting?.clientLogo : null
    const customLogo = isLogin.value ? customLogoImage.value : null

    const logoUrl = systemLogo || customLogo || headInfo.logoImage || defaultLogo
    return { backgroundImage: `url(${logoUrl})` }
  }

  // 保存 headInfo 到 localStorage
  const saveHeadInfoToStorage = () => {
    try {
      localStorage.setItem(StorageHeadInfoKey, JSON.stringify(headInfo))
    } catch (error) {
      console.error('Failed to save headInfo to localStorage:', error)
    }
  }

  // 从 localStorage 读取 headInfo
  const loadHeadInfoFromStorage = () => {
    try {
      const stored = localStorage.getItem(StorageHeadInfoKey)
      if (stored) {
        const parsedData = JSON.parse(stored)
        Object.assign(headInfo, parsedData)
      }
    } catch (error) {
      console.error('Failed to load headInfo from localStorage:', error)
    }
  }

  // 监听系统标题变化（moveTitle 事件）
  const onMoveTitleChanged = title => {
    updateHeadInfo('moveTitle', title, t('header.moveTitle'))
  }

  // 监听系统副标题变化（moveSubTitle 事件）
  const onMoveSubTitleChanged = subTitle => {
    updateHeadInfo('subTitle', subTitle, DEFAULT_SUBTITLE)
  }

  // 监听用户设置变化
  const onUserSettingsChanged = newSettings => {
    updateHeadInfo('scroll', newSettings.scrollMoveTitle, false)
  }

  const connectStats = ref(1001)
  const serverStatusImg = computed(() => {
    return connectStats.value === 1001 ? serverStatusReady : serverStatus
  })

  const settingsCompVisible = ref(false)
  const settingsCompLoaded = ref(false)
  const settingsComp = defineAsyncComponent(() => import('@/components/dialogs/userSetting.vue'))
  const openSettingsDialog = () => {
    settingsCompLoaded.value = true
    settingsCompVisible.value = true
  }

  const currentTime = ref('')
  const updateTime = () => {
    currentTime.value = nowLocalTime(TimeMask + '  ' + DateMask)
  }
  let dateUpdateTimer = null

  const isSelected = path => {
    return route.path.toLowerCase().includes(path.toLowerCase())
  }

  onMounted(() => {
    // 从 localStorage 读取 headInfo
    loadHeadInfoFromStorage()

    updateTime()
    dateUpdateTimer = setInterval(updateTime, 1000)

    // 监听用户设置变化
    bfglob.on('update_user_settings', onUserSettingsChanged)

    // 监听系统标题变化
    bfglob.on('moveTitle', onMoveTitleChanged)

    // 监听系统副标题变化
    bfglob.on('moveSubTitle', onMoveSubTitleChanged)

    // 监听 logo 图片变化
    bfglob.on('logoImage', updateLogoImage)

    // 初始化标题：优先使用系统设置中的值
    if (bfglob.systemSetting?.clientTitle) {
      onMoveTitleChanged(bfglob.systemSetting.clientTitle)
    }

    // 初始化副标题：优先使用系统设置中的值
    if (bfglob.systemSetting?.clientSubTitle) {
      onMoveSubTitleChanged(bfglob.systemSetting.clientSubTitle)
    }
  })

  onUnmounted(() => {
    if (dateUpdateTimer) {
      clearInterval(dateUpdateTimer)
    }

    // 移除事件监听器
    bfglob.off('update_user_settings', onUserSettingsChanged)
    bfglob.off('moveTitle', onMoveTitleChanged)
    bfglob.off('moveSubTitle', onMoveSubTitleChanged)
    bfglob.off('logoImage', updateLogoImage)
  })
</script>

<style scoped lang="scss">
  .head-menu {
    background: url('@/assets/images/common/1x/head_nav.webp') no-repeat center;
    background-size: 100% 100%;

    .menu-item {
    }

    .menu-selected {
      background: url('@/assets/images/common/1x/nav_selected.webp') no-repeat center;
      background-size: 100% 100%;
    }
  }

  @media screen and (min-width: 2561px) {
    .head-menu {
      background: url('@/assets/images/common/2x/head_nav.webp') no-repeat center;
      background-size: 100% 100%;
    }

    .menu-item {
    }

    .menu-selected {
      background: url('@/assets/images/common/2x/nav_selected.webp') no-repeat center;
      background-size: 100% 100%;
    }
  }
</style>
