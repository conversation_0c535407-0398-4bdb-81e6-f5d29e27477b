<template>
  <div class="trace w-full h-full">
    <div id="traceMap" class="map-wrapper h-full">
      <!--地图缩放级别-->
      <div class="maplibregl-ctrl-mapLevel">
        <span class="mapLevelLabel" v-text="$t('map.mapLevel')" />
        <span class="mapLevel" v-text="mapLevel" />
      </div>

      <!--地图中心十字架-->
      <div class="map_coordinates" />
      <!--自定义控件按钮-->
      <el-tooltip :content="$t('map.zoomIn')" popper-class="bf-tooltip" :show-after="500" placement="left">
        <button ref="zoomIn" class="zoom_in_btn" @click="zoomIn" />
      </el-tooltip>
      <el-tooltip :content="$t('map.zoomOut')" popper-class="bf-tooltip" :show-after="500" placement="left">
        <button ref="zoomOut" class="zoom_out_btn" @click="zoomOut" />
      </el-tooltip>
      <el-tooltip :content="$t('map.resetBearingToNorth')" popper-class="bf-tooltip" :show-after="500" placement="left">
        <button ref="location" class="location_btn" @click="resetBearingToNorth" @mousedown="startCompassDrag" @mouseup="endCompassDrag" />
      </el-tooltip>
      <el-tooltip :content="$t('map.satellite')" popper-class="bf-tooltip" :show-after="500" placement="left">
        <button ref="satellite" class="satellite_btn" @click="ctrlIconToggleSapStyle(2)" />
      </el-tooltip>
      <el-tooltip :content="$t('map.streets')" popper-class="bf-tooltip" :show-after="500" placement="left">
        <button ref="streets" class="streets_btn" @click="ctrlIconToggleSapStyle(1)" />
      </el-tooltip>
      <el-tooltip :content="$t('map.stop')" popper-class="bf-tooltip" :show-after="500" placement="left">
        <button ref="quitMap" class="quitMap_btn" @click="close" />
      </el-tooltip>

      <trackCtrol
        v-if="showTrackCtrol"
        :visible="showTrackCtrol"
        :sliderMax="sliderMax"
        :end="playEnd"
        :sliderVal="currentIndex"
        @slider-change="sliderChange"
        @timeout="timeoutChange"
        @stop="stopAnimate"
        @play="playAnimate"
      />
    </div>
  </div>
</template>

<script>
  import maputil, { MapLibreProtoColName, MapStyleName, registerCustomProtocol, rpcUrl } from '@/utils/map'
  import { SupportedLang } from '@/modules/i18n'
  import maplibregl from 'maplibre-gl'
  import 'maplibre-gl/dist/maplibre-gl.css'
  import bfStorage from '@/utils/storage'
  import TrackCtrol from '@/components/common/trackCtrol.vue'
  import { BASE_URL } from '@/envConfig'
  import arrowPng from '@/images/mapImg/arrow.png'
  import bfNotify from '@/utils/notify'
  import '@/css/map.scss'

  const defaultCenter = [118.62188333333333, 25.003556666666668]
  export default {
    name: 'TraceMap',
    emits: ['close'],
    components: { TrackCtrol },
    data() {
      return {
        mapStyle: {
          streets: {},
          satellite: {},
        },
        personalMapSettings: {
          center: null,
          style: 'streets',
          zoom: 16,
        },
        mapLevel: 15,
        distance: -1,
        tileX: -1,
        tileY: -1,
        tileZ: -1,
        saveMapData: true,
        //轨迹播放相关属性
        showTrackCtrol: true,
        sliderMax: 1,
        coordinates: [],
        currentIndex: 0,
        checkDataTableBody: {},
        timeout: 1000,
        timers: null,
        trackPopup: null,
        playEnd: false,
        // 指南针拖动相关状态
        isDraggingCompass: false,
        dragStartX: 0,
        dragStartY: 0,
        initialBearing: 0,
      }
    },
    props: {
      traceData: {
        type: Array,
        required: true,
      },
      traceOrigin: {
        type: String,
        required: true,
      },
    },
    methods: {
      // 切换语言后，更新地图图层样式
      reloadCauseLangChanged() {
        if (this.map.isStyleLoaded()) {
          if (this.bfmap_has_hybrid_style()) {
            // 卫星图
            this.switchingMapStyle(2)
          } else {
            // 街道图
            this.switchingMapStyle(1)
          }
        } else {
          setTimeout(this.reloadCauseLangChanged, 100)
        }
      },
      close() {
        bfNotify.messageBox(this.$t('map.quitMapMsg'), 'info')
        this.$emit('close')
      },
      // 放大地图
      zoomIn() {
        this.map.zoomIn()
      },
      // 缩小地图
      zoomOut() {
        this.map.zoomOut()
      },
      // 重置地图方向到正北方向
      resetBearingToNorth() {
        // 重置地图的旋转角度和倾斜角度到默认值（正北方向）
        this.map.easeTo({
          bearing: 0, // 重置方向到正北
          pitch: 0, // 重置倾斜角度
          duration: 1000, // 动画持续时间
        })
      },
      // 开始指南针拖动
      startCompassDrag(event) {
        event.preventDefault()
        this.isDraggingCompass = true
        this.dragStartX = event.clientX
        this.dragStartY = event.clientY
        this.initialBearing = this.map.getBearing()

        // 添加全局鼠标移动和释放事件监听，支持超出按钮范围的拖动
        document.addEventListener('mousemove', this.onCompassDrag)
        document.addEventListener('mouseup', this.endCompassDrag)
      },
      // 指南针拖动过程中（支持全局拖动）
      onCompassDrag(event) {
        if (!this.isDraggingCompass) return

        const deltaX = event.clientX - this.dragStartX
        // const deltaY = event.clientY - this.dragStartY

        // 计算拖动的角度变化（基于鼠标移动的距离）
        const sensitivity = 0.5 // 调整灵敏度
        const bearingChange = deltaX * sensitivity

        // 计算新的方向
        const newBearing = (this.initialBearing + bearingChange) % 360

        // 应用新的方向
        this.map.setBearing(newBearing)
      },
      // 结束指南针拖动
      endCompassDrag() {
        if (!this.isDraggingCompass) return

        this.isDraggingCompass = false

        // 移除全局事件监听
        document.removeEventListener('mousemove', this.onCompassDrag)
        document.removeEventListener('mouseup', this.endCompassDrag)
      },
      mapResize() {
        this.map.resize()
      },
      setMapStyle() {
        // 获取地图语言类型
        const uiLang2MapLang = (() => {
          try {
            // zh-cn => zh_CN
            if (this.$i18n.locale.value === SupportedLang.zhCN) return 'zh_CN'
            return this.$i18n.locale.value
          } catch (_e) {
            // no-empty
          }

          return 'zh_CN'
        })()
        const roadmap_tiles = [`${MapLibreProtoColName}://${rpcUrl}/gmap?lang=` + uiLang2MapLang + '&mtype=roadmap&x={x}&y={y}&z={z}']
        const hybrid_tiles = [`${MapLibreProtoColName}://${rpcUrl}/gmap?lang=` + uiLang2MapLang + '&mtype=hybrid&x={x}&y={y}&z={z}']

        // 本地化资源，在开发环境下无法使用代理服务器
        // let glyphs = "mapbox://fonts/mapbox/{fontstack}/{range}.pbf";
        const path = `${window.location.origin}${BASE_URL}localMapbox`
        const glyphs = `${path}/glyphs/{fontstack}/{range}.pbf`
        const sprite = `${path}/sprite/sprite`

        const getMinzoom = () => {
          if (typeof bfglob.mapConfig.tilesMinzoom === 'number' && bfglob.mapConfig.tilesMinzoom >= 0 && bfglob.mapConfig.tilesMinzoom < 18) {
            return bfglob.mapConfig.tilesMinzoom
          }

          return 0
        }
        const getMaxzoom = () => {
          if (typeof bfglob.mapConfig.tilesMaxzoom === 'number' && bfglob.mapConfig.tilesMaxzoom > 0 && bfglob.mapConfig.tilesMaxzoom <= 18) {
            return bfglob.mapConfig.tilesMaxzoom
          }

          return 18
        }

        this.mapStyle = {
          streets: {
            name: MapStyleName.streets,
            version: 8,
            sources: {
              'roadmap-tiles': {
                type: 'raster',
                // point to our third-party tiles. Note that some examples
                // show a "url" property. This only applies to tilesets with
                // corresponding TileJSON (such as mapbox tiles).
                tiles: roadmap_tiles,
                tileSize: 256,
                minzoom: getMinzoom(),
                maxzoom: getMaxzoom(),
              },
            },
            layers: [
              {
                id: 'roadmap-tiles',
                type: 'raster',
                source: 'roadmap-tiles',
              },
            ],
            glyphs: glyphs,
            sprite: sprite,
          },
          satellite: {
            name: MapStyleName.satellite,
            version: 8,
            sources: {
              'hybrid-tiles': {
                type: 'raster',
                // point to our third-party tiles. Note that some examples
                // show a "url" property. This only applies to tilesets with
                // corresponding TileJSON (such as mapbox tiles).
                tiles: hybrid_tiles,
                tileSize: 256,
                minzoom: getMinzoom(),
                maxzoom: getMaxzoom(),
              },
            },
            layers: [
              {
                id: 'hybrid-tiles',
                type: 'raster',
                source: 'hybrid-tiles',
              },
            ],
            glyphs: glyphs,
            sprite: sprite,
          },
        }
      },
      getMapStyle() {
        const styleName = this.personalMapSettings.style || 'streets'
        return this.mapStyle[styleName]
      },
      getLocalCenter() {
        return this.personalMapSettings.center || bfglob.mapConfig.mapCenter || defaultCenter
      },
      getLocalZoom() {
        return this.personalMapSettings.zoom || 16
      },
      getMapConfigMaxZoom() {
        if (typeof bfglob.mapConfig.maxZoom === 'number' && bfglob.mapConfig.maxZoom > 0 && bfglob.mapConfig.maxZoom <= 23) {
          return bfglob.mapConfig.maxZoom
        }
        return bfglob.mapSetting.maxZoom || 23
      },
      getMapConfigMinZoom() {
        if (typeof bfglob.mapConfig.minZoom === 'number' && bfglob.mapConfig.minZoom >= 0 && bfglob.mapConfig.minZoom < 23) {
          return bfglob.mapConfig.minZoom
        }
        return bfglob.mapSetting.minZoom || 1.5
      },
      // 修复切换地图图层语言后，缩放控件没有切换title属性问题
      resetMapboxCtrlTitle() {
        document.querySelector('.maplibregl-ctrl-zoom-in')?.setAttribute('title', this.$t('map.zoomIn'))
        document.querySelector('.maplibregl-ctrl-zoom-out')?.setAttribute('title', this.$t('map.zoomOut'))
        document.querySelector('.maplibregl-ctrl-compass')?.setAttribute('title', this.$t('map.resetBearingToNorth'))
      },
      loadCustormCtrlIcon() {
        // 创建自定义控件容器
        const customControlContainer = document.createElement('div')
        customControlContainer.className = 'maplibregl-ctrl maplibregl-ctrl-group custom-map-controls'

        // 添加所有按钮到容器中
        customControlContainer.appendChild(this.$refs.zoomIn)
        customControlContainer.appendChild(this.$refs.zoomOut)
        customControlContainer.appendChild(this.$refs.location)
        customControlContainer.appendChild(this.$refs.satellite)
        customControlContainer.appendChild(this.$refs.streets)
        customControlContainer.appendChild(this.$refs.quitMap)

        // 创建自定义控件类
        class CustomMapControls {
          onAdd(map) {
            this._map = map
            this._container = customControlContainer
            return this._container
          }

          onRemove() {
            this._container.parentNode.removeChild(this._container)
            this._map = undefined
          }
        }

        // 添加控件到地图右上角
        this.map.addControl(new CustomMapControls(), 'top-right')
      },
      mapOnload() {
        this.mapResize()
        this.map.on('zoom', this.onMapZoom)
      },

      initBfMap() {
        this.map = new maplibregl.Map({
          container: 'traceMap',
          style: this.getMapStyle(),
          center: this.getLocalCenter(),
          zoom: this.getLocalZoom(),
          attributionControl: false,
          maxZoom: this.getMapConfigMaxZoom(),
          minZoom: this.getMapConfigMinZoom(),
          transformRequest: (url, resourceType) => {
            if (resourceType === 'Tile') {
              return {
                url: `${url}&bftk=${bfglob.bftk}&bfsid=${bfglob.sessionId}`,
              }
            }
            return { url }
          },
        })
        this.mapLevel = this.map.getZoom().toFixed(2)
        // 不添加默认的 NavigationControl，使用自定义按钮
        // this.map.addControl(new maplibregl.NavigationControl())
        this.resetMapboxCtrlTitle()
        this.loadCustormCtrlIcon()

        // 监听地图事件程序
        this.map.on('load', this.mapOnload)
      },
      hideTilexyz() {
        this.tileX = -1
        this.tileY = -1
        this.tileZ = -1
      },
      ctrlIconToggleSapStyle(stats) {
        // 判断是否需要切换地图
        // roadmap-tiles 街道图，hybrid-tiles 卫星图
        const current_style = this.map.getStyle()
        const is_roadmap = stats === 1 && 'roadmap-tiles' in current_style.sources
        const is_hybrid = stats === 2 && 'hybrid-tiles' in current_style.sources
        if (is_roadmap || is_hybrid) {
          return
        }
        this.switchingMapStyle(stats)
      },
      switchingMapStyle(stats) {
        bfglob.emit('mapStyleReloadBefore')
        const style = stats === 1 ? this.mapStyle.streets : this.mapStyle.satellite
        this.map.setStyle(style)
        this.listen_mapStyle_isloaded()
      },
      listen_mapStyle_isloaded() {
        if (this.map.isStyleLoaded()) {
          if (maputil.close_mapLaye_btn) {
            maputil.close_mapLaye_btn.remove()
            maputil.close_mapLaye_btn = null
          }
          // 清除轨迹点并重新加载
          this.clearlCheckLayers()
          this.addTrackLayers()
        } else {
          setTimeout(this.listen_mapStyle_isloaded, 100)
        }
      },
      bfmap_has_hybrid_style() {
        let has_hybrid = false
        // if ('hybrid-tiles' in bfglob.map.getStyle().sources) {
        if (this.map.getStyle().name === MapStyleName.satellite) {
          has_hybrid = true
        }
        return has_hybrid
      },
      initPersonalMapSettings() {
        const saveMapData = bfStorage.getItem('bfdx_saveMapData')
        if (saveMapData) {
          this.personalMapSettings = JSON.parse(saveMapData)
        }
      },
      onMapZoom() {
        const zoom = this.map.getZoom()
        // 更新地图级别的显示
        this.mapLevel = zoom.toFixed(2)
      },

      //轨迹播放相关方法
      initCoordinates(dataTableBody) {
        this.coordinates = dataTableBody.map(item => {
          const point = bfglob.glinePoints.get(item.pointId)
          if (point) {
            // 巡查历史
            return [point.lon, point.lat]
          } else {
            // 定位轨迹
            return [item.lon, item.lat]
          }
        })
      },
      sliderChange(val) {
        this.currentIndex = val
        // 当播放到最后一个时，则结束播放状态
        if (this.currentIndex === this.coordinates.length) {
          this.stop()
          return
        }
        this.trackPopup.setLngLat(this.coordinates[this.currentIndex]).setHTML(this.setPopupInfo(this.traceData)).addTo(this.map)
      },
      timeoutChange(val) {
        this.timeout = val
      },
      stopAnimate() {
        clearTimeout(this.timers)
        this.clearlCheckLayers()
        if (this.trackPopup) {
          this.trackPopup.remove()
          this.trackPopup = null
        }
        this.visible = true
        this.showTrackCtrol = false
        this.currentIndex = 0
        this.timeout = 500
        this.close()
      },
      playAnimate(isplay) {
        isplay && this.play()
        !isplay && clearTimeout(this.timers)
      },
      play() {
        if (this.currentIndex < this.coordinates.length) {
          this.updateTrackPopup(this.traceData)
          this.currentIndex++
          this.timers = setTimeout(this.play, this.timeout)
          this.playEnd = false
        } else {
          this.stop()
        }
      },
      stop() {
        // 播放完毕，恢复动画初始状态
        this.playEnd = true
        clearTimeout(this.timers)
      },
      updateTrackPopup(dataTableBody) {
        this.checkLngLatInBounds()

        this.trackPopup.setLngLat(this.coordinates[this.currentIndex]).setHTML(this.setPopupInfo(dataTableBody))
        if (!this.trackPopup.isOpen()) {
          this.trackPopup.addTo(this.map)
        }
      },
      testLngLatInBounds(lngLat) {
        if (!Array.isArray(lngLat)) {
          return false
        }
        const bounds = this.map.getBounds()
        const North = bounds.getNorth()
        const East = bounds.getEast()
        const South = bounds.getSouth()
        const West = bounds.getWest()
        const lng = lngLat[0]
        const lat = lngLat[1]
        if (lng >= West && lng <= East && lat >= South && lat <= North) {
          return true
        } else {
          return false
        }
      },
      checkLngLatInBounds() {
        let nextIndex = this.currentIndex + 1
        if (nextIndex >= this.coordinates.length) {
          nextIndex = 0
        }
        const lngLat = this.coordinates[nextIndex]
        if (!this.testLngLatInBounds(lngLat)) {
          // 跳转地图中心
          this.map.flyTo({
            center: lngLat,
          })
        }
      },
      addTrackPopup(dataTableBody) {
        this.trackPopup = new maplibregl.Popup({
          closeOnClick: false,
          closeButton: false,
        })
          .setLngLat(this.coordinates[this.currentIndex])
          .setHTML(this.setPopupInfo(dataTableBody))
          .addTo(this.map)
      },
      setPopupInfo(dataTableBody) {
        const dataItem = dataTableBody[this.currentIndex]
        let title = ''
        let html = ''

        // 巡查历史
        if (this.traceOrigin === 'patrolPlayer') {
          title = this.$t('map.linePointInfo')
          const point = bfglob.glinePoints.get(dataItem.pointId)
          const device = bfglob.gdevices.get(dataItem.deviceId)
          const userName = bfglob.guserData.getUserNameByKey(dataItem.checkerId)
          const coordinates = this.coordinates[this.currentIndex]
          const coordinatesStr = `${coordinates[0].toFixed(6)}, ${coordinates[1].toFixed(6)}`

          html = `<div class='popup-body'>
            <p><span class="popup-item-label">${this.$t('dialog.index')}: </span><span class="popup-item-value">${this.currentIndex + 1} / ${this.coordinates.length}</span></p>
            <p><span class="popup-item-label">${this.$t('dialog.lngLat')}: </span><span class="popup-item-value">${coordinatesStr}</span></p>`

          if (point) {
            html += `<p><span class="popup-item-label">${this.$t('map.patrolPoint')}: </span><span class="popup-item-value">${point.pointId}-${point.pointName}</span></p>`
          }

          if (device && userName) {
            html += `<p><span class="popup-item-label">${this.$t('dialog.deviceName')}: </span><span class="popup-item-value">${device.selfId}</span><span style="margin-left: 20px;" class="popup-item-label">${this.$t('dialog.userName')}: </span><span class="popup-item-value">${userName}</span></p>`
          } else if (device) {
            html += `<p><span class="popup-item-label">${this.$t('dialog.deviceName')}: </span><span class="popup-item-value">${device.selfId}</span></p>`
          } else if (userName) {
            html += `<p><span class="popup-item-label">${this.$t('dialog.userName')}: </span><span class="popup-item-value">${userName}</span></p>`
          }

          html += `<p><span class="popup-item-label">${this.$t('map.patrolTime')}: </span><span class="popup-item-value">${dataItem.checkTime}</span></p>`
          html += `</div>`
        } else {
          // 定位轨迹历史
          title = this.$t('map.trackPointInfo')
          const coordinates = this.coordinates[this.currentIndex]
          const coordinatesStr = `${coordinates[0].toFixed(6)}, ${coordinates[1].toFixed(6)}`
          // const deviceTypeName = dataItem.deviceTypeName || this.$t('map.unknownDevice')

          html = `<div class='popup-body'>
            <p><span class="popup-item-label">${this.$t('dialog.index')}: </span><span class="popup-item-value">${this.currentIndex + 1} / ${this.coordinates.length}</span></p>
            <p><span class="popup-item-label">${this.$t('dialog.lngLat')}: </span><span class="popup-item-value">${coordinatesStr}</span></p>`

          if (dataItem.orgShortName) {
            html += `<p><span class="popup-item-label">${this.$t('map.organization')}: </span><span class="popup-item-value">${dataItem.orgShortName}</span></p>`
          }

          if (dataItem.userName && dataItem.deviceSelfId) {
            html += `<p><span class="popup-item-label">${this.$t('dialog.deviceName')}: </span><span class="popup-item-value">${dataItem.deviceSelfId}</span><span style="margin-left: 20px;" class="popup-item-label">${this.$t('dialog.userName')}: </span><span class="popup-item-value">${dataItem.userName}</span></p>`
          } else if (dataItem.deviceSelfId) {
            html += `<p><span class="popup-item-label">${this.$t('dialog.deviceName')}: </span><span class="popup-item-value">${dataItem.deviceSelfId}</span></p>`
          }

          html += `<p><span class="popup-item-label">${this.$t('map.gpsTime')}: </span><span class="popup-item-value">${dataItem.gpsTime}</span></p>`
          html += `<p><span class="popup-item-label">${this.$t('dataTable.speed')}: </span><span class="popup-item-value">${dataItem.speed} KM/H</span></p>`
          html += `</div>`
        }

        const wrapper = `<div class="popup-wrapper">
              <span class="popup_title" title="${title}">${title}</span>
              <span class="close_icon" onclick="window.closeTrackPopup && window.closeTrackPopup()" title="${this.$t('map.closePopup')}"></span>
              ${html}
            </div>`
        return wrapper
      },
      clearlCheckLayers() {
        if (this.map.getLayer(`${this.traceOrigin}-arrowhead`)) {
          this.map.removeLayer(`${this.traceOrigin}-arrowhead`)
        }
        if (this.map.getLayer(`${this.traceOrigin}-lines`)) {
          this.map.removeLayer(`${this.traceOrigin}-lines`)
        }
        if (this.map.getSource(`${this.traceOrigin}-lines`)) {
          this.map.removeSource(`${this.traceOrigin}-lines`)
        }
        if (this.map.getLayer(`${this.traceOrigin}-point`)) {
          this.map.removeLayer(`${this.traceOrigin}-point`)
        }
        if (this.map.getSource(`${this.traceOrigin}-point`)) {
          this.map.removeSource(`${this.traceOrigin}-point`)
        }
      },
      async addTrackLayers(dataTableBody) {
        if (this.coordinates.length === 0) {
          this.coordinates = dataTableBody.map(item => {
            const point = bfglob.glinePoints.get(item.pointId)
            if (point) {
              return [point.lon, point.lat]
            } else {
              return [item.lon, item.lat]
            }
          })
        }
        // 巡逻轨迹巡查点图层
        const point_features = this.coordinates.map(item => {
          return {
            type: 'Feature',
            properties: {},
            geometry: {
              type: 'Point',
              coordinates: item,
            },
          }
        })
        this.map.addSource(`${this.traceOrigin}-point`, {
          type: 'geojson',
          data: {
            type: 'FeatureCollection',
            features: point_features,
          },
        })
        this.map.addLayer({
          id: `${this.traceOrigin}-point`,
          type: 'circle',
          source: `${this.traceOrigin}-point`,
          paint: {
            'circle-radius': 4,
            'circle-color': '#2b96d6',
          },
        })
        // 巡逻轨迹线路图层
        this.map.addSource(`${this.traceOrigin}-lines`, {
          type: 'geojson',
          data: {
            type: 'Feature',
            geometry: {
              type: 'LineString',
              coordinates: this.coordinates,
            },
          },
        })
        this.map.addLayer({
          id: `${this.traceOrigin}-lines`,
          type: 'line',
          source: `${this.traceOrigin}-lines`,
          layout: {
            'line-join': 'round',
            'line-cap': 'round',
          },
          paint: {
            'line-color': '#2b96d6',
            'line-width': 2,
          },
        })
        // 添加轨迹方向arrowhead
        const addArrowImage = () => {
          this.map.addLayer({
            id: `${this.traceOrigin}-arrowhead`,
            type: 'symbol',
            source: `${this.traceOrigin}-lines`,
            layout: {
              'symbol-placement': 'line',
              'icon-image': 'bf-arrow',
              'icon-offset': [0, 0],
              'icon-rotate': 90,
              'icon-size': 0.6,
            },
          })
        }
        if (this.map.hasImage('bf-arrow')) {
          addArrowImage()
        } else {
          const res = await this.map.loadImage(arrowPng).catch(err => {
            return {
              err,
            }
          })
          if (res.err) {
            bfglob.console.error('addTrackLayers loadImage error', res.err)
            return
          }
          this.map.addImage('bf-arrow', res.data)
          addArrowImage()
        }
      },
    },
    mounted() {
      this.setMapStyle()
      registerCustomProtocol()
      this.initPersonalMapSettings()
      this.initBfMap()

      // 添加全局关闭弹窗方法
      window.closeTrackPopup = () => {
        if (this.trackPopup) {
          this.trackPopup.remove()
          // 不销毁 trackPopup 对象，只是从地图上移除
        }
      }

      // 地图样式加载完成后在加载轨迹样式
      this.map.on('style.load', () => {
        this.initCoordinates(this.traceData)
        this.showTrackCtrol = true
        this.sliderMax = this.coordinates.length
        // 绘制线路轨迹图层
        this.addTrackLayers(this.traceData)

        // 将轨迹的第一个坐标作为地图中心
        this.map.flyTo({
          center: this.coordinates[0],
        })
        // 添加巡逻轨迹当前巡查点巡查状况信息popup
        this.addTrackPopup(this.traceData)
      })
    },
    beforeUnmount() {
      // 清理指南针拖动事件监听器
      document.removeEventListener('mousemove', this.onCompassDrag)
      document.removeEventListener('mouseup', this.endCompassDrag)

      // 清理全局方法
      if (window.closeTrackPopup) {
        delete window.closeTrackPopup
      }
    },
    computed: {
      lang() {
        return this.$i18n.locale
      },
    },
    watch: {
      lang() {
        this.setMapStyle()
        this.reloadCauseLangChanged()
        this.resetMapboxCtrlTitle()
      },
    },
    activated() {
      if (this.map) {
        this.map.resize()
      }
    },
  }
</script>

<style lang="scss">
  @keyframes things-card-emergency-alarm-animate {
    50% {
      color: #21ba45;
    }

    100% {
      color: #c10015;
    }
  }

  @keyframes user-card-emergency-alarm-animate {
    50% {
      background: radial-gradient(#21ba45, transparent);
    }

    100% {
      background: radial-gradient(#c10015, transparent);
    }
  }

  .map-wrapper {
    position: relative;
  }

  .markerContainer {
    $noCommandWarnColor: #e65100;
    $otherAlarmColor: #c10015;
    $normalReportColor: #21ba45;
    $offlineColor: gray(100);

    cursor: pointer;
    text-align: center;
    box-sizing: inherit;

    .mapDisplayName {
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%) translateY(100%);
      word-break: keep-all;
      color: #0012ff;
      white-space: nowrap;
      font-size: 13px;
      text-shadow:
        0 0 2px rgba(255, 255, 255, 1),
        -1px 0 2px rgba(255, 255, 255, 1),
        0 -1px 2px rgba(255, 255, 255, 1),
        1px 1px 2px rgba(255, 255, 255, 1);

      .hideMarker {
        top: 50%;
        transform: translateX(-50%) translateY(-50%);
      }
    }
  }

  .maplibregl-canvas {
    left: 0;
    width: 100% !important;
    height: 100% !important;
  }

  .maplibregl-missing-css {
    display: none !important;
  }

  .maplibregl-popup-content {
    padding: 10px;
    text-align: center;
    word-break: break-word;
    box-sizing: border-box;
  }

  .maplibregl-popup-content p {
    margin: 0;
    padding: 0 0 6px 0;
  }

  .maplibregl-popup-content .popup-body {
    text-align: left;
    width: 100%;
    max-height: 220px;
    overflow: auto;
  }

  .maplibregl-popup {
    z-index: 1000;
    max-width: 480px !important;
  }

  .select_more_linPoint_list {
    cursor: pointer;
    text-decoration: underline;
    color: #ff4949;
  }

  .select_more_linPoint_list:hover {
    color: #f00;
  }

  .maplibregl-ctrl-mapLevel {
    background: #fff;
    border-radius: 4px;
    padding: 2px 4px;
    text-align: center;
    position: absolute;
    bottom: 2px;
    left: 2px;
    z-index: 2000;
  }

  .mapLevel,
  .distance {
    color: #f00;
    font-size: 1.3em;
    font-weight: bold;
  }

  .distance-container {
    position: absolute;
    top: 10px;
    left: 50%;
    z-index: 1;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 1);
    color: #fff;
    font-size: 16px;
    line-height: 18px;
    display: block;
    margin: 0;
    padding: 5px 10px;
    border-radius: 3px;
  }

  .map_coordinates {
    position: absolute;
    top: 50%;
    left: 50%;
    margin-left: -20.5px;
    margin-top: -20.5px;
    z-index: 9999;
  }

  .map_coordinates:before {
    content: '';
    display: table;
    border-bottom: 1px solid #003eff;
    position: absolute;
    top: 0;
    left: 0;
    width: 41px;
    margin-top: 20.5px;
  }

  .map_coordinates:after {
    content: '';
    display: table;
    border-right: 1px solid #003eff;
    position: absolute;
    top: 0;
    left: 0;
    height: 41px;
    margin-left: 20.5px;
  }

  .floor-select-container {
    position: absolute;
    top: 50%;
    left: 5px;
    z-index: 100;
    display: flex;
    flex-direction: column;
    transform: translate(0, -50%);
    background-color: #fff;
  }

  .floor-select-container button {
    border: none !important;
  }

  .floor-switch-top,
  .floor-switch-bottom,
  .floors-container-wrap,
  .btn-select-floor {
    flex: auto;
  }

  .floor-switch-top {
    border-radius: 3px 3px 0 0;
  }

  .floor-switch-bottom {
    border-radius: 0 0 3px 3px;
  }

  .floors-container-wrap {
    max-height: 208px;
    min-height: 26px;
    overflow: hidden;
  }

  .floors-container {
    display: flex;
    flex-direction: column-reverse;
  }

  .floor-switch-top,
  .floor-switch-bottom,
  .floor-select-container button.btn-select-floor {
    padding: 7px;
  }

  .floor-select-container button.btn-select-floor {
    margin-left: 0;
    border-radius: unset;
  }

  .btn-select-floor.floor-selected {
    background-color: #409eff !important;
    border-color: #409eff !important;
    color: #fff !important;
  }

  .maplibregl-ctrl-top-left,
  .maplibregl-ctrl-top-right,
  .maplibregl-ctrl-bottom-left,
  .maplibregl-ctrl-bottom-right {
    z-index: 2000;
  }

  .marker-wrapper {
    display: flex;
    justify-content: center;
    align-content: center;
  }

  .marker-wrapper .marker-image {
    width: 100%;
    border-radius: 50%;
    vertical-align: top;
  }

  .marker-wrapper ~ .mapDisplayName {
    top: 16px;
  }

  .marker-wrapper .marker-badge {
    flex: none;
    background-color: #409eff;
    border-radius: 10px;
    color: #fff;
    line-height: 1.2;
    padding: 0 2px;
    text-align: center;
    white-space: nowrap;
    border: 1px solid #fff;
    /*margin-left: 2px;*/
  }

  .popup-active-device-list {
    max-height: 100%;
    overflow: auto;
  }
</style>
