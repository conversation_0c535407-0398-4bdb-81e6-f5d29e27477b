<template>
  <div class="writer-frequency-wrap">
    <selectDevice v-model="selectedDeviceDmrId" :disabled="isReading || isWriting" />
    <el-tabs v-model="writerFrequencyTabName" class="writer-frequency-tabs" tab-position="left">
      <!--设备信息-->
      <el-tab-pane lazy :label="$t('dialog.deviceInfo')" name="deviceWriteInfo" class="deviceInfo-box settings-box">
        <deviceInfo ref="deviceWriteInfo" v-model="deviceWriteInfo" :model="deviceModel" />
      </el-tab-pane>
      <!--常规设置-->
      <el-tab-pane lazy :label="$t('dialog.generalSetting')" name="generalSettings" class="general-settings-box settings-box">
        <el-form ref="generalSettings" class="general-settings-form" :model="generalSettings" label-position="top" :rules="generalSettingsRules">
          <el-row :gutter="20" class="no-margin-x">
            <el-col :xs="24" :sm="12">
              <el-form-item :label="$t('dialog.terminalName')" prop="deviceName">
                <el-input v-model="generalSettings.deviceName" :maxlength="16" disabled />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item label="DMRID">
                <el-input :value="dmrIdLabel" disabled />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item :label="$t('dialog.terminalLevel')" prop="clientType.clientLevel">
                <el-select
                  v-model="generalSettings.clientType.clientLevel"
                  :placeholder="$t('dialog.select')"
                  filterable
                  disabled
                  :no-match-text="$t('dialog.noMatchText')"
                >
                  <el-option v-for="(item, i) in clientLevelOpts" :key="i" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item :label="$t('dialog.registCode')" prop="regCode">
                <el-input v-model="generalSettings.regCode" :maxlength="16" disabled />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item :label="$t('dialog.repeaterId')" prop="repeaterId">
                <el-input v-model.number="generalSettings.repeaterId" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item :label="$t('dialog.programmingPwd')">
                <el-input v-model="passwordInfo.md5Key" type="password" :maxlength="8" @input="fixMd5KeyValue" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item :label="$t('dialog.powerOnPwd')" prop="powerOnPassword">
                <el-input v-model="generalSettings.powerOnPassword" type="password" :maxlength="6" :minlength="6" @input="fixPowerOnValue" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item :label="$t('dialog.voiceCryptoType')" prop="soundEncryptType">
                <el-select v-model="generalSettings.soundEncryptType" :placeholder="$t('dialog.select')" filterable :no-match-text="$t('dialog.noMatchText')">
                  <el-option v-for="(item, i) in soundEncryptTypeList" :key="i" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item :label="$t('dialog.voiceCryptoKey')" prop="soundEncryptValue">
                <el-input
                  v-model="generalSettings.soundEncryptValue"
                  type="password"
                  :maxlength="10"
                  :disabled="generalSettings.soundEncryptType === 0"
                  @input="fixSoundEncryptValue"
                />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item :label="$t('dialog.voiceLevel')" prop="soundCtrlLevel">
                <bf-input-number
                  v-model="generalSettings.soundCtrlLevel"
                  step-strictly
                  :min="0"
                  :max="8"
                  :step="1"
                  :formatter="
                    v => {
                      return v === 0 ? $t('writeFreq.off') : v
                    }
                  "
                />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item :label="$t('dialog.voiceDelay')" prop="soundCtrlDelay">
                <el-input-number v-model="generalSettings.soundCtrlDelay" step-strictly :min="500" :max="10000" :step="500" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item :label="$t('dialog.sendPreambleDuration')" prop="sendLeadCodeTime">
                <el-input-number v-model="generalSettings.sendLeadCodeTime" step-strictly :min="0" :max="8640" :step="240" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item :label="$t('dialog.offNetworkGroupCallHangTime')" prop="offlineGroupCallHungTime">
                <el-input-number v-model="generalSettings.offlineGroupCallHungTime" step-strictly :min="0" :max="7000" :step="500" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item :label="$t('dialog.offNetworkSingleCallHangTime')" prop="offlineSingleCallHungTime">
                <el-input-number v-model="generalSettings.offlineSingleCallHungTime" step-strictly :min="0" :max="7000" :step="500" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item>
                <el-checkbox v-model="generalSettings.locations.savePowerMode">
                  <span v-text="$t('dialog.powerSavingMode')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="disabledAllLED">
                <el-checkbox v-model="generalSettings.soundAndDisplayTip.disabledAllLED">
                  <span v-text="$t('dialog.disabledAllLed')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="soundAndDisplayTip.freqDisplay">
                <el-checkbox v-model="generalSettings.soundAndDisplayTip.freqDisplay">
                  <span v-text="$t('dialog.displayFrequency')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="soundAndDisplayTip.rejectUnfamiliarCall">
                <el-checkbox v-model="generalSettings.soundAndDisplayTip.rejectUnfamiliarCall">
                  <span v-text="$t('dialog.rejectingStrangeCalls')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="soundAndDisplayTip.directMode">
                <el-checkbox v-model="generalSettings.soundAndDisplayTip.directMode">
                  <span v-text="$t('dialog.passThroughMode')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="soundAndDisplayTip.menuOff">
                <el-checkbox v-model="generalSettings.soundAndDisplayTip.menuOff">
                  <span v-text="$t('dialog.closeMenuButton')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20" class="no-margin-x">
            <el-divider>
              <el-icon>
                <CaretBottom />
              </el-icon>
              <span v-text="$t('writeFreq.locationSystem')" />
            </el-divider>
            <el-col :xs="24" :sm="12">
              <el-form-item>
                <el-checkbox v-model="generalSettings.locations.closePosition">
                  <span v-text="$t('writeFreq.closePosition')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item>
                <el-checkbox v-model="generalSettings.locations.gps">
                  <span>GPS</span>
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item>
                <el-checkbox v-model="generalSettings.locations.beidou">
                  <span v-text="$t('writeFreq.beidou')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20" class="no-margin-x">
            <el-divider>
              <el-icon>
                <CaretBottom />
              </el-icon>
              <span v-text="$t('writeFreq.voicePrompt')" />
            </el-divider>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="soundAndDisplayTip.allSlient">
                <el-checkbox v-model="generalSettings.soundAndDisplayTip.allSlient">
                  <span v-text="$t('dialog.muteAll')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="soundAndDisplayTip.voiceNotice">
                <el-checkbox v-model="generalSettings.soundAndDisplayTip.voiceNotice" :disabled="generalSettings.soundAndDisplayTip.allSlient">
                  <span v-text="$t('dialog.voiceIndication')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="soundAndDisplayTip.channelFreeNotice">
                <el-checkbox v-model="generalSettings.soundAndDisplayTip.channelFreeNotice" :disabled="generalSettings.soundAndDisplayTip.allSlient">
                  <span v-text="$t('dialog.channelIdleIndication')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="soundAndDisplayTip.allowCallInstruction">
                <el-checkbox v-model="generalSettings.soundAndDisplayTip.allowCallInstruction" :disabled="generalSettings.soundAndDisplayTip.allSlient">
                  <span v-text="$t('dialog.callPermissionIndication')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item :label="$t('dialog.receiveLowPowerPromptInterval')" prop="powerInfoAlert">
                <el-input-number v-model="generalSettings.powerInfoAlert" step-strictly :min="0" :max="635" :step="5" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-tab-pane>
      <!--菜单设置-->
      <el-tab-pane lazy :label="$t('dialog.menuSettings')" name="menuSettings" class="menu-settings-box settings-box">
        <el-form ref="menuSettings" class="menu-settings-form" :model="menuSettings" label-position="top">
          <el-row :gutter="20" class="no-margin-x">
            <el-col :xs="24">
              <el-form-item :label="$t('dialog.menuHangTime')" :label-width="menuHangTimeLabelWidth" prop="hangTime">
                <el-input-number v-model="menuSettings.hangTime" step-strictly :min="0" :max="30" :step="1" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="settings.message">
                <el-checkbox v-model="menuSettings.settings.message">
                  <span v-text="$t('dialog.sms')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="settings.editAddressBook">
                <el-checkbox v-model="menuSettings.settings.editAddressBook">
                  <span v-text="$t('dialog.editContacts')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="settings.manualDialing">
                <el-checkbox v-model="menuSettings.settings.manualDialing">
                  <span v-text="$t('dialog.manualDialing')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="settings.unrecorded">
                <el-checkbox v-model="menuSettings.settings.unrecorded">
                  <span v-text="$t('dialog.missedCall')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="settings.record">
                <el-checkbox v-model="menuSettings.settings.record">
                  <span v-text="$t('dialog.answeredCall')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="settings.outgoingCall">
                <el-checkbox v-model="menuSettings.settings.outgoingCall">
                  <span v-text="$t('dialog.outgoingCall')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="settings.offline">
                <el-checkbox v-model="menuSettings.settings.offline">
                  <span v-text="$t('dialog.offNetwork')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="settings.toneCue">
                <el-checkbox v-model="menuSettings.settings.toneCue">
                  <span v-text="$t('dialog.toneOrTip')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="settings.emissivePower">
                <el-checkbox v-model="menuSettings.settings.emissivePower">
                  <span v-text="$t('dialog.txPower')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="settings.locationEnable">
                <el-checkbox v-model="menuSettings.settings.locationEnable">
                  <span v-text="$t('writeFreq.locationEnable')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="settings.backLight">
                <el-checkbox v-model="menuSettings.settings.backLight">
                  <span v-text="$t('dialog.backlight')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="settings.poweronDesktop">
                <el-checkbox v-model="menuSettings.settings.poweronDesktop">
                  <span v-text="$t('dialog.bootInterface')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="settings.keyboardLock">
                <el-checkbox v-model="menuSettings.settings.keyboardLock">
                  <span v-text="$t('dialog.keyboardLock')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="settings.ledIndicator">
                <el-checkbox v-model="menuSettings.settings.ledIndicator">
                  <span v-text="$t('dialog.ledIndicator')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="settings.soundCtrl">
                <el-checkbox v-model="menuSettings.settings.soundCtrl">
                  <span v-text="$t('dialog.voiceControl')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="settings.poweronPass">
                <el-checkbox v-model="menuSettings.settings.poweronPass">
                  <span v-text="$t('dialog.powerOnPwd')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-tab-pane>
      <!--巡逻系统-->
      <el-tab-pane lazy :label="$t('dialog.patrolSystem')" name="patrolSystem" class="h-full patrol-system-box settings-box">
        <el-tabs v-model="patrolSystemTabName" class="patrol-system-tabs settings-tabs-box" type="border-card">
          <el-tab-pane :label="$t('dialog.configure')" name="configure" class="h-full">
            <patrolConfig ref="ppp" v-model="patrolConfig" timeZone />
          </el-tab-pane>
          <el-tab-pane :label="$t('dialog.emergency')" name="emergency" class="h-full">
            <emergencyAlarmConfig v-model="emergencyAlarm" :addressBooks="selectedAddressBook" />
          </el-tab-pane>
          <el-tab-pane :label="$t('dialog.trailCtrl')" name="trailCtrl" class="h-full">
            <el-form ref="patrolSystemTrailCtrl" class="patrol-system-trailCtrl-form" :model="TrackMonitor" label-width="95px" label-position="top">
              <el-row :gutter="20" class="no-margin-x">
                <el-col :xs="24">
                  <el-form-item :label="$t('dialog.onOff')" prop="trackEnable">
                    <el-select v-model="TrackMonitor.trackEnable" :placeholder="$t('dialog.select')" filterable :no-match-text="$t('dialog.noMatchText')">
                      <el-option v-for="(item, i) in onOffList" :key="i" :label="item.label" :value="item.value" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :xs="24">
                  <el-form-item :label="$t('dialog.trailSpacing')" prop="rollTime">
                    <el-input-number v-model="TrackMonitor.rollTime" :disabled="TrackMonitor.trackEnable === 0" step-strictly :min="0" :max="9995" :step="5" />
                  </el-form-item>
                </el-col>
                <el-col :xs="24">
                  <el-form-item :label="$t('dialog.shortestDistance')" prop="rollDistant">
                    <el-input-number
                      v-model="TrackMonitor.rollDistant"
                      :disabled="TrackMonitor.trackEnable === 0"
                      step-strictly
                      :min="0"
                      :max="495"
                      :step="5"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-tab-pane>
          <el-tab-pane :label="$t('dialog.activeRFID')" name="activeRFID" class="h-full">
            <el-form ref="patrolSystemActiveRfid" class="patrol-system-activeRfid-form" :model="ActiveRFID" label-width="95px" label-position="top">
              <el-row :gutter="20" class="no-margin-x">
                <el-col :xs="24" :sm="12">
                  <el-form-item :label="$t('dialog.onOff')" prop="settings.enable">
                    <el-select v-model="ActiveRFID.settings.enable" :placeholder="$t('dialog.select')" filterable :no-match-text="$t('dialog.noMatchText')">
                      <el-option v-for="(item, i) in onOffList" :key="i" :label="item.label" :value="item.value" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12">
                  <el-form-item :label="$t('dialog.mode')" prop="settings.mode">
                    <el-select
                      v-model="ActiveRFID.settings.mode"
                      :placeholder="$t('dialog.select')"
                      filterable
                      :disabled="ActiveRFID.settings.enable === 0"
                      :no-match-text="$t('dialog.noMatchText')"
                    >
                      <el-option v-for="(item, i) in rfidModeList" :key="i" :label="item.label" :value="item.value" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12">
                  <el-form-item :label="$t('dialog.power')" prop="settings.power">
                    <el-select
                      v-model="ActiveRFID.settings.power"
                      :placeholder="$t('dialog.select')"
                      filterable
                      :disabled="ActiveRFID.settings.enable === 0"
                      :no-match-text="$t('dialog.noMatchText')"
                    >
                      <el-option v-for="(item, i) in rfidPowerList" :key="i" :label="item.label" :value="item.value" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12">
                  <el-form-item :label="$t('dialog.answer')" prop="settings.replyMechanism">
                    <el-select
                      v-model="ActiveRFID.settings.replyMechanism"
                      :placeholder="$t('dialog.select')"
                      filterable
                      :disabled="ActiveRFID.settings.enable === 0"
                      :no-match-text="$t('dialog.noMatchText')"
                    >
                      <el-option v-for="(item, i) in rfidAnswerList" :key="i" :label="item.label" :value="item.value" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12">
                  <el-form-item :label="$t('dialog.channel')" prop="workChannel">
                    <el-input-number
                      v-model="ActiveRFID.workChannel"
                      :disabled="ActiveRFID.settings.enable === 0"
                      step-strictly
                      :min="2"
                      :max="125"
                      :step="1"
                    />
                  </el-form-item>
                </el-col>
                <el-col v-for="(dataChannel, index) in ActiveRFID.address" :key="index" :xs="24" :sm="12">
                  <el-form-item :label="$t('dialog.dataChannel', { num: index })">
                    <bf-input-number
                      v-model="ActiveRFID.address[index]"
                      :disabled="ActiveRFID.settings.enable === 0"
                      step-strictly
                      :min="1"
                      :max="0xffffffffff"
                      :step="1"
                    />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-tab-pane>
        </el-tabs>
      </el-tab-pane>
      <!--通讯录-->
      <el-tab-pane :label="$t('dialog.addressBook')" name="addressBook" class="h-full settings-box address-book">
        <addressBook
          :ref="addrBookTreeId"
          class="settings-box"
          :treeId="addrBookTreeId"
          :redrawTree="writerFrequencyTabName === 'addressBook'"
          :callTypes="addressBookCallTypes"
          @select="selectAddressBooks"
        />
      </el-tab-pane>
      <!--接收组列表-->
      <el-tab-pane :label="$t('dialog.rxGroup')" name="rxGroup" class="h-full settings-box">
        <receiveGroup
          :ref="refReceiveGroup"
          v-model="rxGroupList"
          :channels="selectedChannels"
          :addressTreeId="addrBookTreeId"
          :getDefaultAddress="getDefaultDigitalAddressId"
          :getAddressName="getAddressNameByDmrId"
          :getOriginAddress="getOriginAddressBook"
        />
      </el-tab-pane>
      <!--信道设置-->
      <el-tab-pane :label="$t('dialog.channelSetting')" name="channelSettings" class="h-full channel-settings-box settings-box">
        <el-tabs v-model="channelSettingsTabName" class="channel-list-tabs settings-tabs-box" type="border-card">
          <el-tab-pane :label="$t('dialog.channelList')" name="channel" class="h-full channel-tab-pane">
            <el-table
              ref="channelTable"
              :data="channelDatas"
              border
              highlight-current-row
              height="calc(100% - 1px)"
              style="width: 100%"
              @row-click="channelListRowClick"
              @row-dblclick="channelListRowDblclick"
            >
              <el-table-column v-for="(column, i) in channelListColumn" :key="i" :prop="column.prop" :label="column.label" :width="column.width">
                <template #default="scope">
                  <span v-if="column.prop === 'channelId'">
                    <el-icon
                      class="channel-enable-flag"
                      :style="{
                        visibility: scope.row['enable'] ? 'visible' : 'hidden',
                      }"
                    >
                      <SuccessFilled />
                    </el-icon>
                    <span v-text="scope.row[column.prop] + 1" />
                  </span>

                  <span v-else-if="column.prop.includes('recvFreq') || column.prop.includes('sendFreq')" v-text="frequencyHz2Mhz(scope.row[column.prop])" />

                  <span v-else v-text="scope.row[column.prop]" />
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane :label="$t('dialog.channelSetting')" name="chSettings" class="h-full settings-box">
            <el-form
              ref="channelSettings"
              class="channel-settings-form"
              :model="channelSettings"
              label-width="95px"
              label-position="top"
              :rules="channelSettingsRules"
            >
              <el-row :gutter="20" class="no-margin-x">
                <el-col :xs="24" :sm="12">
                  <el-form-item :label="$t('dialog.chId')">
                    <el-select v-model="channelDataId">
                      <el-option v-for="(item, i) in channelIdList" :key="i" :label="item.label" :value="item.value" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12">
                  <el-form-item :label="$t('dialog.chName')" prop="channelName">
                    <el-input v-model="channelSettings.channelName" :maxlength="16" />
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12">
                  <el-form-item :label="$t('dialog.receiveGroupList')" prop="digitalChannelPara.recvGroupId">
                    <el-select
                      v-model="channelSettings.digitalChannelPara.recvGroupId"
                      disabled
                      :placeholder="$t('dialog.select')"
                      :no-match-text="$t('dialog.noMatchText')"
                    >
                      <el-option v-for="(item, i) in recvGroupIdList" :key="i" :label="item.label" :value="item.value" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12">
                  <el-form-item :label="$t('dialog.sendGroup')" prop="digitalChannelPara.defaultDigitalAddress">
                    <el-select
                      v-model="channelSettings.digitalChannelPara.defaultDigitalAddress"
                      disabled
                      :placeholder="$t('dialog.select')"
                      :no-match-text="$t('dialog.noMatchText')"
                    >
                      <el-option v-for="(item, i) in defaultDigitalAddressList" :key="i" :label="item.label" :value="item.value" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <template v-for="num in 3" :key="num">
                  <el-col :xs="24" :sm="12">
                    <el-form-item :label="$t('dialog.emissionNumber', { num })" :prop="'sendFreq' + num">
                      <frequencyMhz v-model="channelSettings['sendFreq' + num]" :maxlength="9" />
                    </el-form-item>
                  </el-col>
                  <el-col :xs="24" :sm="12">
                    <el-form-item :label="$t('dialog.receiveNumber', { num })" :prop="'recvFreq' + num">
                      <frequencyMhz v-model="channelSettings['recvFreq' + num]" :maxlength="9" />
                    </el-form-item>
                  </el-col>
                </template>
                <el-col :xs="24" :sm="12">
                  <el-form-item :label="$t('dialog.txPower')" prop="channelSetting.sendPower">
                    <el-select v-model="channelSettings.channelSetting.sendPower" :placeholder="$t('dialog.select')" :no-match-text="$t('dialog.noMatchText')">
                      <el-option v-for="(item, i) in txPowerTypes" :key="i" :label="item.label" :value="item.value" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12">
                  <el-form-item :label="$t('dialog.sendTimeLimiter')" prop="sendTimeLimter">
                    <el-input-number v-model="channelSettings.sendTimeLimter" step-strictly :min="15" :max="495" :step="15" />
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12">
                  <el-form-item :label="$t('dialog.totPwdUpdateDelay')" prop="keyUpdateDelay">
                    <el-input-number v-model="channelSettings.keyUpdateDelay" step-strictly :min="0" :max="255" :step="5" />
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12">
                  <el-form-item :label="$t('dialog.colorCodes')" prop="digitalChannelPara.colorCode">
                    <el-input-number v-model="channelSettings.digitalChannelPara.colorCode" step-strictly :min="0" :max="15" :step="1" />
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12">
                  <el-form-item :label="$t('dialog.permissionConditions')" prop="digitalChannelPara.setting.allowCondition">
                    <el-select
                      v-model="channelSettings.digitalChannelPara.setting.allowCondition"
                      :placeholder="$t('dialog.select')"
                      :no-match-text="$t('dialog.noMatchText')"
                    >
                      <el-option v-for="(item, i) in permissionConditionsList" :key="i" :label="item.label" :value="item.value" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12">
                  <el-form-item :label="$t('dialog.slotMode')" prop="digitalChannelPara.switches.slotMode">
                    <el-select
                      v-model="channelSettings.digitalChannelPara.switches.slotMode"
                      :placeholder="$t('dialog.select')"
                      :disabled="channelSettings.digitalChannelPara.switches.registerToSystem"
                      :no-match-text="$t('dialog.noMatchText')"
                    >
                      <el-option v-for="(item, i) in slotModeList" :key="i" :label="item.label" :value="item.value" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12">
                  <el-form-item :label="$t('dialog.virtualTimeSlot')" prop="digitalChannelPara.switches.virtualTimeSlot">
                    <el-select
                      v-model="channelSettings.digitalChannelPara.switches.virtualTimeSlot"
                      :placeholder="$t('dialog.select')"
                      filterable
                      :disabled="channelSettings.digitalChannelPara.switches.slotMode !== 2"
                      :no-match-text="$t('dialog.noMatchText')"
                    >
                      <el-option v-for="(item, i) in virtualTimeSlotList" :key="i" :label="item.label" :value="item.value" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20" class="no-margin-x">
                <el-col :xs="24" :sm="12">
                  <el-form-item label="" prop="digitalChannelPara.switches.singleConfirm">
                    <el-checkbox v-model="channelSettings.digitalChannelPara.switches.singleConfirm">
                      <span v-text="$t('dialog.singleCallConfirm')" />
                    </el-checkbox>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12">
                  <el-form-item label="" prop="digitalChannelPara.setting.allowOffline">
                    <el-checkbox v-model="channelSettings.digitalChannelPara.setting.allowOffline">
                      <span v-text="$t('dialog.allowOffNetwork')" />
                    </el-checkbox>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12">
                  <el-form-item label="" prop="channelSetting.recvOnly">
                    <el-checkbox v-model="channelSettings.channelSetting.recvOnly">
                      <span v-text="$t('dialog.receiveOnly')" />
                    </el-checkbox>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12">
                  <el-form-item label="" prop="digitalChannelPara.switches.registerToSystem">
                    <el-checkbox v-model="channelSettings.digitalChannelPara.switches.registerToSystem">
                      <span v-text="$t('dialog.registerToSystem')" />
                    </el-checkbox>
                  </el-form-item>
                </el-col>
                <el-col :xs="24" :sm="12">
                  <el-form-item label="" prop="digitalChannelPara.switches.localCall">
                    <el-checkbox
                      v-model="channelSettings.digitalChannelPara.switches.localCall"
                      :disabled="!channelSettings.digitalChannelPara.switches.registerToSystem"
                    >
                      <span v-text="$t('dialog.localCall')" />
                    </el-checkbox>
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-tab-pane>
        </el-tabs>
      </el-tab-pane>
      <!--短信内容-->
      <el-tab-pane lazy :label="$t('dialog.smsContent')" name="smsContent" class="h-full settings-box">
        <shortMessage :ref="refSms" v-model="smsContent" />
      </el-tab-pane>
      <!--电话本-->
      <el-tab-pane :label="$t('dialog.phoneBook')" name="phoneBook" class="h-full settings-box phoneBook-box-container">
        <phoneBook
          :ref="phoneBookTreeId"
          class="settings-box phoneBook-box"
          :treeId="phoneBookTreeId"
          :redrawTree="writerFrequencyTabName === 'phoneBook'"
          @select="selectPhoneBooks"
        />
      </el-tab-pane>
    </el-tabs>
    <write-freq-footer
      :is-reading="isReading"
      :is-writing="isWriting"
      :disable-read="disReadBtn"
      :disable-write="disWriteBtn"
      @new-config="newConfig"
      @read-config="readDataConfig"
      @write-config="writeInFrequency"
      @export-config="exportConfig"
      @import-config="customImportConfig"
    />
  </div>
</template>

<script>
  import AsyncValidator from 'async-validator'
  import { cloneDeep, merge } from 'lodash'
  import bftree from '@/utils/bftree'
  import bfutil from '@/utils/bfutil'
  import bfNotify from '@/utils/notify'
  import bfStorage from '@/utils/storage'
  import validateRules from '@/utils/validateRules'
  import { fixPowerOnPassword, fixProgramPassword, fixVoiceEncryptKey } from '@/writingFrequency/interphone/common'
  import commonToolMixin from '@/writingFrequency/interphone/commonToolMixin'
  import { AddressBookCallTypes, getClassInstance, Model } from '@/writingFrequency/interphone/TD081000'
  import wfTool from '@/writingFrequency/interphone/tool'
  import deviceInfo from '@/platform/dataManage/deviceManage/common/deviceInfo'
  import WriteFreqFooter from '@/platform/dataManage/deviceManage/common/writeFreqFooter.vue'
  import selectDevice from '@/platform/dataManage/deviceManage/common/selectDevice.vue'
  import { defineAsyncComponent } from 'vue'

  const Password = {
    md5Key: '',
  }
  const DefaultFrequency = {
    value: 400000000,
    label: '400',
  }
  const GeneralSettings = {
    deviceName: '',
    dmrId: '',
    ids: 0,
    clientType: {
      clientLevel: 1,
    },
    regCode: '',
    repeaterId: 16777215,
    powerOnPassword: '',
    soundEncryptType: 0,
    soundEncryptValue: '',
    soundCtrlLevel: 0,
    soundCtrlDelay: 500,
    sendLeadCodeTime: 960,
    offlineGroupCallHungTime: 3000,
    offlineSingleCallHungTime: 4000,
    powerInfoAlert: 120,
    locations: {
      savePowerMode: true,
      closePosition: false,
      gps: true,
      beidou: true,
      glonas: false,
      galileo: false,
    },
    soundAndDisplayTip: {
      disabledAllLED: false,
      freqDisplay: true,
      rejectUnfamiliarCall: false,
      directMode: false,
      menuOff: false,
      allSlient: false,
      voiceNotice: false,
      channelFreeNotice: false,
      allowCallInstruction: false,
    },
  }
  const MenuSettings = {
    hangTime: 10,
    settings: {
      message: true,
      editAddressBook: true,
      manualDialing: true,
      unrecorded: true,
      record: true,
      outgoingCall: true,
      offline: true,
      toneCue: true,
      emissivePower: true,
      locationEnable: true,
      backLight: true,
      poweronDesktop: true,
      keyboardLock: true,
      ledIndicator: false,
      soundCtrl: true,
      poweronPass: false,
      quieting: false,
    },
  }
  const TrackMonitor = {
    rollTime: 300,
    rollDistant: 50,
    trackEnable: 1,
  }
  const ActiveRFID = {
    settings: {
      enable: 0,
      mode: 0,
      power: 0,
      replyMechanism: 0,
    },
    workChannel: 2,
    address: [0xe7d3f03577, 0xc2c2c2c2c2, 0xc2c2c2c2c3, 0xc2c2c2c2c4, 0xc2c2c2c2c5, 0xc2c2c2c2c5],
  }
  // enable 属性为38个信道中标记哪个信道是已配置可用的
  const ChannelSettings = {
    enable: false,
    channelId: 0,
    channelName: 'CH 1',
    // 信号类型，固定为0
    signalType: 0,
    recvFreq1: 400000000,
    recvFreq2: 400000000,
    recvFreq3: 400000000,
    sendFreq1: 400000000,
    sendFreq2: 400000000,
    sendFreq3: 400000000,
    digitalChannelPara: {
      colorCode: 1,
      setting: {
        allowOffline: false,
        allowCondition: 1,
      },
      recvGroupId: 0,
      defaultDigitalAddress: 0,
      switches: {
        singleConfirm: false,
        registerToSystem: true,
        localCall: false,
        slotMode: 2,
        virtualTimeSlot: 0,
      },
    },
    channelSetting: {
      sendPower: 0,
      recvOnly: false,
    },
    sendTimeLimter: 60,
    keyUpdateDelay: 0,
  }

  export default {
    name: 'TD800SDC',
    mixins: [commonToolMixin, wfTool],
    data() {
      return {
        selectedDeviceDmrId: '',
        gdeviceList: bfglob.gdevices.getList(),

        writerFrequencyTabName: 'deviceWriteInfo',
        patrolSystemTabName: 'configure',
        channelSettingsTabName: 'channel',
        channelDatasCache: [],
        channelDatas: [],
        defaultFrequency: cloneDeep(DefaultFrequency),
        channelSettings: cloneDeep(ChannelSettings),
        // 设备信息
        deviceWriteInfo: {},
        deviceModel: Model,
        // 缓存身份信息
        identityInfo: {},
        // 编程密码
        passwordInfo: cloneDeep(Password),
        generalSettings: cloneDeep(GeneralSettings),
        menuSettings: cloneDeep(MenuSettings),
        // 配置
        patrolConfig: {},
        // 紧急报警,
        emergencyAlarm: {},

        TrackMonitor: cloneDeep(TrackMonitor),
        // 有源RFID
        // 自动定位监控
        ActiveRFID: cloneDeep(ActiveRFID),

        originAddressBook: [],
        selectedAddressBook: [],
        addrBookTreeId: 'TD810AddressBookTree',
        channelDataId: -1,

        // 接收组配置
        rxGroupList: [],
        refReceiveGroup: 'receiveGroup',

        // 电话本
        phoneBookTreeId: 'TD810PhoneBook',
        phoneBook: [],

        // 多个数据的结构写频时，几个写一次
        wfCountLimit: 3,
        availableChannels: {
          areaChannelIndexTable: [],
        },
        smsContent: [],
        refSms: 'shortMessage',
        // 信道总数
        totalChannels: 38,
        readCount: 0,
        isReading: false,
        isWriting: false,
        readWriteFlag: '',
      }
    },
    methods: {
      fixMd5KeyValue(value) {
        this.passwordInfo.md5Key = fixProgramPassword(value)
      },
      fixPowerOnValue(value) {
        this.generalSettings.powerOnPassword = fixPowerOnPassword(value)
      },
      fixSoundEncryptValue(value) {
        this.generalSettings.soundEncryptValue = fixVoiceEncryptKey(value)
      },
      selectPhoneBooks(books) {
        this.phoneBook = books
      },
      selectAddressBooks(books) {
        this.selectedAddressBook = books
      },
      // 以默认的配置覆盖当前的配置
      newConfig() {
        this.clearDeviceDataConfig(true)
        // 清除设备频率信息
        this.deviceWriteInfo = Object.assign(this.deviceWriteInfo, {
          maxFrequency: 0,
          minFrequency: 0,
        })
        // 清除当前选择的终端设备
        this.selectedDeviceDmrId = ''
        this.generalSettings = cloneDeep(GeneralSettings)
        this.menuSettings = cloneDeep(MenuSettings)
        this.patrolConfig = {}
        this.emergencyAlarm = {}
        this.TrackMonitor = cloneDeep(TrackMonitor)
        this.ActiveRFID = cloneDeep(ActiveRFID)
        // 设置信道默认配置
        this.channelDatas = this.getDefaultChannelData()
        this.channelSettings = this.channelDatas[0]

        // 恢复通讯录、接收组、短信、电话簿默认配置
        bfStorage.removeItem(`fancytree:${bfglob.userInfo.rid}:${this.addrBookTreeId}`)
        this.initRxGroupList()
        this.initSms()
      },
      exportConfig() {
        // 只导出设备的写频配置，不导出设备的接收组、信道数据、设备的dmrId等
        const dataStructType = ['1', '2', '4', '6', '7', '9', '11', '12', '14', '15', '20', '22']
        const jsonData = dataStructType
          .map(type => {
            let config = this.getBeforeEncodeData(type)
            // 删除一些不需要导出的参数
            switch (type) {
              case '4':
                // 将常规设置中的设备dmrId和设备名称去掉
                delete config.deviceName
                delete config.ids
                if (config.clientType) {
                  delete config.clientType.clientLevel
                }
                break
              case '11':
                config = config.map(item => {
                  if (item.digitalChannelPara) {
                    delete item.digitalChannelPara.recvGroupId
                    delete item.digitalChannelPara.defaultDigitalAddress
                  }
                  // delete item.channelName
                  return item
                })
                break
            }
            if (!Array.isArray(config)) {
              config = [config]
            }
            return { [type]: config }
          })
          .reduce((p, c) => {
            return Object.assign(p, c)
          }, {})

        this.exportData(jsonData)
      },
      customImportConfig(jsonData) {
        this.importConfig(jsonData)
        // 导入完成后，重置信道参数
        if (this.selectDeviceData) {
          this.resetChannelDatasArgs(this.selectDeviceData)
        }
      },
      readDataConfig() {
        if (!this.canRead() || this.isReading) {
          return
        }
        // 开始读取数据提示
        bfNotify.messageBox(this.$t('msgbox.startReading'))
        if (this.selectedDeviceDmrId) {
          this.selectedDeviceDmrId = ''
        } else {
          this.clearDeviceDataConfig()
        }
        this.sendAuthentication()
      },
      // 写入数据
      validateChannelRules() {
        // 找到信道数据的表单校验规则，再检验数据是否通过，不通过，将该信道数据放到设置页中显示
        return new Promise((resolve, reject) => {
          // 使用表单验证库，将规则传入生成验证器
          const validator = new AsyncValidator(this.channelSettingsRules)
          // 生成数据迭代器
          const iterator = this.channelDatas[Symbol.iterator]()

          // 递归处理异步验证数据
          const asyncCheckData = item => {
            if (item.done) {
              resolve(item.done)
              return
            }

            this.validateChannelOneData(validator, item.value)
              .then(vaild => {
                asyncCheckData(iterator.next())
              })
              .catch(e => {
                reject(e)
              })
          }

          asyncCheckData(iterator.next())
        })
      },
      validateAllRules() {
        return new Promise((resolve, reject) => {
          const validateList = [
            {
              ref: 'generalSettings',
              msg: this.$t('writeFreq.generalSettingsFormValidate'),
            },
            // { ref: 'digitAlarm', msg: this.$t('writeFreq.digitalAlarmFormValidate') },
            // { ref: 'scanGroup', msg: this.$t('writeFreq.scanGroupFormValidate') }
          ]
          // 信道列表中有数据时才需要验证
          // if (this.channelDatas.length) {
          // if (!this.channelDatas.includes(this.channelSettings)) {
          //   this.channelSettings = this.channelDatas[0]
          // }
          // validateList.push({ ref: 'channelData', msg: this.$t('writeFreq.channelDataFormValidate') })
          // }
          const iterator = validateList[Symbol.iterator]()

          const validate = item => {
            if (item.done) {
              if (this.channelDatas.length) {
                this.validateChannelRules()
                  .then(() => {
                    resolve()
                  })
                  .catch(() => {
                    reject(this.$t('writeFreq.channelDataFormValidate'))
                  })
                return
              }
              resolve()
            }
            const { ref, msg } = item.value
            this.formValidate(ref)
              .then(() => {
                validate(iterator.next())
              })
              .catch(() => {
                reject(msg)
              })
          }
          validate(iterator.next())
        })
      },
      writeInFrequency() {
        this.writeDataConfig()
      },

      getChannelDataById(channelId) {
        for (let i = 0; i < this.channelDatas.length; i++) {
          const item = this.channelDatas[i]
          if (item.channelId === channelId) {
            return item
          }
        }
        return undefined
      },
      getSmsElById(id) {
        const ref = 'sms' + id
        let el = this.$refs[ref]
        if (Array.isArray(el)) {
          el = el[0]
        }
        return el
      },
      getBeforeEncodeData(type) {
        const typeStr = type + ''
        switch (typeStr) {
          // 设备信息
          case '1':
            return cloneDeep(this.deviceWriteInfo)
          // 身份信息
          case '2':
            return cloneDeep(this.identityInfo)
          // 通讯密码
          case '3':
            return cloneDeep(this.passwordInfo)
          // 总体设置
          case '4':
            return cloneDeep(this.generalSettings)
          case '6':
            return cloneDeep(this.smsContent)
          // 菜单设置
          case '7':
            return cloneDeep(this.menuSettings)
          // 数字通讯录设置
          case '9':
            return cloneDeep(this.selectedAddressBook)
          // 接收组列表
          case '10':
            return this.receiveGroup ? this.receiveGroup.getWriteRxGroupList() : []
          // 信道
          case '11':
            return cloneDeep(this.channelDatas).map(channel => {
              if (this.receiveGroup) {
                channel.digitalChannelPara.recvGroupId = this.receiveGroup.getGroupIdByChId(channel.channelId)
              } else {
                channel.digitalChannelPara.recvGroupId = 0xff
              }
              return channel
            })
          // 配置
          case '12':
            return cloneDeep(this.patrolConfig)
          // 紧急报警
          case '14':
            return cloneDeep(this.emergencyAlarm)
          // 自动定位监控
          case '15':
            return cloneDeep(this.TrackMonitor)
          // 有源rfid
          case '20':
            return cloneDeep(this.ActiveRFID)
          // 可用信道
          case '21':
            const areaChannelIndexTable = []
            const defVal = 0xffff
            const cur_channels = this.selectDeviceData ? this.selectDeviceData.channels || [] : []
            const cur_channels_len = cur_channels.length
            for (let idx = 0; idx < cur_channels_len; idx++) {
              const ch_data = cur_channels[idx]
              areaChannelIndexTable[idx] = ch_data.no - 1
            }
            areaChannelIndexTable.length = this.totalChannels
            areaChannelIndexTable.fill(defVal, cur_channels_len)

            this.availableChannels = {
              areaChannelIndexTable: areaChannelIndexTable,
            }
            return cloneDeep(this.availableChannels)
          // 电话簿
          case '22':
            return this.phoneBook.filter(item => {
              return !!bfglob.gphoneBook.getDataByIndex(item.phoneNo)
            })
          default:
            return {}
        }
      },

      showUnvalidateChanneldata(data) {
        this.resetChSettings(data)
        this.channelSettingsTabName = 'chSettings'
        if (this.writerFrequencyTabName !== 'channelSettings') {
          this.writerFrequencyTabName = 'channelSettings'
        }
      },
      // 因信道有多个数据，需要每个数据都能通过规则校验，需要额外处理
      validateChannelOneData(validator, data = {}) {
        return new Promise((resolve, reject) => {
          if (!validator && typeof validator.validate !== 'function') {
            reject(false)
            return
          }

          validator.validate(data, (errors, fields) => {
            if (errors) {
              this.showUnvalidateChanneldata(data)
              reject(errors)
            } else {
              resolve(true)
            }
          })
        })
      },

      saveDefaultFrequency(frequency = 400) {
        this.defaultFrequency.value = this.frequencyMhz2Hz(frequency)
        this.defaultFrequency.label = this.frequencyHz2Mhz(this.defaultFrequency.value)
      },
      asyncDeviceWriteInfo(res) {
        if (!res || !Array.isArray(res.result)) {
          return
        }

        this.deviceWriteInfo = Object.assign(this.deviceWriteInfo, res.result[0])
        this.saveDefaultFrequency(this.deviceWriteInfo.minFrequency)
      },
      asyncIdentityInfo(res) {
        const result = res.result[0]
        if (!result) {
          return
        }
        this.identityInfo = merge(this.identityInfo, result)

        // 显示序列号
        this.deviceWriteInfo['serizeNumber'] = this.identityInfo.serizeNumber
      },
      // general settings
      asyncGeneralSettings(res) {
        const result = res.result[0]
        if (!result) {
          return
        }
        this.generalSettings = merge(this.generalSettings, result)
        // 因为导入进来的配置中没有ids和设备名称，不能覆盖旧的数据
        if (result.ids) {
          this.generalSettings.dmrId = bfutil.int32Dmrid2Hex(result.ids)
        }
      },
      asyncMenuSettings(res) {
        const settings = res.result[0]
        if (!settings) {
          return
        }
        this.menuSettings = merge(this.menuSettings, settings)
      },
      asyncAddressBook(res) {
        if (!res || !Array.isArray(res.result)) {
          return
        }

        // 同步本地通讯录树
        if (this.addressBookTree) {
          this.addressBookTree.asyncNodeSelectStatus(res.result)
        }

        this.originAddressBook = this.originAddressBook.concat(res.result)
      },
      getOriginAddressBook(id) {
        for (let i = 0; i < this.originAddressBook.length; i++) {
          const item = this.originAddressBook[i]
          if (item.id === id) {
            return item
          }
        }
        return undefined
      },
      asyncRxGroup(res) {
        if (!res || !Array.isArray(res.result)) {
          return
        }

        // 处理读取回来的接收组数据
        if (this.receiveGroup) {
          this.receiveGroup.asyncRxGroup(res.result)
        }
      },
      setChannelDataItem(channel) {
        for (let i = 0; i < this.channelDatas.length; i++) {
          const item = this.channelDatas[i]
          if (item.channelId === channel.channelId) {
            this.channelDatas[i] = merge(item, channel)
            return
          }
        }
        this.channelDatas.push(channel)
      },
      asyncChannelSettings(res) {
        // 将读取回来的信道数据解构成信道设置页结构数据
        for (let i = 0; i < res.result.length; i++) {
          const item = res.result[i]
          this.setChannelDataItem(item)
        }
        // 判断是否需要将该信道的数据显示在设置页
        if (!this.channelDatas.includes(this.channelSettings)) {
          const channel = this.getChannelDataById(this.channelSettings.channelId)
          this.channelSettings = channel || this.channelDatas[0]
        }
      },
      asyncPatrolConfig(res) {
        if (!res || !Array.isArray(res.result)) {
          return
        }

        this.patrolConfig = merge(this.patrolConfig, res.result[0])
      },
      asyncEmergencyAlarm(res) {
        const settings = res.result[0]
        if (!settings) {
          return
        }
        this.emergencyAlarm = merge(this.emergencyAlarm, settings)
      },
      asyncTrackMonitor(res) {
        const settings = res.result[0]
        if (!settings) {
          return
        }
        this.TrackMonitor = merge(this.TrackMonitor, settings)
      },
      asyncActiveRFID(res) {
        const settings = res.result[0]
        if (!settings) {
          return
        }
        this.ActiveRFID = merge(this.ActiveRFID, settings)
      },
      asyncShortMessage(res) {
        if (!res || !Array.isArray(res.result)) {
          return
        }
        for (let i = 0; i < res.result.length; i++) {
          const sms = res.result[i]
          const index = this.smsContent.findIndex(item => item.msgId === sms.msgId)
          if (index === -1) {
            this.smsContent.push(sms)
          } else {
            this.smsContent[index] = sms
          }
        }
      },
      setChannelEnableFlag(channel) {
        // 标记哪几个信道有启用
        for (let i = 0; i < this.channelDatas.length; i++) {
          const item = this.channelDatas[i]
          if (item.channelId === channel.channelId) {
            this.channelDatas[i] = merge(item, channel)
            break
          }
        }
      },
      cleanChannelEnableFlag() {
        for (let i = 0; i < this.channelDatas.length; i++) {
          const item = this.channelDatas[i]
          item.enable = false
          this.channelDatas[i] = { ...item }
        }
      },
      asyncAvailableChannels(res) {
        this.availableChannels = res.result[0]
        if (!this.availableChannels.areaChannelIndexTable) {
          return
        }

        this.cleanChannelEnableFlag()

        for (let i = 0; i < this.availableChannels.areaChannelIndexTable.length; i++) {
          const item = this.availableChannels.areaChannelIndexTable[i]
          const config = {
            channelId: item,
            enable: true,
          }
          this.setChannelEnableFlag(config)
        }
      },
      asyncPhoneBook(res) {
        if (!res || !Array.isArray(res.result)) {
          return
        }

        // 保存本地数据
        for (let i = 0; i < res.result.length; i++) {
          const pb = res.result[i]
          const index = this.phoneBook.findIndex(item => item.phoneId === pb.phoneId)
          if (index === -1) {
            this.phoneBook.push(pb)
          } else {
            this.phoneBook[index] = pb
          }
        }
        if (this.phoneBookTree) {
          this.phoneBookTree.asyncNodeSelectStatus(res.result)
        }
      },
      asyncLocalConfig(data) {
        bfglob.console.log('asyncLocalConfig data:', data.type, data)
        switch (data.type) {
          // 单个配置对象
          // "1": "deviceWriteInfo",设备信息
          case 1:
            this.asyncDeviceWriteInfo(data)
            break
          case 2:
            this.asyncIdentityInfo(data)
            break
          // "4": "generalSettings",总体设置
          case 4:
            this.asyncGeneralSettings(data)
            break
          // "7": "menuSettings",菜单设置
          case 7:
            this.asyncMenuSettings(data)
            break
          // "12": "PatrolConfig",配置
          case 12:
            this.asyncPatrolConfig(data)
            break
          // "14": "EmergencyAlarm",紧急报警
          case 14:
            this.asyncEmergencyAlarm(data)
            break
          // "15": "TrackMonitor",自动定位监控
          case 15:
            this.asyncTrackMonitor(data)
            break
          // "20": "ActiveRFID",有源RFID
          case 20:
            this.asyncActiveRFID(data)
            break
          // "21": "AvailableChannels",可用信道
          case 21:
            this.asyncAvailableChannels(data)
            break

          // 多个结果集
          // "6": "ShortMessage",短信
          case 6:
            this.asyncShortMessage(data)
            break
          // "9": "addressBook",数字通讯录
          case 9:
            this.asyncAddressBook(data)
            break
          // "10": "RxGroup",接收组列表
          case 10:
            this.asyncRxGroup(data)
            break
          // "11": "channelSettings",信道
          case 11:
            this.asyncChannelSettings(data)
            break
          // 22：PhoneBook,电话簿
          case 22:
            this.asyncPhoneBook(data)
            break
        }
      },

      showDeviceInsetOrRemovedMessage() {
        let msg = this.$t('msgbox.usbDeviceInsertSuccess')
        let type = 'success'

        if (this.noDevice) {
          msg = this.$t('msgbox.usbDeviceHasBeenOut')
          type = 'warning'
        }
        bfNotify.messageBox(msg, type)
      },
      // 信道表格单击/双击事件
      channelListRowClick(row, event, rowConfig) {
        this.resetChSettings(row)
      },
      channelListRowDblclick(row, event, rowConfig) {
        this.resetChSettings(row)
        this.channelSettingsTabName = 'chSettings'
      },
      resetChSettings(val) {
        this.channelSettings = val
        this.channelDataId = val.channelId
      },
      resetChannelDatas() {
        if (this.channelDatas.length) {
          this.channelDatasCache = cloneDeep(this.channelDatas)
        }
        this.channelSettings = this.channelDatas[0]
      },
      clearSelectedDeviceDmrId() {
        this.selectedDeviceDmrId = ''
      },
      // HZ and MHZ transfer,1GHz=1000MHz，1MHz=1000kHz，1kHz=1000Hz
      frequencyHz2Mhz(hz) {
        return bfutil.frequencyHz2Mhz(hz)
      },
      frequencyMhz2Hz(mhz) {
        return bfutil.frequencyMhz2Hz(mhz)
      },

      // 覆盖常规设置
      resetGeneralSettings(device) {
        this.generalSettings = Object.assign(this.generalSettings, {
          deviceName: device.selfId,
          dmrId: device.dmrId,
          ids: parseInt(device.dmrId, 16),
        })
        this.generalSettings.clientType.clientLevel = device.priority
      },
      // 接收组列表,将发射组也添加到通讯录中
      initRxGroupList() {
        if (!this.receiveGroup) {
          return []
        }
        return this.receiveGroup.initRxGroupList(this.selectedChannels)
      },
      getAddressNameByDmrId(dmrId) {
        // 从读取回来的通讯录中查找对应的dmrId的通讯录名称
        for (let i = 0; i < this.originAddressBook.length; i++) {
          const item = this.originAddressBook[i]
          if (item.dmrId === dmrId) {
            return item.name
          }
        }

        // 在通讯录中无法找到数据，则从本地的数据中查找
        const org = bfglob.gorgData.getDataByIndex(dmrId)
        return org ? org.orgShortName : ''
      },
      getDefaultDigitalAddressId(dmrId) {
        for (let i = 0; i < this.selectedAddressBook.length; i++) {
          const item = this.selectedAddressBook[i]
          if (item.dmrId === dmrId) {
            return item.id
          }
        }
        return 0xffff
      },
      getSystemAddrBook(dmrId) {
        // 查找通讯录对应的发射组dmrId
        for (let i = 0; i < this.selectedAddressBook.length; i++) {
          const item = this.selectedAddressBook[i]
          if (item.dmrId === dmrId) {
            return item
          }
        }

        return undefined
      },
      getDeviceChannelData(device, channelId) {
        for (let i = 0; i < device.channels.length; i++) {
          const item = device.channels[i]
          if (item.no === channelId + 1) {
            return item
          }
        }
        return undefined
      },
      getDefaultAddress(dmrId) {
        // 默认发射组id
        const addrBook = this.getSystemAddrBook(dmrId)
        return addrBook ? addrBook.id : 0xffff
      },
      getChannelCache(channelId) {
        return this.channelDatasCache.find(channel => channel.channelId === channelId)
      },
      resetChannelDatasArgs() {
        this.cleanChannelEnableFlag()
        // 信道默认最小频率
        const minFreq = this.frequencyMhz2Hz(this.deviceWriteInfo.minFrequency || 400)
        // 从设备配置的信道数据中，找到对应的接收组和发射组信息，合并到对应的信道数据上
        for (let i = 0; i < this.selectedChannels.length; i++) {
          const item = this.selectedChannels[i]
          // 从接收组中读取相关数据
          const channelId = item.no - 1
          const config = {
            recvFreq1: minFreq,
            sendFreq1: minFreq,
            recvFreq2: minFreq,
            sendFreq2: minFreq,
            recvFreq3: minFreq,
            sendFreq3: minFreq,
            ...this.getChannelCache(channelId),
            channelId,
            digitalChannelPara: {
              recvGroupId: this.getRxGroupId(channelId),
              defaultDigitalAddress: this.getDefaultAddress(item.sendGroup),
            },
            enable: true,
          }
          this.setChannelEnableFlag(config)
        }

        const firstData = this.channelDatas[0]
        firstData && this.resetChSettings(firstData)
      },

      // 同步设备管理数据变化
      updateDeviceData(data) {
        // console.log("updateDeviceData:", data);
        if (!data) {
          return
        }
        if (this.selectedDeviceDmrId === data.oldDmrId) {
          this.selectedDeviceDmrId = data.dmrId
        }
        if (this.addressBookTree) {
          this.addressBookTree.updateAddrBookTree(data)
        }
      },
      deleteDeviceData(rid, data) {
        // console.log("deleteDeviceData:", data);
        if (!data) {
          return
        }
        if (this.selectedDeviceDmrId === data.dmrId) {
          this.selectedDeviceDmrId = ''
        }
        if (this.addressBookTree) {
          this.addressBookTree.updateAddrBookTree(data)
        }
      },
      globalDeviceChannelsChanged(device) {
        if (!device) {
          return
        }
        // if (this.selectedDeviceDmrId === device.dmrId) {
        //   this.selectedDeviceDmrId = ''
        //   this.$nextTick(() => {
        //     this.selectedDeviceDmrId = device.dmrId
        //   })
        // }
        // this.cleanChannelEnableFlag()
        // for (let i = 0; i < device.channels.length; i++) {
        //   let item = device.channels[i]
        //   const channelId = item.no - 1
        //   let config = {
        //     channelId,
        //     digitalChannelPara: {
        //       recvGroupId: this.getRxGroupId(channelId),
        //       defaultDigitalAddress: this.getDefaultAddress(item.sendGroup),
        //     },
        //   }
        //   this.setChannelEnableFlag(config)
        // }

        this.resetChannelDatasArgs()
      },

      getDefaultChannelData() {
        const defaultChannelData = []
        const minFreq = this.frequencyMhz2Hz(this.deviceWriteInfo.minFrequency || 400)
        for (let i = 0; i < this.totalChannels; i++) {
          const settings = cloneDeep(ChannelSettings)
          settings.channelId = i
          settings.channelName = `CH ${settings.channelId + 1}`
          settings.recvFreq1 = minFreq
          settings.sendFreq1 = minFreq
          settings.recvFreq2 = minFreq
          settings.sendFreq2 = minFreq
          settings.recvFreq3 = minFreq
          settings.sendFreq3 = minFreq
          settings.enable = false

          defaultChannelData.push(settings)
        }

        return defaultChannelData
      },
      initSms() {
        this.smsContent = []
      },
      async selectDeviceDataChanged(device) {
        // 以当前选中的设备配置覆盖写频参数
        // 常规设置
        this.resetGeneralSettings(device)
        // 接收组
        await this.initRxGroupList()
        // 信道
        this.resetChannelDatasArgs()
      },
      // 清除通讯录被禁用的状态
      clearTreeNodeUnselectable() {
        // 清除通讯录被禁用和非系统通讯数据的节点
        this.addressBookTree.removeNotInSystemNodes()
        this.selectedAddressBook.forEach(item => {
          bftree.nodeUnselectable(this.addrBookTreeId, item.nodeKey)
        })
      },
      clearPrivateConfig() {
        // 清除常规设置的设备名称
        this.generalSettings.deviceName = ''
        this.generalSettings.ids = 0
        // 接收组
        this.receiveGroup.resetRxGroupList()
        // 信道
        this.resetChannelDatas()
        this.clearTreeNodeUnselectable()
        if (this.phoneBookTree) {
          this.phoneBookTree.removeNotInSystemNodes()
        }
      },
      // cleanAll 标记是否清除全部配置
      async clearDeviceDataConfig(cleanAll = false) {
        // 必须清除的数据，接收组、区域、信道等私有数据，包括一些标记参数
        this.clearPrivateConfig()

        // 可选的清除数据，常规设置、菜单、按键定义、警报、通讯录、电话本、短信等通用数据
        if (!cleanAll) {
          return
        }
        this.originAddressBook = []
        this.selectedAddressBook = []
        this.addressBookTree.treeReload(true)
        this.phoneBook = []
        this.phoneBookTree && this.phoneBookTree.treeReload(true)
      },
    },
    computed: {
      dmrIdLabel() {
        const intDmrId = this.generalSettings.ids
        if (intDmrId === 0) {
          return ''
        }
        const dmrId = bfutil.int32Dmrid2Hex(intDmrId)
        return dmrId ? ` ${dmrId} / ${intDmrId}` : ''
      },

      hasFreqRange() {
        return this.deviceWriteInfo.maxFrequency > 0 && this.deviceWriteInfo.minFrequency > 0
      },
      disWriteBtn() {
        return this.disReadBtn || !this.selectedDeviceDmrId || !this.hasFreqRange || this.selectedChannels.length === 0
      },
      disReadBtn() {
        return this.noQWebServer || this.noDevice || this.isWriting || this.isReading
      },
      noQWebServer() {
        return !this.QWebServer
      },
      channelListColumn() {
        return [
          {
            prop: 'channelId',
            label: this.$t('dialog.chId'),
            width: this.isFullscreen ? 'auto' : this.isEN ? '150px' : '100px',
          },
          {
            prop: 'channelName',
            label: this.$t('dialog.chName'),
            width: this.isFullscreen ? 'auto' : this.isEN ? '150px' : '100px',
          },
          {
            prop: 'recvFreq1',
            label: this.$t('dialog.receiveNumber', { num: 1 }),
            width: this.isFullscreen ? 'auto' : this.isEN ? '150px' : '120px',
          },
          {
            prop: 'sendFreq1',
            label: this.$t('dialog.emissionNumber', { num: 1 }),
            width: this.isFullscreen ? 'auto' : this.isEN ? '150px' : '120px',
          },
          {
            prop: 'recvFreq2',
            label: this.$t('dialog.receiveNumber', { num: 2 }),
            width: this.isFullscreen ? 'auto' : this.isEN ? '150px' : '120px',
          },
          {
            prop: 'sendFreq2',
            label: this.$t('dialog.emissionNumber', { num: 2 }),
            width: this.isFullscreen ? 'auto' : this.isEN ? '220px' : '120px',
          },
          {
            prop: 'recvFreq3',
            label: this.$t('dialog.receiveNumber', { num: 3 }),
            width: this.isFullscreen ? 'auto' : this.isEN ? '150px' : '120px',
          },
          {
            prop: 'sendFreq3',
            label: this.$t('dialog.emissionNumber', { num: 3 }),
            width: this.isFullscreen ? 'auto' : this.isEN ? '150px' : '120px',
          },
        ]
      },
      generalSettingsRules() {
        const getDefaultSoundEncryptValueRulesRules = () => {
          return [validateRules.mustLength(['change', 'blur'], 10)]
        }
        let soundEncryptValueRules = []
        if (this.generalSettings.soundEncryptType === 0) {
          soundEncryptValueRules = getDefaultSoundEncryptValueRulesRules()
        } else {
          soundEncryptValueRules = getDefaultSoundEncryptValueRulesRules()
          soundEncryptValueRules.push(validateRules.required())
        }

        return {
          deviceName: [validateRules.required(), validateRules.maxLen('blur', 16)],
          repeaterId: [validateRules.required(), validateRules.mustNumber(), validateRules.range('blur', 1, 16777215, `${1}~${16777215}`)],
          soundEncryptValue: soundEncryptValueRules,
          powerOnPassword: [validateRules.mustLength(['change', 'blur'], 6)],
        }
      },
      channelSettingsRules() {
        const { minFrequency, maxFrequency } = this.deviceWriteInfo
        const freqValidate = [
          validateRules.mustNumber(),
          validateRules.range('blur', bfutil.frequencyMhz2Hz(minFrequency), bfutil.frequencyMhz2Hz(maxFrequency), `${minFrequency}~${maxFrequency}`),
        ]
        return {
          channelName: [validateRules.required(), validateRules.maxLen('blur', 16)],
          recvFreq1: freqValidate,
          recvFreq2: freqValidate,
          recvFreq3: freqValidate,
          sendFreq1: freqValidate,
          sendFreq2: freqValidate,
          sendFreq3: freqValidate,
        }
      },
      repeaterTimeSlotList() {
        return [
          {
            label: '1',
            value: 0,
          },
          {
            label: '2',
            value: 1,
          },
        ]
      },
      permissionConditionsList() {
        // 0 可用彩色码
        // 1 始终
        // 2 信道空闲
        return [
          {
            label: this.$t('dialog.availableColorCode'),
            value: 0,
          },
          {
            label: this.$t('writeFreq.always'),
            value: 1,
          },
          {
            label: this.$t('dialog.channelIdle'),
            value: 2,
          },
        ]
      },
      txPowerTypes() {
        // 0-低
        // 1-高
        return [
          {
            label: this.$t('dialog.low'),
            value: 0,
          },
          {
            label: this.$t('dialog.high'),
            value: 1,
          },
        ]
      },
      soundEncryptTypeList() {
        return [
          {
            value: 0,
            label: this.$t('dialog.unencrypted'),
          },
          {
            value: 1,
            label: this.$t('dialog.staticEncryption'),
          },
          {
            value: 2,
            label: this.$t('dialog.dynamicencryption'),
          },
        ]
      },
      clientLevelOpts() {
        return [
          {
            label: this.$t('dialog.low'),
            value: 1,
          },
          {
            label: this.$t('dialog.mid'),
            value: 2,
          },
          {
            label: this.$t('dialog.high'),
            value: 3,
          },
        ]
      },
      onOffList() {
        return [
          {
            label: this.$t('dialog.off'),
            value: 0,
          },
          {
            label: this.$t('dialog.on'),
            value: 1,
          },
        ]
      },
      rfidModeList() {
        return [
          {
            label: this.$t('dialog.autoCardReading'),
            value: 0,
          },
          {
            label: this.$t('dialog.triggerCardReading'),
            value: 1,
          },
        ]
      },
      rfidPowerList() {
        // 0 -18dbm
        // 1 -12dbm
        // 2 -6dbm
        // 3 0dbm
        return [
          {
            label: '-18dbm',
            value: 0,
          },
          {
            label: '-12dbm',
            value: 1,
          },
          {
            label: '-6dbm',
            value: 2,
          },
          {
            label: '0dbm',
            value: 3,
          },
        ]
      },
      rfidAnswerList() {
        // 0 数传指令应答
        // 1 采用芯片自动应答机制
        return [
          {
            label: this.$t('dialog.dataTrsCmdRes'),
            value: 0,
          },
          {
            label: this.$t('dialog.chipAutoResMechanism'),
            value: 1,
          },
        ]
      },
      patrolSystemConfigureFormLabelWidth() {
        return this.isFR || this.isEN ? '150px' : '100px'
      },
      channelIdList() {
        const list = []
        for (let i = 0; i < this.channelDatas.length; i++) {
          const item = this.channelDatas[i]
          const chId = item.channelId + 1
          const opt = {
            label: chId,
            value: item.channelId,
          }
          list.push(opt)
        }
        return list
      },
      // 虚拟时隙
      slotModeList() {
        return [
          {
            label: '1',
            value: 0,
          },
          {
            label: '2',
            value: 1,
          },
          {
            label: this.$t('dialog.virtualCluster'),
            value: 2,
          },
        ]
      },
      virtualTimeSlotList() {
        return [
          {
            label: this.$t('dialog.nothing'),
            value: 0,
          },
          {
            label: '1',
            value: 1,
          },
          {
            label: '2',
            value: 2,
          },
        ]
      },
      recvGroupIdList() {
        const def = [
          {
            label: this.$t('dialog.nothing'),
            value: 0xff,
          },
        ]
        const rxGroupList = this.rxGroupList.map(item => {
          return {
            label: item.groupName,
            value: item.groupId,
          }
        })
        return def.concat(rxGroupList)
      },
      defaultDigitalAddressList() {
        const cache = []
        return [
          {
            label: this.$t('dialog.nothing'),
            value: 0xffff,
          },
        ].concat(
          this.selectedAddressBook
            .concat(this.originAddressBook)
            .filter(address => {
              if (cache.includes(address.id)) {
                return false
              }
              cache.push(address.id)
              return true
            })
            .map(address => {
              return {
                label: address.name,
                value: address.id,
              }
            })
        )
      },
      selectDeviceData() {
        return bfglob.gdevices.getDataByIndex(this.selectedDeviceDmrId)
      },
      addressBookCallTypes() {
        return AddressBookCallTypes
      },
      addressBookTree() {
        return this.$refs[this.addrBookTreeId]
      },
      phoneBookTree() {
        return this.$refs[this.phoneBookTreeId]
      },

      receiveGroup() {
        return this.$refs[this.refReceiveGroup]
      },
      writeDataOption() {
        return [
          {
            type: 4,
            failedMsg: this.$t('msgbox.writeRegularSettingsFailed'),
          },
          {
            type: 6,
            failedMsg: this.$t('msgbox.writeSMSFailed'),
            option: { limit: 1 },
          },
          {
            type: 7,
            failedMsg: this.$t('msgbox.writeMenuFailed'),
          },
          {
            type: 9,
            failedMsg: this.$t('msgbox.writeAddressBookFailed'),
          },
          {
            type: 10,
            failedMsg: this.$t('msgbox.writeReceivingGroupFailed'),
          },
          {
            type: 11,
            failedMsg: this.$t('msgbox.writeChannelDataFailed'),
          },
          {
            type: 12,
            failedMsg: this.$t('msgbox.writePatrolSystemConfigFailed'),
          },
          {
            type: 14,
            failedMsg: this.$t('msgbox.writeEmergencyAlarmConfigFailed'),
          },
          {
            type: 15,
            failedMsg: this.$t('msgbox.writeTraceMonitorConfigFailed'),
          },
          {
            type: 20,
            failedMsg: this.$t('msgbox.writeActiveRFIDConfigFailed'),
          },
          {
            type: 21,
            failedMsg: this.$t('msgbox.writeValidChannelFailed'),
          },
          {
            type: 22,
            failedMsg: this.$t('msgbox.writePhoneBookFailed'),
          },
          // 最后写入编程密码
          {
            type: 3,
            failedMsg: this.$t('msgbox.writeProgrammingPwdFailed'),
          },
        ]
      },
      getClassInstance() {
        return getClassInstance
      },
      Model() {
        return Model
      },
    },
    watch: {
      'generalSettings.soundEncryptType'(val) {
        if (val === 0) {
          this.generalSettings.soundEncryptValue = ''
        }
      },
      // 常规设置中勾选全部静音，语音指示、信道空闲指示、呼叫允许指示不可勾选
      'generalSettings.soundAndDisplayTip.allSlient'(val) {
        if (val) {
          this.generalSettings.soundAndDisplayTip.voiceNotice = false
          this.generalSettings.soundAndDisplayTip.channelFreeNotice = false
          this.generalSettings.soundAndDisplayTip.allowCallInstruction = false
        }
      },
      'TrackMonitor.trackEnable'(val) {
        // 关闭时，其他参数设置为0,打开时，其他参数填充默认值
        if (val) {
          this.TrackMonitor.rollTime = 300
          this.TrackMonitor.rollDistant = 50
        } else {
          this.TrackMonitor.rollTime = 0
          this.TrackMonitor.rollDistant = 0
        }
      },
      // mixins计算属性
      selectDeviceData(data) {
        this.clearPrivateConfig()

        this.$nextTick(() => {
          if (data) {
            // 将选中的设备中关于信道等数据同步到界面中
            this.selectDeviceDataChanged(data)
          }
        })
      },
      channelDataId(val, old) {
        // 当信道id变化时，重置表单检验结果
        this.$refs.channelSettings.clearValidate()

        const data = this.getChannelDataById(val)
        if (data) {
          this.resetChSettings(data)
        }
      },
      'channelSettings.digitalChannelPara.switches.registerToSystem'(val) {
        this.channelSettings.digitalChannelPara.switches.slotMode = 2
      },
      'channelSettings.digitalChannelPara.switches.slotMode'(val) {
        if (val !== 2) {
          this.channelSettings.digitalChannelPara.switches.virtualTimeSlot = 0
        }
      },
    },
    components: {
      selectDevice,
      WriteFreqFooter,
      bfInputNumber: defineAsyncComponent(() => import('@/components/common/bfInputNumber')),
      frequencyMhz: defineAsyncComponent(() => import('@/components/common/FrequencyMhz')),
      addressBook: defineAsyncComponent(() => import('@/platform/dataManage/deviceManage/common/addressBook')),
      phoneBook: defineAsyncComponent(() => import('@/platform/dataManage/deviceManage/common/phoneBook')),
      shortMessage: defineAsyncComponent(() => import('@/platform/dataManage/deviceManage/common/shortMessage')),
      receiveGroup: defineAsyncComponent(() => import('@/platform/dataManage/deviceManage/common/receiveGroup')),
      deviceInfo,
      patrolConfig: defineAsyncComponent(() => import('@/platform/dataManage/deviceManage/common/patrolConfig')),
      emergencyAlarmConfig: defineAsyncComponent(() => import('@/platform/dataManage/deviceManage/common/emergencyAlarmConfig')),
    },
    beforeMount() {
      this.channelDatas = cloneDeep(this.getDefaultChannelData())

      bfglob.on('vdevices_table_update_data', this.updateDeviceData)
      bfglob.on('vdevices_table_delete_data', this.deleteDeviceData)
      bfglob.on('device_channel_changed', this.globalDeviceChannelsChanged)
    },
    beforeUnmount() {
      // Vue实例销毁前，取消订阅的一些方法主题
      bfglob.off('vdevices_table_update_data', this.updateDeviceData)
      bfglob.off('vdevices_table_delete_data', this.deleteDeviceData)
      bfglob.off('device_channel_changed', this.globalDeviceChannelsChanged)
    },
  }
</script>

<style lang="scss">
  @use '@/css/interphoneWf/tabsWf.scss' as *;
</style>
