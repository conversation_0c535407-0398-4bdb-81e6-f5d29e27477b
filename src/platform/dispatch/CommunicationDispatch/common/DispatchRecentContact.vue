<script lang="ts" setup>
  import { RecycleScroller } from 'vue-virtual-scroller'
  import 'vue-virtual-scroller/dist/vue-virtual-scroller.css'
  import { DispatchRecentContactCardEmit } from './DispatchRecentContactCard.vue'
  import { calcScaleSize } from '@/utils/setRem'
  import { watch, ref, useTemplateRef } from 'vue'
  import { useRecentContact } from '@/utils/callContact'
  import { useResizeObserver } from '@vueuse/core'
  import { useI18n } from 'vue-i18n'
  import bfNotify from '@/utils/notify'
  import { insertDbGroupCallContact2db, deleteDbGroupCallContactByOrgId2db } from '@/utils/callContact'
  import { useCommonContact } from '@/utils/callContact/commonContact'
  import type { RecentContact } from '@/utils/callContact'
  import { setSpeakTarget, globalVoipServerManager, speakState, speakFast } from '@/utils/speak'

  const { t } = useI18n()
  const { recentContacts } = useRecentContact()
  const { commonContacts } = useCommonContact()

  const scrollerRef = useTemplateRef('scroller')

  watch(
    () => recentContacts.value,
    () => {
      scrollerRef.value?.updateVisibleItems(true)
    },
    { deep: true }
  )

  const emit = defineEmits<DispatchRecentContactCardEmit>()

  const itemSize = ref(calcScaleSize(115))
  const itemSecondarySize = ref(calcScaleSize(284))

  useResizeObserver(document.documentElement, () => {
    itemSize.value = calcScaleSize(115)
    itemSecondarySize.value = calcScaleSize(284)
  })

  const cleanRecentContacts = () => {
    if (recentContacts.value.length === 0) {
      bfNotify.messageBox(t('dispatch.noRecentContacts'))
      return
    }
    recentContacts.value.splice(0, recentContacts.value.length)
    bfNotify.messageBox(t('dispatch.deleteRecentContactsSuccess'))
  }

  // 检查是否在常用联系人中
  const isInCommonContact = (recentContact: RecentContact) => {
    return commonContacts.value.some(contact => contact.targetRid === recentContact.targetRid)
  }

  // 添加到常用联系人
  const handleAddToCommonContact = (targetDmrIDHex: string) => {
    // 根据 targetDmrIDHex 找到对应的最近联系人
    const recentContact = recentContacts.value.find(contact => contact.targetDmrIDHex === targetDmrIDHex)
    if (!recentContact) {
      bfNotify.messageBox(t('msgbox.addError'), 'error')
      return
    }

    insertDbGroupCallContact2db(recentContact.targetRid, 0)
      .then(() => {
        bfNotify.messageBox(t('msgbox.addSuccess'), 'success')
      })
      .catch(() => {
        bfNotify.messageBox(t('msgbox.addError'), 'error')
      })
  }

  // 从常用联系人移除
  const handleRemoveFromCommonContact = (targetDmrIDHex: string) => {
    // 根据 targetDmrIDHex 找到对应的最近联系人
    const recentContact = recentContacts.value.find(contact => contact.targetDmrIDHex === targetDmrIDHex)
    if (!recentContact) {
      bfNotify.messageBox(t('msgbox.delError'), 'error')
      return
    }

    deleteDbGroupCallContactByOrgId2db(recentContact.targetRid)
      .then(() => {
        bfNotify.messageBox(t('msgbox.delSuccess'), 'success')
      })
      .catch(() => {
        bfNotify.messageBox(t('msgbox.delError'), 'error')
      })
  }

  // 呼叫函数
  const call = (targetDmrId: string, item: RecentContact, callback?: (success: boolean) => void) => {
    // 如果正在呼叫，则直接返回
    if (speakState.current === 1) {
      callback?.(false)
      return
    }

    // 直接使用 speak.ts 的 speakFast 函数，不再打开 bfSpeaking.vue 对话框
    setSpeakTarget(item.targetRid)
    speakFast(targetDmrId)
      .then(() => {
        // 只有在通话真正连通后才执行 callback(true)
        callback?.(true)
      })
      .catch(() => {
        callback?.(false)
      })
  }

  // 挂断函数
  const hangup = (targetDmrId: string, item: RecentContact, callback?: (success: boolean) => void) => {
    if (speakState.current === 1) {
      globalVoipServerManager.sl_i_speak_end()
    }
    callback?.(true)
  }
</script>

<template>
  <PageHeader :title="t('dispatch.recentContacts')">
    <DispatchTitleIcon icon="bfdx-qingchuyangshi-neibu" @click="cleanRecentContacts" />
  </PageHeader>
  <RecycleScroller
    class="recent-contact-container"
    ref="scroller"
    :items="recentContacts"
    :item-size="itemSize"
    :grid-items="2"
    :item-secondary-size="itemSecondarySize"
    key-field="keyId"
  >
    <template #default="{ item, index }">
      <DispatchRecentContactCard
        :key="index"
        v-bind="item"
        :is-common-contact="isInCommonContact(item)"
        @call="(targetDmrIDHex, callback) => call(targetDmrIDHex, item, callback)"
        @hangup="(targetDmrIDHex, callback) => hangup(targetDmrIDHex, item, callback)"
        @add-to-common="handleAddToCommonContact"
        @remove-from-common="handleRemoveFromCommonContact"
      />
    </template>
  </RecycleScroller>
</template>

<style lang="scss" scoped>
  .recent-contact-container {
    height: 100%;
    padding: 10px 0 0;
    border: 1px solid transparent;
    border-image: linear-gradient(to bottom right, rgba(156, 166, 214, 0.88), rgba(122, 136, 203, 0.46)) 30/1px;

    background: linear-gradient(to bottom right, rgba(0, 0, 11, 0.38), rgba(0, 0, 11, 0.26));

    .card-container {
      margin: 10px;
    }
  }
</style>
