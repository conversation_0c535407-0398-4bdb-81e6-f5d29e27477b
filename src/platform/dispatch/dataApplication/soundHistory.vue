<template>
  <div>
    <historyCommon
      id="bfsoundHistory"
      ref="hisCom"
      class="sound-history-data-table"
      :dbListName="queryProps.dbListName"
      :cmd="queryProps.cmd"
      :deviceRid="deviceRid"
      :userRid="userRid"
      :head="dthead"
      :name="dataTableName"
      :exportNamePrefix="dlgTitle"
      :parse-request-data="parseRequestdata"
      @query-finish-sound="queryFinishProxyOperate"
      @remove-data-table-data="removeDataTableData"
    >
      <template #optionsFormItem>
        <el-form-item prop="deviceRid">
          <template #label>
            <EllipsisText :content="$t('dialog.terminalName')" class="!w-[64px] text-right" />
          </template>
          <DataTableElSelect v-model="deviceRid" filterable clearable :placeholder="$t('dialog.select')" :no-match-text="$t('dialog.noMatchText')">
            <el-option v-for="item in deviceRids" :key="item.rid" :label="item.label" :value="item.rid" />
          </DataTableElSelect>
        </el-form-item>
        <el-form-item prop="userRid">
          <template #label>
            <EllipsisText :content="$t('dialog.userName')" class="!w-[64px] text-right" />
          </template>
          <DataTableElSelect v-model="userRid" filterable clearable :placeholder="$t('dialog.select')" :no-match-text="$t('dialog.noMatchText')">
            <el-option v-for="item in userRids" :key="item.rid" :label="item.label" :value="item.rid" />
          </DataTableElSelect>
        </el-form-item>
      </template>
    </historyCommon>

    <audio ref="soundAudio" hidden :src="onlineAudioSrc" />
  </div>
</template>

<script>
  import bfutil from '@/utils/bfutil'

  import vueMixin from '@/utils/vueMixin'
  import historyCommon from '@/components/common/historyCommon.vue'
  import EllipsisText from '@/components/common/EllipsisText.vue'
  import bfNotify from '@/utils/notify'
  import playRecordIconUrl from '@/assets/images/dispatch/dataApplication/play_record.svg'
  import downloadRecordIconUrl from '@/assets/images/dispatch/dataApplication/download_record.svg'

  let lastAudioControlBtn = null

  export default {
    mixins: [vueMixin],
    components: {
      historyCommon,
      EllipsisText,
    },
    data() {
      return {
        queryProps: {
          dbListName: 'db_sound_history_list',
          cmd: 50,
        },
        dataTableName: 'soundHistoryTable',
        deviceRid: '',
        userRid: '',
        deviceRids: bfglob.gdevices.getList(),
        userRids: bfglob.guserData.getList(),
        // <audio>元素的src属性如果直接赋空值，vite编译后会出现`require("")`导致报错，"ReferenceError: require is not defined"
        onlineAudioSrc: '',
      }
    },
    methods: {
      removeDataTableData() {
        this.deviceRid = ''
        this.userRid = ''
      },
      parseRequestdata(item) {
        var device = bfglob.gdevices.get(item.deviceId)
        if (typeof device === 'undefined') {
          bfglob.console.error('没有此对讲机', item.deviceId)
          return
        }
        item.orgShortName = device.orgShortName
        item.deviceSelfId = device.selfId
        if (item.sourceInfo) {
          item.deviceSelfId += ' / ' + item.sourceInfo
        }
        item.userName = bfglob.guserData.getUserNameByKey(item.personId)

        var get_callTargetName_by_dmrId = function (dmrId) {
          var orgItem = bfglob.gorgData.getDataByIndex(dmrId)
          if (orgItem) {
            return orgItem.orgShortName
          }

          var deviceItem = bfglob.gdevices.getDataByIndex(dmrId)
          if (deviceItem) {
            return deviceItem.selfId
          }

          return ''
        }
        var callTarget = get_callTargetName_by_dmrId(item.target)
        item.callTarget = callTarget || item.target
        if (item.targetInfo) {
          item.callTarget += ' / ' + item.targetInfo
        }

        return item
      },
      /**
       * 下载ambe录音数据
       * @param {string} fileName 录音文件名称 格式：主叫DMRID-被叫DMRID-时分秒日月年, 002800AD-002FFFFF-02463711022023.ambe
       * @param {string} deviceSelfId 主叫终端名称
       * @param {string} soundTime 本地时间字符串 YYYY-MM-DD hh:mm:ss
       */
      downloadSoundFile(fileName, deviceSelfId, soundTime) {
        const fileNameList = fileName.split('-')
        // 去掉协议的时间日期格式，转换为本地时间字符串
        fileNameList.pop()
        const soundTimeStr = soundTime.replace(/[-:]/g, '').replace(/\s/, '_')
        // 主叫终端DMRID-时间-主叫终端名称-中继DMRID
        const downloadName = `${soundTimeStr}-${deviceSelfId}-${fileNameList.join('-')}.ambe.wav`
        const url = '/download_sound_wav?sys=' + bfglob.sysId + '&filename=' + fileName
        bfutil.downloadAndSaveFile(url, downloadName)
      },
      stopWavPlay() {
        const audio = this.$refs.soundAudio
        if (audio) {
          audio.pause()
          audio.currentTime = 0
          URL.revokeObjectURL(audio.src)
        }
      },
      stopLastWavPlay() {
        if (lastAudioControlBtn) {
          this.stopWavPlay()
          this.setWavPlayStatus(lastAudioControlBtn)
        }
      },
      setWavPlayStatus(el) {
        el.classList.replace('el-button--warning', 'el-button--primary')
        delete el.dataset.isPlay
        if (el.firstElementChild) {
          el.firstElementChild.classList.replace('mdi-pause-circle-outline', 'mdi-play-circle-outline')
        }
      },
      setWavStopStatus(el) {
        el.dataset.isPlay = 'true'
        el.classList.replace('el-button--primary', 'el-button--warning')
        if (el.firstElementChild) {
          el.firstElementChild.classList.replace('mdi-play-circle-outline', 'mdi-pause-circle-outline')
        }
      },
      wavPlayControl(fileName, el) {
        // 判断当前被点击的按钮是否处于播放中
        if (el.dataset.isPlay || el.dataset.isPlay === 'true') {
          // 处于播放中，则结束播放，恢复待播放状态
          this.stopWavPlay()
          this.setWavPlayStatus(el)
        } else {
          // 停止上一个音频的播放
          this.stopLastWavPlay()
          // 处于待播放状态，进行播放音频，切换到播放中状态
          this.setWavStopStatus(el)

          // 请求音频文件，进行播放
          const url = '/download_sound_wav?sys=' + bfglob.sysId + '&filename=' + fileName
          // 下载音频文件
          fetch(url)
            .then(res => res.blob())
            .then(data => {
              const audio = this.$refs.soundAudio
              if (!audio) {
                bfglob.console.error('can not found audio')
                return
              }

              // 转换路径，进行播放
              audio.src = URL.createObjectURL(data)
              audio.onended = () => {
                // 播放结束，恢复播放按钮初始状态
                this.stopWavPlay()
                this.setWavPlayStatus(el)
              }
              audio.onerror = () => {
                // 音频源数据有问题，无法播放
                this.stopWavPlay()
                this.setWavPlayStatus(el)
                // // 禁用无法播放的音频按钮
                // if (el) {
                //   el.disabled = true
                //   if (!el.classList.contains('is-disabled')) {
                //     el.classList.add('is-disabled')
                //   }
                // }
              }
              audio.play()
            })
            .catch(err => {
              bfglob.console.error('playWavOnline', err)
            })
          // 缓存当前播放的元素，以便切换音频时停止
          lastAudioControlBtn = el
        }
      },
      queryFinishProxyOperate() {
        if (this.$refs.hisCom.dataTableBody.length > 0) {
          setTimeout(() => {
            // 代理播放按钮事件
            const that = this
            const proxyPlayerClick = function () {
              // this指向为被点击的元素
              that.wavPlayControl(this.dataset.fileName, this)
            }
            $('#bfsoundHistory .bfdataTable tbody').off('click', '.sound_player')
            $('#bfsoundHistory .bfdataTable tbody').on('click', '.sound_player', proxyPlayerClick)

            const proxyDownloadClick = function () {
              // this指向为被点击的元素
              that.downloadSoundFile(this.dataset.fileName, this.dataset.deviceSelfId, this.dataset.soundTime)
            }
            $('#bfsoundHistory .bfdataTable tbody').off('click', '.sound_download')
            $('#bfsoundHistory .bfdataTable tbody').on('click', '.sound_download', proxyDownloadClick)
          }, 0)
        } else {
          bfNotify.messageBox(this.$t('msgbox.noRecords'), 'info')
        }
      },
    },
    computed: {
      dthead() {
        return [
          {
            title: this.$t('dialog.parentOrg'),
            data: 'orgShortName',
            width: '120px',
          },
          {
            title: this.$t('dialog.calledDevice'),
            data: 'deviceSelfId',
            width: '120px',
          },
          {
            title: this.$t('dialog.calledTarget'),
            data: 'callTarget',
            width: '120px',
          },
          {
            title: this.$t('dataTable.sourceController'),
            data: 'controller',
            width: this.isFR ? '140px' : '120px',
            render: data => {
              if (!data || data === '00000000') return ''
              const controller = bfglob.gcontrollers.getDataByIndex(data)
              return controller?.selfId ?? data
            },
          },
          {
            title: this.$t('dataTable.callTime'),
            data: 'soundTime',
            width: '120px',
          },
          {
            title: this.$t('dataTable.duration'),
            data: 'soundLen',
            width: '60px',
          },
          {
            title: this.$t('dataTable.callChannel'),
            data: 'channel',
            width: '60px',
          },
          {
            title: this.$t('dialog.userName'),
            data: 'userName',
            width: '120px',
          },
          {
            title: this.$t('dialog.action'),
            data: null,
            width: '60px',
            class: 'text-center last-column-action',
            render: data => {
              let action = ''
              if (data.fileName) {
                // 下载音频原文件
                action += `<button type='button' class='sound_download'
                  data-file-name='${data.fileName}' data-device-self-id='${data.deviceSelfId}' data-sound-time='${data.soundTime}'>
                  <img src="${downloadRecordIconUrl}" alt="download" style='width: 20px; height: 20px;'/></button>`

                // 在线播放转换后的wav音频
                action += `<button type='button' class='sound_player'
                  data-file-name='${data.fileName}'>
                  <img src="${playRecordIconUrl}" alt="play" style='width: 20px; height: 20px;'/></button>`
              }

              return action
            },
          },
        ]
      },
      dlgTitle() {
        return this.$t('nav.soundHistory')
      },
    },
  }
</script>

<style lang="scss">
  .sound-history-data-table {
    .last-column-action {
      display: flex;
      justify-content: space-evenly;
    }
  }
</style>
