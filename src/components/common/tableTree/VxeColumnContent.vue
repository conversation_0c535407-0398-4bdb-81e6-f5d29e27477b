<script setup lang="ts">
  import { TreeNodeType, TreeNodeData } from './types'
  import { computed, useTemplateRef } from 'vue'
  import {
    getDeviceNodeTooltip,
    getOrgNodeTooltip,
    getOrgNodeIcon,
    getDeviceStatusClassName,
    getDeviceChannel,
    getDeviceUserName,
    getNodeOriginData,
  } from './treeUtil'

  const {
    enableCheckbox = true,
    row,
    checked = true,
    indeterminate,
    toggleCheckboxEvent,
    showDeviceChannel = true,
    showDeviceUserName = true,
  } = defineProps<{
    enableCheckbox?: boolean
    row: TreeNodeData
    checked?: boolean
    indeterminate?: boolean
    showDeviceChannel?: boolean
    showDeviceUserName?: boolean
    toggleCheckboxEvent?: (row: TreeNodeData) => void
  }>()

  const slots = defineSlots<{
    default(props: { row: TreeNodeData; checked: boolean; indeterminate: boolean }): unknown
    tooltipContent(props: { row: TreeNodeData; isOverflow: boolean; checked: boolean; indeterminate: boolean }): unknown
  }>()

  const contextValueRef = useTemplateRef('contextValueRef')
  const isOverflow = computed(() => {
    const el = contextValueRef.value
    if (!el) return false
    for (const child of Array.from(el.children)) {
      if (child.scrollWidth > child.clientWidth) {
        return true
      }
    }
    return false
  })

  const displayData = computed(() => {
    if (slots.default) {
      return {
        iconClass: '',
        sumDeviceCount: 0,
        showIcon: false,
        channel: '',
        userName: '',
        content: '',
        tooltipContent: '',
      }
    }

    const originData = getNodeOriginData(row)
    const isOrgNode = row.nodeType === TreeNodeType.Org
    const orgIcon = isOrgNode ? getOrgNodeIcon(originData) : ''
    const sumDeviceCount = isOrgNode ? (originData?.sumDeviceCount as number) : 0
    const deviceIcon = isOrgNode ? '' : getDeviceStatusClassName(originData)
    const channel = isOrgNode ? '' : showDeviceChannel ? getDeviceChannel(originData) : ''
    const userName = isOrgNode ? '' : showDeviceUserName ? getDeviceUserName(originData) : ''
    const content = isOrgNode ? originData.orgShortName : (originData.selfId as string)
    const tooltipContentValue = isOrgNode ? getOrgNodeTooltip(originData) : getDeviceNodeTooltip(originData)

    return {
      iconClass: isOrgNode ? orgIcon : deviceIcon,
      sumDeviceCount: sumDeviceCount,
      showIcon: isOrgNode ? !!orgIcon : deviceIcon !== 'device_status_none',
      channel: channel,
      userName: userName,
      content: content,
      tooltipContent: isOverflow.value ? `${content} ${channel} ${userName} ${tooltipContentValue}` : tooltipContentValue,
    }
  })
</script>

<template>
  <div class="vxe-column-content-wrapper relative h-[20px] px-[5px] w-full inline-flex gap-[5px]">
    <span v-if="enableCheckbox" class="vxe-column-checkbox" @click.stop="toggleCheckboxEvent(row)">
      <i v-if="indeterminate" class="bf-iconfont bfdx-bufenxuanzhong"></i>
      <i v-else-if="checked" class="bf-iconfont bfdx-fuxuanduoanniu"></i>
      <i v-else class="bf-iconfont bfdx-dingweimaodianwaikuang"></i>
    </span>
    <el-tooltip popper-class="bf-tooltip" effect="dark" placement="bottom" :show-after="500" :content="displayData.tooltipContent">
      <template #content>
        <slot name="tooltipContent" :row :isOverflow :checked :indeterminate></slot>
      </template>

      <div
        ref="contextValueRef"
        class="vxe-column-content relative w-auto px-[5px] cursor-default flex justify-start items-center"
        :class="[enableCheckbox ? 'max-w-[calc(100%_-_30px)]' : 'max-w-full']"
      >
        <slot :row :checked :indeterminate>
          <i
            v-show="displayData.showIcon"
            :class="[
              'relative inline-flex items-center bf-iconfont mr-[5px] w-[15px] h-[20px] text-center before:text-[15px] text-[#1A7AFF]',
              displayData.iconClass,
            ]"
          ></i>
          <div class="w-full text-center overflow-hidden overflow-ellipsis">
            {{ displayData.content }}
            <div v-if="displayData.channel" class="inline-block h-[15px] my-[2.5px] leading-[15px]! px-[5px] bg-[#1a7aff] rounded-[4px] text-[11px]">
              {{ displayData.channel }}
            </div>
            {{ displayData.userName ?? '' }}
          </div>
          <div
            v-if="row.nodeType === TreeNodeType.Org && displayData.sumDeviceCount > 0"
            class="h-[15px] ml-[5px] leading-[15px]! px-[5px] bg-[#1a7aff] rounded-[4px] text-[11px]"
          >
            {{ displayData.sumDeviceCount }}
          </div>
        </slot>
      </div>
    </el-tooltip>
  </div>
</template>

<style lang="scss" scoped>
  @use '@/assets/bfdxFont/iconfont.css';
  .vxe-column-checkbox {
    * {
      margin: auto 0 auto 5px;
      color: #1a7aff;
      cursor: pointer;
      height: 20px;
      width: 20px;
      font-size: 20px;
    }
  }

  $device_status_color_light_gray: rgb(123, 144, 161, 0.7);
  $device_status_color_gray: #949494;
  $device_status_color_yellow: #ffff1e;
  $device_status_color_green: #2fffa2;
  $device_status_color_red: #ff5833;
  $device_status_color_blue: rgb(26, 122, 255);

  @mixin device_status_base {
    @extend .bfdx-zhuangtaitubiao2;
    color: $device_status_color_gray;
  }

  .device_status {
    &_none {
      @include device_status_base;
      display: none;
    }

    &_yellow {
      @include device_status_base;
      color: $device_status_color_yellow;
    }

    &_light_gray {
      @include device_status_base;
      color: $device_status_color_light_gray;
    }

    &_gray {
      @include device_status_base;
    }

    &_green {
      @include device_status_base;
      color: $device_status_color_green;
    }

    &_red {
      @include device_status_base;
      color: $device_status_color_red;
    }

    &_other_red {
      @include device_status_base;

      &:after {
        content: '';
        display: block;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        position: absolute;
        top: 4px;
        right: 4px;
        z-index: 10;
        background-color: $device_status_color_red;
      }
    }

    &_icon_th {
      @extend .bfdx-tonghuazhong;
      color: $device_status_color_blue;
    }

    &_emergency_green {
      @include device_status_base;
      color: $device_status_color_green;

      animation: _emergency_green 0.5s infinite;
    }
    @keyframes _emergency_green {
      0% {
        color: $device_status_color_green;
      }
      50% {
        color: $device_status_color_red;
      }
      100% {
        color: $device_status_color_green;
      }
    }

    &_emergency_yellow {
      @include device_status_base;

      color: $device_status_color_yellow;
      animation: _emergency_yellow 0.5s infinite;
    }
    @keyframes _emergency_yellow {
      0% {
        color: $device_status_color_yellow;
      }
      50% {
        color: $device_status_color_red;
      }
      100% {
        color: $device_status_color_yellow;
      }
    }
  }
</style>
