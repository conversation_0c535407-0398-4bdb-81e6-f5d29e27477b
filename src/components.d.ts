/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AddressBook: typeof import('./platform/dataManage/deviceManage/common/addressBook.vue')['default']
    AddressBookGroup: typeof import('./platform/dataManage/deviceManage/common/addressBookGroup.vue')['default']
    AdminLayout: typeof import('./layouts/AdminLayout.vue')['default']
    AlarmHistory: typeof import('./platform/dispatch/dataApplication/alarmHistory.vue')['default']
    App: typeof import('./App.vue')['default']
    AudioSwitch: typeof import('./platform/dispatch/CommunicationDispatch/dialog/audioSwitch.vue')['default']
    AuthAppMapPrivilegeDevice: typeof import('./components/command/authAppMapPrivilegeDevice.vue')['default']
    Authorization: typeof import('./platform/dataManage/otherManage/authorization.vue')['default']
    BaseMap: typeof import('./components/common/BaseMap.vue')['default']
    BcxxResult: typeof import('./components/secondary/bcxxResult.vue')['default']
    BfDataNav: typeof import('./layouts/BfDataNav.vue')['default']
    BfDispatchDataNav: typeof import('./layouts/BfDispatchDataNav.vue')['default']
    BfHead: typeof import('./components/layout/BfHead.vue')['default']
    BFHead: typeof import('./layouts/BFHead.vue')['default']
    BfInputNumber: typeof import('./components/common/bfInputNumber/bf-input-number.vue')['default']
    BfLogin: typeof import('./layouts/BfLogin.vue')['default']
    BfMain: typeof import('./components/layout/BfMain.vue')['default']
    BfMap: typeof import('./layouts/BfMap.vue')['default']
    BfMapV2: typeof import('./components/layout/BfMapV2.vue')['default']
    BfSpeaking: typeof import('./platform/dispatch/CommunicationDispatch/dialog/bfSpeaking.vue')['default']
    BfTabsContainer: typeof import('./components/bfTabsContainer.vue')['default']
    BfTree: typeof import('./components/layout/BfTree.vue')['default']
    BP610: typeof import('./platform/dataManage/deviceManage/views/BP610.vue')['default']
    BP620: typeof import('./platform/dataManage/deviceManage/views/BP620.vue')['default']
    BP660: typeof import('./platform/dataManage/deviceManage/views/BP660.vue')['default']
    BP750SDC: typeof import('./platform/dataManage/deviceManage/views/BP750SDC.vue')['default']
    BP750SVT: typeof import('./platform/dataManage/deviceManage/views/BP750SVT.vue')['default']
    BP860SDC: typeof import('./platform/dataManage/deviceManage/views/BP860SDC.vue')['default']
    BP860SVT: typeof import('./platform/dataManage/deviceManage/views/BP860SVT.vue')['default']
    BroadcastFullCall: typeof import('./platform/dispatch/CommunicationDispatch/dialog/broadcastFullCall.vue')['default']
    Channel: typeof import('./platform/dataManage/controllerManage/tr925/Channel.vue')['default']
    Channel_v9: typeof import('./platform/dataManage/deviceManage/common/channel/channel_v9.vue')['default']
    ChannelSettings: typeof import('./components/common/ChannelSettings.vue')['default']
    ChannelTransfer: typeof import('./platform/dataManage/deviceManage/common/channelTransfer.vue')['default']
    CommonContactEdit: typeof import('./platform/dispatch/CommunicationDispatch/dialog/CommonContactEdit.vue')['default']
    CommunicationDispatch: typeof import('./platform/dispatch/CommunicationDispatch.vue')['default']
    ControllerGateway: typeof import('./components/common/controllerGateway.vue')['default']
    Controllers: typeof import('./platform/dataManage/controllerManage/Controllers.vue')['default']
    CrudHistory: typeof import('./platform/common/crudHistory.vue')['default']
    CtrlonlineHistory: typeof import('./platform/dataManage/controllerManage/ctrlonlineHistory.vue')['default']
    CustomImage: typeof import('./components/common/customImage.vue')['default']
    DataApplication: typeof import('./platform/dispatch/DataApplication.vue')['default']
    DataFormEditor: typeof import('./components/common/DataFormEditor.vue')['default']
    DataManageCrudHistory: typeof import('./platform/dataManage/otherManage/dataManageCrudHistory.vue')['default']
    DataManageOnlineHistory: typeof import('./platform/dataManage/deviceManage/dataManageOnlineHistory.vue')['default']
    DataStatusPopover: typeof import('./components/common/dataStatusPopover.vue')['default']
    DataTableElDatePicker: typeof import('./components/common/dataTable/DataTableElDatePicker.vue')['default']
    DataTableElSelect: typeof import('./components/common/dataTable/DataTableElSelect.vue')['default']
    DataTableIcon: typeof import('./components/common/dataTable/DataTableIcon.vue')['default']
    DataTableRow: typeof import('./components/common/dataTable/DataTableRow.vue')['default']
    DataTableRowInput: typeof import('./components/common/dataTable/DataTableRowInput.vue')['default']
    DataTableRowItem: typeof import('./components/common/dataTable/DataTableRowItem.vue')['default']
    DataTablesVue3: typeof import('./components/common/dataTablesVue3.vue')['default']
    DeviceInfo: typeof import('./platform/dataManage/deviceManage/common/deviceInfo.vue')['default']
    Devices: typeof import('./platform/dataManage/deviceManage/Devices.vue')['default']
    DeviceStateTable: typeof import('./components/secondary/deviceStateTable.vue')['default']
    DialogTableTree: typeof import('./components/common/dialogTableTree.vue')['default']
    DigitalAlert: typeof import('./platform/dataManage/deviceManage/common/digitalAlert.vue')['default']
    DispatchCommonContact: typeof import('./platform/dispatch/CommunicationDispatch/common/DispatchCommonContact.vue')['default']
    DispatchContactCard: typeof import('./platform/dispatch/CommunicationDispatch/common/DispatchContactCard.vue')['default']
    DispatchCrudHistory: typeof import('./platform/dispatch/dataApplication/dispatchCrudHistory.vue')['default']
    DispatchDynamicContact: typeof import('./platform/dispatch/CommunicationDispatch/common/DispatchDynamicContact.vue')['default']
    DispatchFunctionList: typeof import('./platform/dispatch/CommunicationDispatch/common/DispatchFunctionList.vue')['default']
    DispatchFunctionListItem: typeof import('./platform/dispatch/CommunicationDispatch/common/DispatchFunctionListItem.vue')['default']
    DispatchHistory: typeof import('./platform/dispatch/dataApplication/dispatchHistory.vue')['default']
    DispatchLayout: typeof import('./layouts/DispatchLayout.vue')['default']
    DispatchOnlineHistory: typeof import('./platform/dispatch/dataApplication/dispatchOnlineHistory.vue')['default']
    DispatchRecentContact: typeof import('./platform/dispatch/CommunicationDispatch/common/DispatchRecentContact.vue')['default']
    DispatchRecentContactCard: typeof import('./platform/dispatch/CommunicationDispatch/common/DispatchRecentContactCard.vue')['default']
    DispatchRecentContactCardIcon: typeof import('./platform/dispatch/CommunicationDispatch/common/DispatchRecentContactCardIcon.vue')['default']
    DispatchTitleIcon: typeof import('./platform/dispatch/CommunicationDispatch/common/DispatchTitleIcon.vue')['default']
    DispatchTree: typeof import('./platform/dispatch/CommunicationDispatch/common/DispatchTree.vue')['default']
    DmridInput: typeof import('./components/common/DmridInput.vue')['default']
    DynamicGroup: typeof import('./platform/dispatch/DynamicGroup.vue')['default']
    DynamicGroupMemberInfo: typeof import('./components/common/DynamicGroupMemberInfo.vue')['default']
    DynamicGroupTree: typeof import('./components/common/dynamicGroupTree/dynamic-group-tree.vue')['default']
    DZ1480: typeof import('./platform/dataManage/controllerManage/modelView/DZ1480.vue')['default']
    DZ1481: typeof import('./platform/dataManage/controllerManage/modelView/DZ1481.vue')['default']
    ElAside: typeof import('element-plus/es')['ElAside']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElButtonGroup: typeof import('element-plus/es')['ElButtonGroup']
    ElCard: typeof import('element-plus/es')['ElCard']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElCol: typeof import('element-plus/es')['ElCol']
    ElColorPicker: typeof import('element-plus/es')['ElColorPicker']
    ElConfigProvider: typeof import('element-plus/es')['ElConfigProvider']
    ElContainer: typeof import('element-plus/es')['ElContainer']
    ElDescriptions: typeof import('element-plus/es')['ElDescriptions']
    ElDescriptionsItem: typeof import('element-plus/es')['ElDescriptionsItem']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElHeader: typeof import('element-plus/es')['ElHeader']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    EllipsisText: typeof import('./components/common/EllipsisText.vue')['default']
    ElMain: typeof import('element-plus/es')['ElMain']
    ElMessageDetailContent: typeof import('./components/common/ElMessageDetailContent.vue')['default']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElOptionGroup: typeof import('element-plus/es')['ElOptionGroup']
    ElPopover: typeof import('element-plus/es')['ElPopover']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRow: typeof import('element-plus/es')['ElRow']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSlider: typeof import('element-plus/es')['ElSlider']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElTimePicker: typeof import('element-plus/es')['ElTimePicker']
    ElTooltip: typeof import('element-plus/es')['ElTooltip']
    ElTree: typeof import('element-plus/es')['ElTree']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    EmergencyAlarmConfig: typeof import('./platform/dataManage/deviceManage/common/emergencyAlarmConfig.vue')['default']
    EmergencyDialog: typeof import('./components/secondary/emergencyDialog.vue')['default']
    EncryptSettings: typeof import('./platform/dataManage/deviceManage/common/encryptSettings.vue')['default']
    Error404: typeof import('./layouts/Error404.vue')['default']
    FreqMapOffset: typeof import('./platform/dataManage/deviceManage/common/freqMapOffset.vue')['default']
    FrequencyMhz: typeof import('./components/common/FrequencyMhz/FrequencyMhz.vue')['default']
    GatewayFilter: typeof import('./platform/dataManage/phoneManage/GatewayFilter.vue')['default']
    GatewayPermission: typeof import('./platform/dataManage/phoneManage/GatewayPermission.vue')['default']
    GenerateDmrId: typeof import('./components/common/generateDmrId/generateDmrId.vue')['default']
    GisApplication: typeof import('./platform/dispatch/GisApplication.vue')['default']
    GpstraceHistory: typeof import('./platform/dispatch/dataApplication/gpstraceHistory.vue')['default']
    HistoryCommon: typeof import('./components/common/historyCommon.vue')['default']
    InspectionHistory: typeof import('./platform/dispatch/dataApplication/patrolHistory/InspectionHistory.vue')['default']
    InsRulesHistory: typeof import('./platform/dispatch/dataApplication/patrolHistory/InsRulesHistory.vue')['default']
    InterphoneWf: typeof import('./platform/dataManage/deviceManage/interphoneWf.vue')['default']
    IOT: typeof import('./platform/dataManage/IOT.vue')['default']
    IotDeviceHistory: typeof import('./platform/dispatch/dataApplication/iotDeviceHistory.vue')['default']
    IpInput: typeof import('./components/common/IpInput/IpInput.vue')['default']
    Jobs: typeof import('./platform/dataManage/Jobs.vue')['default']
    Layout: typeof import('./layouts/Layout.vue')['default']
    LinePoint: typeof import('./platform/dataManage/patrolManage/LinePoint.vue')['default']
    Lines: typeof import('./platform/dataManage/patrolManage/Lines.vue')['default']
    LonLat: typeof import('./components/common/lonLat.vue')['default']
    LowBatteryAlarm: typeof import('./components/secondary/lowBatteryAlarm.vue')['default']
    MapPoints: typeof import('./platform/dataManage/otherManage/MapPoints.vue')['default']
    MenuBtn: typeof import('./components/common/MenuBtn.vue')['default']
    MultistageZone: typeof import('./platform/dataManage/deviceManage/common/multistageZone.vue')['default']
    NetworkSetting: typeof import('./platform/dataManage/controllerManage/common/NetworkSetting.vue')['default']
    Notes: typeof import('./platform/dataManage/otherManage/notes.vue')['default']
    OnlineHistory: typeof import('./platform/common/onlineHistory.vue')['default']
    Orgs: typeof import('./platform/dataManage/Orgs.vue')['default']
    PageHeader: typeof import('./layouts/PageHeader.vue')['default']
    PatrolConfig: typeof import('./platform/dataManage/deviceManage/common/patrolConfig.vue')['default']
    PhoneBook: typeof import('./platform/dataManage/deviceManage/common/phoneBook.vue')['default']
    PocSetting: typeof import('./components/common/PocSetting.vue')['default']
    PredefinedPhoneBook: typeof import('./platform/dataManage/phoneManage/PredefinedPhoneBook.vue')['default']
    PreSetChannel: typeof import('./platform/dataManage/deviceManage/common/preSetChannel.vue')['default']
    ProchatDeviceSelect: typeof import('./components/common/prochatDeviceSelect.vue')['default']
    ProchatGatewaySetting: typeof import('./components/common/prochatGatewaySetting.vue')['default']
    QuickRoutingTags: typeof import('./components/layout/QuickRoutingTags.vue')['default']
    QuickSendCmd: typeof import('./platform/dispatch/gisApplication/quickSendCmd.vue')['default']
    ReceiveGroup: typeof import('./platform/dataManage/deviceManage/common/receiveGroup.vue')['default']
    RecordList: typeof import('./platform/dataManage/deviceManage/common/recordList.vue')['default']
    RecordListV2: typeof import('./platform/dataManage/deviceManage/common/recordListV2.vue')['default']
    RelatedSoftware: typeof import('./platform/dataManage/otherManage/relatedSoftware.vue')['default']
    RepeaterInfo: typeof import('./platform/dataManage/controllerManage/common/RepeaterInfo.vue')['default']
    RepeaterKey: typeof import('./platform/dataManage/controllerManage/common/RepeaterKey.vue')['default']
    RepeaterStatusMonitor: typeof import('./components/common/RepeaterStatusMonitor.vue')['default']
    RepeaterWriteFrequency: typeof import('./platform/dataManage/controllerManage/repeaterWriteFrequency.vue')['default']
    Restart: typeof import('./platform/dataManage/controllerManage/common/Restart.vue')['default']
    RfidBatteryAlarm: typeof import('./platform/dispatch/dataApplication/patrolHistory/rfidBatteryAlarm.vue')['default']
    RoamGroup: typeof import('./platform/dataManage/deviceManage/common/roamGroup.vue')['default']
    RoamGroupList: typeof import('./platform/dataManage/deviceManage/common/roamGroupList.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Rules: typeof import('./platform/dataManage/patrolManage/Rules.vue')['default']
    ScanGroup: typeof import('./platform/dataManage/deviceManage/common/scanGroup.vue')['default']
    ScanGroupList: typeof import('./platform/dataManage/deviceManage/common/scanGroupList.vue')['default']
    ScrollingText: typeof import('./components/common/scrollingText.vue')['default']
    SelectDevice: typeof import('./platform/dataManage/deviceManage/common/selectDevice.vue')['default']
    SelectListenGroup: typeof import('./components/common/selectListenGroup.vue')['default']
    Sendcmd: typeof import('./components/command/sendcmd.vue')['default']
    SendCmd: typeof import('./platform/dispatch/CommunicationDispatch/dialog/sendCmd.vue')['default']
    ServerSetting: typeof import('./platform/dataManage/controllerManage/common/ServerSetting.vue')['default']
    ShiftHistory: typeof import('./platform/dispatch/dataApplication/patrolHistory/shiftHistory.vue')['default']
    ShortMessage: typeof import('./platform/dataManage/deviceManage/common/shortMessage.vue')['default']
    ShortNumberMapping: typeof import('./platform/dataManage/phoneManage/ShortNumberMapping.vue')['default']
    SipGatewaySettings: typeof import('./components/common/sipGatewaySettings/sipGatewaySettings.vue')['default']
    SipServerGatewaySetting: typeof import('./components/common/sipServerGatewaySetting.vue')['default']
    SiteInfo: typeof import('./platform/dataManage/deviceManage/common/SiteInfo.vue')['default']
    SiteInfo_511svt: typeof import('./platform/dataManage/deviceManage/common/SiteInfo_511svt.vue')['default']
    SmsHistory: typeof import('./platform/dispatch/dataApplication/smsHistory.vue')['default']
    SoundHistory: typeof import('./platform/dispatch/dataApplication/soundHistory.vue')['default']
    SwitchChannel: typeof import('./platform/dataManage/controllerManage/common/SwitchChannel.vue')['default']
    SwitchLangs: typeof import('./components/common/switchLangs.vue')['default']
    SwitchTransmitPower: typeof import('./platform/dataManage/controllerManage/common/SwitchTransmitPower.vue')['default']
    SystemLog: typeof import('./platform/dataManage/otherManage/systemLog/systemLog.vue')['default']
    SystemVersion: typeof import('./platform/dataManage/otherManage/systemVersion.vue')['default']
    TableTree: typeof import('./components/common/tableTree/tableTree.vue')['default']
    TD510SDC: typeof import('./platform/dataManage/deviceManage/views/TD510SDC.vue')['default']
    TD510SVT: typeof import('./platform/dataManage/deviceManage/views/TD510SVT.vue')['default']
    TD511SDC: typeof import('./platform/dataManage/deviceManage/views/TD511SDC.vue')['default']
    TD511SDC_FR: typeof import('./platform/dataManage/deviceManage/views/TD511SDC_FR.vue')['default']
    TD511SVT: typeof import('./platform/dataManage/deviceManage/views/TD511SVT.vue')['default']
    TD800SDC: typeof import('./platform/dataManage/deviceManage/views/TD800SDC.vue')['default']
    TD818SDC: typeof import('./platform/dataManage/deviceManage/views/TD818SDC.vue')['default']
    TD818SVT: typeof import('./platform/dataManage/deviceManage/views/TD818SVT.vue')['default']
    TD880SDC: typeof import('./platform/dataManage/deviceManage/views/TD880SDC.vue')['default']
    TD880SVT: typeof import('./platform/dataManage/deviceManage/views/TD880SVT.vue')['default']
    TD910PSDC: typeof import('./platform/dataManage/deviceManage/views/TD910PSDC.vue')['default']
    TD910SDC: typeof import('./platform/dataManage/deviceManage/views/TD910SDC.vue')['default']
    TD920: typeof import('./platform/dataManage/deviceManage/views/TD920.vue')['default']
    TD930SDC: typeof import('./platform/dataManage/deviceManage/views/TD930SDC.vue')['default']
    TD930SDC_R7F: typeof import('./platform/dataManage/deviceManage/views/TD930SDC_R7F.vue')['default']
    TD930SVT: typeof import('./platform/dataManage/deviceManage/views/TD930SVT.vue')['default']
    TD930SVT_R7F: typeof import('./platform/dataManage/deviceManage/views/TD930SVT_R7F.vue')['default']
    Td930Timezone: typeof import('./components/common/timezone/td930-timezone.vue')['default']
    Timezone: typeof import('./components/common/timezone/timezone.vue')['default']
    TimeZoneV2: typeof import('./platform/dataManage/deviceManage/common/TimeZoneV2.vue')['default']
    TM8250SDC: typeof import('./platform/dataManage/deviceManage/views/TM8250SDC.vue')['default']
    TM8250SDC_FR: typeof import('./platform/dataManage/deviceManage/views/TM8250SDC_FR.vue')['default']
    TM8250SDC_R7F: typeof import('./platform/dataManage/deviceManage/views/TM8250SDC_R7F.vue')['default']
    TM8250SVT_R7F: typeof import('./platform/dataManage/deviceManage/views/TM8250SVT_R7F.vue')['default']
    TR092501: typeof import('./platform/dataManage/controllerManage/modelView/TR092501.vue')['default']
    TR805005: typeof import('./platform/dataManage/controllerManage/modelView/TR805005.vue')['default']
    TR805005Channel: typeof import('./platform/dataManage/controllerManage/common/TR805005Channel.vue')['default']
    TR900M: typeof import('./platform/dataManage/controllerManage/modelView/TR900M.vue')['default']
    TR925BdContact: typeof import('./platform/dataManage/controllerManage/common/TR925BdContact.vue')['default']
    TR925BdSetting: typeof import('./platform/dataManage/controllerManage/common/TR925BdSetting.vue')['default']
    TR925Channel: typeof import('./platform/dataManage/controllerManage/common/TR925Channel.vue')['default']
    TR925CommonSetting: typeof import('./platform/dataManage/controllerManage/common/TR925CommonSetting.vue')['default']
    TR925ContactGrouping: typeof import('./platform/dataManage/controllerManage/common/TR925ContactGrouping.vue')['default']
    TR925DmrContact: typeof import('./platform/dataManage/controllerManage/common/TR925DmrContact.vue')['default']
    TR925DmrSetting: typeof import('./platform/dataManage/controllerManage/common/TR925DmrSetting.vue')['default']
    TR925MenuSetting: typeof import('./platform/dataManage/controllerManage/common/TR925MenuSetting.vue')['default']
    TR925PositionSetting: typeof import('./platform/dataManage/controllerManage/common/TR925PositionSetting.vue')['default']
    TR925PreMadeSms: typeof import('./platform/dataManage/controllerManage/common/TR925PreMadeSms.vue')['default']
    TR925ZoneIdSetting: typeof import('./platform/dataManage/controllerManage/common/TR925ZoneIdSetting.vue')['default']
    TraceMap: typeof import('./components/common/traceMap.vue')['default']
    TrackCtrol: typeof import('./components/common/trackCtrol.vue')['default']
    TreeFilterInput: typeof import('./components/common/tableTree/TreeFilterInput.vue')['default']
    TreeToolbar: typeof import('./components/common/tableTree/treeToolbar/treeToolbar.vue')['default']
    Upgrade: typeof import('./layouts/upgrade.vue')['default']
    UserPrivelege: typeof import('./components/UserPrivelege.vue')['default']
    Users: typeof import('./platform/dataManage/Users.vue')['default']
    UserSetting: typeof import('./components/dialogs/userSetting.vue')['default']
    VirtualCluster_930svt_v0: typeof import('./platform/dataManage/deviceManage/common/virtualCluster/virtualCluster_930svt_v0.vue')['default']
    VirtualCluster_v0: typeof import('./platform/dataManage/deviceManage/common/virtualCluster/virtualCluster_v0.vue')['default']
    VxeColumnContent: typeof import('./components/common/tableTree/VxeColumnContent.vue')['default']
    VxeTableTree: typeof import('./components/common/tableTree/VxeTableTree.vue')['default']
    WriteFreqFooter: typeof import('./platform/dataManage/deviceManage/common/writeFreqFooter.vue')['default']
    ZoneChannelTable: typeof import('./platform/dataManage/deviceManage/common/zoneChannelTable.vue')['default']
    ZoneLeafTable: typeof import('./platform/dataManage/deviceManage/common/zoneLeafTable.vue')['default']
  }
}
