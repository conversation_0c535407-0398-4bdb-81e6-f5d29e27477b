<template>
  <div id="bfmap" class="map-wrapper">
    <!--地图缩放级别-->
    <div class="maplibregl-ctrl-mapLevel">
      <span class="mapLevelLabel" v-text="$t('map.mapLevel')" />
      <span class="mapLevel" v-text="mapLevel" />
    </div>
    <!--测距结果显示-->
    <div v-if="distance !== -1" class="distance-container">
      <span class="distanceLabel" v-text="$t('map.DIST')" />
      <span class="distance" v-text="distance" />
      <span>km</span>
    </div>
    <!--显示地图瓦片的x,y,z-->
    <div v-if="tileX > -1 && tileY > -1 && tileZ > -1 && distance == -1" class="distance-container" @dblclick="hideTilexyz">
      <span class="distanceLabel">x:</span>
      <span class="distance" v-text="tileX" />
      <span class="distanceLabel">y:</span>
      <span class="distance" v-text="tileY" />
      <span class="distanceLabel">z:</span>
      <span class="distance" v-text="tileZ" />
    </div>
    <!--地图中心十字架-->
    <div class="map_coordinates" />
    <!--自定义控件按钮-->
    <button ref="satellite" class="satellite_btn" :title="$t('map.satellite')" @click="ctrlIconToggleSapStyle(2)" />
    <button ref="streets" class="streets_btn" :title="$t('map.streets')" @click="ctrlIconToggleSapStyle(1)" />
    <button ref="distances" class="distances_btn" :title="$t('map.distances')" @click="measureDistances" />
    <!--楼层选择功能-->
    <div v-if="showFloorCtrl" class="floor-select-container">
      <el-button v-if="floorSwitch" class="floor-switch-top" icon="arrow-up" :disabled="!floorSwitchTop" @click="clickFloorSwitchTop" />
      <div class="floors-container-wrap">
        <div ref="floorsContainer" class="floors-container">
          <el-button
            v-for="(item, index) in floorsCtrl"
            :key="index"
            class="btn-select-floor"
            :class="{ 'floor-selected': item.value == currentFloor }"
            :style="{ order: item.value }"
            @click="selectFloor(item.value)"
            v-text="item.label"
          />
        </div>
      </div>
      <el-button v-if="floorSwitch" class="floor-switch-bottom" icon="arrow-down" :disabled="!floorSwitchBottom" @click="clickFloorSwitchBottom" />
    </div>
  </div>
</template>

<script>
  import { defaultTreeId, getNodeByKey } from '@/utils/bftree'
  import maplibregl from 'maplibre-gl'
  import 'maplibre-gl/dist/maplibre-gl.css'
  import dbCmd from '@/modules/protocol/db.pb.cmd'
  import bfFloors from '@/utils/3dFloors'
  import bfprocess from '@/utils/bfprocess'
  import bfutil, { MapMarkerClasses, MapMarkerTypes } from '@/utils/bfutil'
  import maputil, { MapLibreProtoColName, MapStyleName, registerCustomProtocol, rpcUrl, updateDeviceMarkerStatus } from '@/utils/map'
  import bfStorage from '@/utils/storage'
  import turf from 'turf'
  import { SupportedLang } from '@/modules/i18n'
  import { BASE_URL } from '@/envConfig'

  // maplibregl.accessToken = 'pk.eyJ1IjoibGluZmwiLCJhIjoiY2l2dTlxeWoyMDVrYjJ6bzFwemR6OWFoMCJ9.pppjfM7dDpXg1KVSaIZUIQ'

  const defaultCenter = [118.62188333333333, 25.003556666666668]

  export default {
    data() {
      return {
        mapStyle: {
          streets: {},
          satellite: {},
        },
        mapLevel: 15,
        distance: -1,
        map_point_zoom_delay_process: undefined,
        saveMapData: true,
        close_mapLaye_btn: null,
        personalMapSettings: {
          center: null,
          style: 'streets',
          zoom: 16,
        },
        all3dLayers: {},
        // 3d室内楼层控制相关设置
        floorsCtrl: [
          // {value: 2, label: "F2"},
          // {value: 1, label: "F1"},
        ],
        currentFloor: 0,
        floorSwitchTop: true,
        floorSwitchBottom: true,
        marginTop: 0,
        floorsShowType: 0,
        tileX: -1,
        tileY: -1,
        tileZ: -1,
        map: null,
      }
    },
    methods: {
      mapResize() {
        bfglob.map.resize()
      },
      setMapStyle() {
        // 获取地图语言类型
        const uiLang2MapLang = (() => {
          try {
            // zh-cn => zh_CN
            if (this.$i18n.locale === SupportedLang.zhCN) return 'zh_CN'

            return this.$i18n.locale
          } catch (e) {
            // no-empty
          }

          return 'zh_CN'
        })()
        const roadmap_tiles = [`${MapLibreProtoColName}://${rpcUrl}/gmap?lang=` + uiLang2MapLang + '&mtype=roadmap&x={x}&y={y}&z={z}']
        const hybrid_tiles = [`${MapLibreProtoColName}://${rpcUrl}/gmap?lang=` + uiLang2MapLang + '&mtype=hybrid&x={x}&y={y}&z={z}']

        // 本地化资源，在开发环境下无法使用代理服务器
        // let glyphs = "mapbox://fonts/mapbox/{fontstack}/{range}.pbf";
        const path = `${window.location.origin}${BASE_URL}localMapbox`
        const glyphs = `${path}/glyphs/{fontstack}/{range}.pbf`
        const sprite = `${path}/sprite/sprite`

        const getMinzoom = () => {
          if (typeof bfglob.mapConfig.tilesMinzoom === 'number' && bfglob.mapConfig.tilesMinzoom >= 0 && bfglob.mapConfig.tilesMinzoom < 18) {
            return bfglob.mapConfig.tilesMinzoom
          }

          return 0
        }
        const getMaxzoom = () => {
          if (typeof bfglob.mapConfig.tilesMaxzoom === 'number' && bfglob.mapConfig.tilesMaxzoom > 0 && bfglob.mapConfig.tilesMaxzoom <= 18) {
            return bfglob.mapConfig.tilesMaxzoom
          }

          return 18
        }

        this.mapStyle = {
          streets: {
            name: MapStyleName.streets,
            version: 8,
            sources: {
              'roadmap-tiles': {
                type: 'raster',
                // point to our third-party tiles. Note that some examples
                // show a "url" property. This only applies to tilesets with
                // corresponding TileJSON (such as mapbox tiles).
                tiles: roadmap_tiles,
                tileSize: 256,
                minzoom: getMinzoom(),
                maxzoom: getMaxzoom(),
              },
            },
            layers: [
              {
                id: 'roadmap-tiles',
                type: 'raster',
                source: 'roadmap-tiles',
              },
            ],
            glyphs: glyphs,
            sprite: sprite,
          },
          satellite: {
            name: MapStyleName.satellite,
            version: 8,
            sources: {
              'hybrid-tiles': {
                type: 'raster',
                // point to our third-party tiles. Note that some examples
                // show a "url" property. This only applies to tilesets with
                // corresponding TileJSON (such as mapbox tiles).
                tiles: hybrid_tiles,
                tileSize: 256,
                minzoom: getMinzoom(),
                maxzoom: getMaxzoom(),
              },
            },
            layers: [
              {
                id: 'hybrid-tiles',
                type: 'raster',
                source: 'hybrid-tiles',
              },
            ],
            glyphs: glyphs,
            sprite: sprite,
          },
        }
      },
      getMapStyle() {
        const styleName = this.personalMapSettings.style || 'streets'
        return this.mapStyle[styleName]
      },
      getLocalCenter() {
        return this.personalMapSettings.center || bfglob.mapConfig.mapCenter || defaultCenter
      },
      getLocalZoom() {
        return this.personalMapSettings.zoom || 16
      },
      bfmap_has_hybrid_style() {
        let has_hybrid = false
        // if ('hybrid-tiles' in bfglob.map.getStyle().sources) {
        if (bfglob.map.getStyle().name === MapStyleName.satellite) {
          has_hybrid = true
        }
        return has_hybrid
      },
      save_currnet_usage_map_style() {
        const centerLngLat = bfglob.map.getCenter()
        const zoomLevel = bfglob.map.getZoom()
        let style = 'streets'
        if (this.bfmap_has_hybrid_style()) {
          style = 'satellite'
        }
        const saveMapData = {
          center: centerLngLat,
          zoom: zoomLevel,
          style: style,
        }
        bfStorage.setItem('bfdx_saveMapData', JSON.stringify(saveMapData))
        this.saveMapData = true
      },
      listen_mapStyle_isloaded() {
        if (bfglob.map.isStyleLoaded()) {
          this.save_currnet_usage_map_style()
          if (maputil.close_mapLaye_btn) {
            maputil.close_mapLaye_btn.remove()
            maputil.close_mapLaye_btn = null
          }

          bfglob.emit('refresh_rooms_layers')
          bfglob.emit('mapStyleReloaded')
        } else {
          setTimeout(this.listen_mapStyle_isloaded, 100)
        }
      },
      switchingMapStyle(stats) {
        bfglob.emit('mapStyleReloadBefore')
        const style = stats === 1 ? this.mapStyle.streets : this.mapStyle.satellite
        bfglob.map.setStyle(style)
        this.listen_mapStyle_isloaded()
      },
      ctrlIconToggleSapStyle(stats) {
        // 判断是否需要切换地图
        // roadmap-tiles 街道图，hybrid-tiles 卫星图
        const current_style = bfglob.map.getStyle()
        const is_roadmap = stats === 1 && 'roadmap-tiles' in current_style.sources
        const is_hybrid = stats === 2 && 'hybrid-tiles' in current_style.sources
        if (is_roadmap || is_hybrid) {
          return
        }
        this.switchingMapStyle(stats)
      },
      // 切换语言后，更新地图图层样式
      reloadCauseLangChanged() {
        if (bfglob.map.isStyleLoaded()) {
          if (this.bfmap_has_hybrid_style()) {
            // 卫星图
            this.switchingMapStyle(2)
          } else {
            // 街道图
            this.switchingMapStyle(1)
          }
        } else {
          setTimeout(this.reloadCauseLangChanged, 100)
        }
      },
      measureDistances() {
        const that = this
        // setting measure state
        if (bfglob.map.measure) {
          return
        }
        bfglob.map.measure = true
        bfglob.map.doubleClickZoom.disable()

        // Add layer source
        const disGeojson = {
          type: 'FeatureCollection',
          features: [],
        }
        const linestring = {
          type: 'Feature',
          geometry: {
            type: 'LineString',
            coordinates: [],
          },
        }
        const addMeasureSource = () => {
          bfglob.map.addSource('disGeojson', {
            type: 'geojson',
            data: disGeojson,
          })
        }
        addMeasureSource()

        // Add styles to the map
        const addMeasureLayers = () => {
          bfglob.map.addLayer({
            id: 'measure-points',
            type: 'circle',
            source: 'disGeojson',
            paint: {
              'circle-radius': 5,
              'circle-color': '#0402ff',
            },
            filter: ['in', '$type', 'Point'],
          })
          bfglob.map.addLayer({
            id: 'measure-lines',
            type: 'line',
            source: 'disGeojson',
            layout: {
              'line-cap': 'round',
              'line-join': 'round',
            },
            paint: {
              'line-color': '#ff0000',
              'line-width': 2.5,
            },
            filter: ['in', '$type', 'LineString'],
          })
        }
        addMeasureLayers()

        // 计算距离
        const calcMeasure = _linestring => {
          const dis_result = turf.lineDistance(_linestring).toLocaleString()
          that.distance = dis_result
        }

        const click_map_create_measure_points = function (e) {
          // 点击地图时，如果点击的是测距点，则返回测距点的数据，features Array
          const features = bfglob.map.queryRenderedFeatures(e.point, {
            layers: ['measure-points'],
          })

          // Remove the linestring from the group
          // So we can redraw it based on the points collection
          if (disGeojson.features.length > 1) {
            disGeojson.features.pop()
          }

          that.distance = -1

          // 如果点击的是测距点，则删除该测距点，否则添加一个测距点数据
          if (features.length) {
            const id = features[0].properties.id
            disGeojson.features = disGeojson.features.filter(function (point) {
              return point.properties.id !== id
            })
          } else {
            const point = {
              type: 'Feature',
              geometry: {
                type: 'Point',
                coordinates: [e.lngLat.lng, e.lngLat.lat],
              },
              properties: {
                id: String(new Date().getTime()),
              },
            }

            disGeojson.features.push(point)
          }

          if (disGeojson.features.length > 1) {
            linestring.geometry.coordinates = disGeojson.features.map(function (point) {
              return point.geometry.coordinates
            })
            disGeojson.features.push(linestring)

            calcMeasure(linestring)
          }

          bfglob.map.getSource('disGeojson').setData(disGeojson)
        }

        const dblclick_end_measure_distance = function (e) {
          bfglob.map.getCanvas().style.cursor = ''
          that.distance = -1

          if (bfglob.map.getLayer('measure-points')) {
            bfglob.map.removeLayer('measure-points')
          }
          if (bfglob.map.getLayer('measure-lines')) {
            bfglob.map.removeLayer('measure-lines')
          }
          if (bfglob.map.getSource('disGeojson')) {
            bfglob.map.removeSource('disGeojson')
          }

          bfglob.map.measure = false
          bfglob.map.doubleClickZoom.enable()

          bfglob.map.off('click', click_map_create_measure_points)
          bfglob.map.off('mousemove', mousemove_find_measure_points)
          bfglob.map.off('dblclick', dblclick_end_measure_distance)
          bfglob.off('mapStyleReloaded', mapStyleReloaded)
        }

        const mousemove_find_measure_points = function (e) {
          try {
            const features = bfglob.map.queryRenderedFeatures(e.point, {
              layers: ['measure-points'],
            })
            // UI indicator for clicking/hovering a point on the map
            bfglob.map.getCanvas().style.cursor = features.length ? 'pointer' : 'crosshair'
          } catch (err) {
            dblclick_end_measure_distance(e)
          }
        }

        bfglob.map.on('click', click_map_create_measure_points)
        bfglob.map.on('mousemove', mousemove_find_measure_points)
        bfglob.map.on('dblclick', dblclick_end_measure_distance)

        // 监听地图样式切换事件，以便重新绘制测距图层和计算距离
        const mapStyleReloaded = () => {
          addMeasureSource()
          addMeasureLayers()
          calcMeasure(linestring)
        }
        bfglob.on('mapStyleReloaded', mapStyleReloaded)
      },
      addCtrlIcon(elm) {
        class CustormCtrlIcon {
          onAdd(map) {
            this._map = map
            this._container = elm
            return this._container
          }

          onRemove() {
            this._container.parentNode.removeChild(this._container)
            this._map = undefined
          }
        }

        return new CustormCtrlIcon()
      },
      loadCustormCtrlIcon() {
        const satellite_btn = this.addCtrlIcon(this.$refs.satellite).onAdd(bfglob.map)
        const streets_btn = this.addCtrlIcon(this.$refs.streets).onAdd(bfglob.map)
        const distances_btn = this.addCtrlIcon(this.$refs.distances).onAdd(bfglob.map)
        $('.maplibregl-ctrl-top-right .maplibregl-ctrl-group').append(satellite_btn, streets_btn, distances_btn)
      },

      // 室内3d楼层控制方法
      selectFloor(floor) {
        // 设置当前激活的楼层
        this.currentFloor = floor
      },
      clickFloorSwitchTop() {
        const box = this.$refs.floorsContainer
        const top = parseInt(getComputedStyle(box).marginTop)
        this.marginTop = top - 26
        if (box.offsetHeight + this.marginTop < 208) {
          this.marginTop = box.offsetTop - 26
        } else {
          box.style.marginTop = this.marginTop + 'px'
        }
      },
      clickFloorSwitchBottom() {
        const box = this.$refs.floorsContainer
        const top = parseInt(getComputedStyle(box).marginTop)
        this.marginTop = top + 26
        if (top >= 0) {
          this.marginTop = 0
        } else {
          box.style.marginTop = this.marginTop + 'px'
        }
      },
      refresh_rooms_layers() {
        this.mapLevel >= 18 && this.load3dRoomLayers(this.currentFloor)
      },
      load3dRoomLayers(val) {
        bfFloors.load3dFloorsLayers()
      },

      // 地图缩放、点击事件
      mapEventSaveMapData() {
        // 5秒后保存地图缩放级别、中心坐标等数据
        const that = this
        if (this.saveMapData) {
          this.saveMapData = false
          setTimeout(function () {
            that.save_currnet_usage_map_style()
          }, 5 * 1000)
        }
      },
      long2tile(lon, zoom) {
        return Math.floor(((lon + 180) / 360) * Math.pow(2, zoom))
      },
      lat2tile(lat, zoom) {
        return Math.floor(((1 - Math.log(Math.tan((lat * Math.PI) / 180) + 1 / Math.cos((lat * Math.PI) / 180)) / Math.PI) / 2) * Math.pow(2, zoom))
      },
      // 计算地图瓦片的x,y,z
      calcMapTile(e) {
        const zoom = Math.floor(bfglob.map.getZoom())
        const x = this.long2tile(e.lngLat.lng, zoom)
        const y = this.lat2tile(e.lngLat.lat, zoom)

        this.tileX = x
        this.tileY = y
        this.tileZ = zoom
      },
      hideTilexyz() {
        this.tileX = -1
        this.tileY = -1
        this.tileZ = -1
      },
      onMapClick(e) {
        // console.log("onMapClick", e);
        !!bfglob.mapConfig.tile && this.calcMapTile(e)
      },
      onMapZoom(e) {
        const zoom = bfglob.map.getZoom()
        // 更新地图级别的显示
        this.mapLevel = zoom.toFixed(2)
        const __monitor_zoom_for_map_point_display = () => {
          this.map_point_zoom_delay_process = undefined

          // 检查巡查点和地图标记点是否显示或隐藏
          const mapPoints = bfglob.gmapPoints.getAll()
          for (const i in mapPoints) {
            const item = mapPoints[i]
            const mapMarker = bfglob.gmapPoints.getMarker(item.rid)
            if (!mapMarker) {
              continue
            }

            // 如果是单位的地图标记点，则要判断设备树的单位节点是否有选中
            // 没有选中的单位，则不显示单位的标记点
            if (bfglob.gorgData.get(item.rid)) {
              const orgNode = getNodeByKey(defaultTreeId, item.rid)
              mapMarker.hide(orgNode?.isSelected() ? item.startShowLevel > this.mapLevel : true)
              continue
            }

            mapMarker.hide(item.startShowLevel > this.mapLevel)
          }
          const linePoints = bfglob.glinePoints.getAll()
          for (const j in linePoints) {
            const __item = linePoints[j]
            const linePointMarker = bfglob.glinePoints.getMarker(__item.rid)
            if (!linePointMarker) {
              continue
            }
            linePointMarker.hide(__item.startShowLevel > this.mapLevel)
          }
          if (bfglob.userInfo.setting.showCtrlMarker) {
            if (this.mapLevel >= 9) {
              $('.ctrlMarker .mapDisplayName').show()
            } else {
              $('.ctrlMarker .mapDisplayName').hide()
            }
            if (this.mapLevel >= 5) {
              $('.ctrlMarker').show()
            } else {
              $('.ctrlMarker').hide()
            }
          }

          this.mapEventSaveMapData()
        }
        if (typeof this.map_point_zoom_delay_process === 'undefined') {
          this.map_point_zoom_delay_process = 1
          setTimeout(__monitor_zoom_for_map_point_display, 300)
        }
      },
      onMapDrag(e) {
        this.mapEventSaveMapData()
      },
      onMapMousedown(e) {
        // console.log("onMapMousedown", e);
      },
      onMapMouseup(e) {
        // console.log("onMapMouseup", e);
      },
      onMapMousemove(e) {
        // console.log("onMapMousemove", e);
      },

      initPersonalMapSettings() {
        const saveMapData = bfStorage.getItem('bfdx_saveMapData')
        if (saveMapData) {
          this.personalMapSettings = JSON.parse(saveMapData)
        }
      },
      getMapConfigMaxZoom() {
        if (typeof bfglob.mapConfig.maxZoom === 'number' && bfglob.mapConfig.maxZoom > 0 && bfglob.mapConfig.maxZoom <= 23) {
          return bfglob.mapConfig.maxZoom
        }
        return bfglob.mapSetting.maxZoom || 23
      },
      getMapConfigMinZoom() {
        if (typeof bfglob.mapConfig.minZoom === 'number' && bfglob.mapConfig.minZoom >= 0 && bfglob.mapConfig.minZoom < 23) {
          return bfglob.mapConfig.minZoom
        }
        return bfglob.mapSetting.minZoom || 1.5
      },
      initBfMap() {
        bfglob.map = this.map = new maplibregl.Map({
          container: 'bfmap',
          style: this.getMapStyle(),
          center: this.getLocalCenter(),
          zoom: this.getLocalZoom(),
          attributionControl: false,
          maxZoom: this.getMapConfigMaxZoom(),
          minZoom: this.getMapConfigMinZoom(),
          transformRequest: (url, resourceType) => {
            // 请求瓦片地图数据时，添加上token
            // headers会覆盖原有请求的headers，暂不可用
            if (resourceType === 'Tile') {
              return {
                url: `${url}&bftk=${bfglob.bftk}&bfsid=${bfglob.sessionId}`,
                // headers: {
                //   bftk: bfglob.bftk,
                //   bfsid: bfglob.sessionId,
                //   'Access-Control-Allow-Origin': '*',
                // },
              }
            }
            return { url }
          },
        })
        this.mapLevel = bfglob.map.getZoom().toFixed(2)
        bfglob.map.addControl(new maplibregl.NavigationControl())
        this.resetMapboxCtrlTitle()
        this.loadCustormCtrlIcon()

        // 监听地图事件程序
        bfglob.map.on('load', this.mapOnload)
      },
      mapOnload() {
        this.mapResize()
        bfglob.on('mapResize', this.mapResize)
        bfglob.on('refresh_rooms_layers', this.refresh_rooms_layers)
        bfglob.map.on('click', this.onMapClick)
        bfglob.map.on('zoom', this.onMapZoom)
        bfglob.map.on('drag', this.onMapDrag)
        // bfglob.map.on('mousedown', this.onMapMousedown);
        // bfglob.map.on('mouseup', this.onMapMouseup);
        // bfglob.map.on('mousemove', this.onMapMousemove);
      },
      markerClickCallback(context) {
        const classList = context.classList
        let type = MapMarkerTypes.Device
        if (classList.contains(MapMarkerClasses.LinePoint)) {
          type = MapMarkerTypes.LinePoint
        } else if (classList.contains(MapMarkerClasses.MapPoint)) {
          type = MapMarkerTypes.MapPoint
        } else if (classList.contains(MapMarkerClasses.Controller)) {
          type = MapMarkerTypes.Controller
        } else if (classList.contains(MapMarkerClasses.IotDevice)) {
          type = MapMarkerTypes.IotDevice
        }

        const key = context.id.substr(1)
        const __data = bfutil.getDataByKey(key, type)
        maputil.updatePopupContent(__data, type)
      },

      // 修复切换地图图层语言后，缩放控件没有切换title属性问题
      resetMapboxCtrlTitle() {
        document.querySelector('.maplibregl-ctrl-zoom-in')?.setAttribute('title', this.$t('map.zoomIn'))
        document.querySelector('.maplibregl-ctrl-zoom-out')?.setAttribute('title', this.$t('map.zoomOut'))
        document.querySelector('.maplibregl-ctrl-compass')?.setAttribute('title', this.$t('map.resetBearingToNorth'))
      },

      getMap() {
        return this.map
      },
    },
    mounted() {
      const that = this
      this.setMapStyle()
      registerCustomProtocol()
      this.initPersonalMapSettings()
      // 等待bfglob.bftk获取后再初始化地图
      if (bfglob.bftk) {
        this.initBfMap()
      } else {
        bfglob.once('bftkReady', () => {
          this.initBfMap()
        })
      }

      // 给地图 marker 添加点击事件监听代理，并更新对应的 marker popup 内容
      $(this.$el).on('click', '.markerContainer', function (e) {
        const context = this
        that.markerClickCallback(context)
      })

      // 订阅用户设置的地图最大、小级别数据
      bfglob.on('resetMapZoom', config => {
        if (!config) {
          config = {
            maxZoom: 18,
            minZoom: 1.5,
          }
        }
        // 如果当前级别大于设置的最大级别，则跳转到最大级别
        var zoom = bfglob.map.getZoom()
        if (zoom > config.maxZoom) {
          bfglob.map.zoomTo(config.maxZoom || 18)
        }
        if (zoom < config.minZoom) {
          bfglob.map.zoomTo(config.minZoom || 1.5)
        }
        // 重置地图的最大、最小缩放级别
        bfglob.map.setMaxZoom(config.maxZoom || 18)
        bfglob.map.setMinZoom(config.minZoom || 1.5)
      })

      // 获取地图级别设置
      bfprocess.getSysLogoAndTitle('mapLevel', dbCmd.DB_SYS_CONFIG_GETBY)
    },
    computed: {
      lang() {
        return this.$i18n.locale
      },
      floorSwitch() {
        return this.floorsCtrl.length > 8
      },
      showFloorCtrl() {
        return this.mapLevel >= 18
      },
    },
    watch: {
      lang(val) {
        this.setMapStyle()
        this.reloadCauseLangChanged()
        this.resetMapboxCtrlTitle()
      },
      marginTop(val) {
        // 计算楼层选择导航两端按钮是否禁用
        this.$nextTick(() => {
          const box = this.$refs.floorsContainer
          if (box.offsetHeight + val <= 208) {
            this.floorSwitchTop = false
          } else {
            this.floorSwitchTop = true
          }
          if (val < 0) {
            this.floorSwitchBottom = true
          } else {
            this.floorSwitchBottom = false
          }
        })
      },
      showFloorCtrl(val) {
        if (val) {
          // 初始化楼层选择导航F1显示出来
          this.currentFloor = 2
          this.marginTop = -26 * 2
          this.$nextTick(() => {
            const el = $('.floor-selected')[0]
            if (el && el.offsetTop >= 208) {
              const $parent = $(el).parent()
              $parent.css({
                marginTop: this.marginTop + 'px',
              })
            }
          })
        }
      },
      currentFloor(val) {
        this.load3dRoomLayers(val)
      },
    },
    beforeMount() {
      bfglob.on('updateDeviceMarker', updateDeviceMarkerStatus)
      bfglob.on('getMap', this.getMap)
    },
    beforeUnmount() {
      bfglob.off('updateDeviceMarker', updateDeviceMarkerStatus)
      bfglob.off('getMap', this.getMap)
    },
    activated() {
      if (bfglob.map) {
        this.$nextTick(() => {
          this.mapResize()
        })
      }
    },
  }
</script>

<style lang="scss">
  @keyframes things-card-emergency-alarm-animate {
    50% {
      color: #21ba45;
    }

    100% {
      color: #c10015;
    }
  }

  @keyframes user-card-emergency-alarm-animate {
    50% {
      background: radial-gradient(#21ba45, transparent);
    }

    100% {
      background: radial-gradient(#c10015, transparent);
    }
  }

  .map-wrapper {
    position: relative;
  }

  .markerContainer {
    $noCommandWarnColor: #e65100;
    $otherAlarmColor: #c10015;
    $normalReportColor: #21ba45;
    $offlineColor: gray(100);

    cursor: pointer;
    text-align: center;
    box-sizing: inherit;

    .map_marker {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translateX(-50%) translateY(-50%);
      border-radius: 50%;
      width: 16px;
      height: 16px;

      &.iconfont {
        transform: unset;

        .mapDisplayName {
          transform: translateX(-50%) translateY(130%);
          left: unset;
        }
      }
    }

    .mapDisplayName {
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%) translateY(100%);
      word-break: keep-all;
      color: #0012ff;
      white-space: nowrap;
      font-size: 13px;
      text-shadow:
        0 0 2px rgba(255, 255, 255, 1),
        -1px 0 2px rgba(255, 255, 255, 1),
        0 -1px 2px rgba(255, 255, 255, 1),
        1px 1px 2px rgba(255, 255, 255, 1);

      .hideMarker {
        top: 50%;
        transform: translateX(-50%) translateY(-50%);
      }
    }

    &.devMarker {
      z-index: 1000;

      .map_marker {
        width: 16px;
        height: 16px;

        .mapDisplayName {
          bottom: unset;
          top: 0;
          transform: translateX(-50%) translateY(-100%);
        }
      }
    }

    &.ctrlMarker,
    &.iotMarker {
      z-index: 600;

      .map_marker {
        height: auto;
      }

      .iconfont {
        color: #0012ff;
        text-shadow:
          0 0 2px rgba(255, 255, 255, 1),
          -1px 0 2px rgba(255, 255, 255, 1),
          0 -1px 2px rgba(255, 255, 255, 1),
          1px 1px 2px rgba(255, 255, 255, 1);
      }
    }

    &.ctrlMarker {
      .iconfont.icon-base-station {
        font-size: 28px;
        overflow: unset;

        &:before {
          position: absolute;
          transform: translateX(-50%) translateY(-65%);
        }
      }
    }

    &.iotMarker {
      .map_marker {
        &.emergency-alarm {
          animation: things-card-emergency-alarm-animate 0.5s ease infinite;
        }

        &.normal-report {
          color: $normalReportColor !important;
        }

        &.other-alarm {
          color: $otherAlarmColor !important;
        }

        &.no-command-warn {
          color: $noCommandWarnColor !important;
        }

        &.offline {
          color: $offlineColor !important;
        }
      }
    }

    &.linePoint {
      z-index: 800;

      .map_marker {
        &:after {
          content: '';
          display: block;
          width: 16px;
          height: 16px;
        }

        &.emergency-alarm:after {
          animation: user-card-emergency-alarm-animate 0.5s ease infinite;
        }

        /*
                正常读卡，不需要显示任何状态
                &.normal-report:after {
                  background: radial-gradient($normalReportColor, transparent);
                }
        */

        &.other-alarm:after {
          background: radial-gradient($otherAlarmColor, transparent);
        }

        &.no-command-warn:after {
          background: radial-gradient($noCommandWarnColor, transparent);
        }
      }
    }

    &.mapPoint {
      z-index: 400;
    }
  }

  .maplibregl-canvas {
    left: 0;
    width: 100% !important;
    height: 100% !important;
  }

  .maplibregl-missing-css {
    display: none !important;
  }

  .maplibregl-popup-content {
    padding: 10px;
    text-align: center;
    word-break: break-word;
    box-sizing: border-box;
  }

  .maplibregl-popup-content p {
    margin: 0;
    padding: 0 0 6px 0;
  }

  .maplibregl-popup-content .popup-body {
    text-align: left;
    width: 100%;
    max-height: 220px;
    overflow: auto;
  }

  .maplibregl-popup {
    z-index: 1000;
    max-width: 480px !important;
  }

  .maplibregl-popup-close-button {
    padding-block: 1px;
    padding-inline: 6px;
  }

  .select_more_linPoint_list {
    cursor: pointer;
    text-decoration: underline;
    color: #ff4949;
  }

  .select_more_linPoint_list:hover {
    color: #f00;
  }

  .satellite_btn {
    background: url(@/images/mapImg/map_satellite.png) no-repeat center center;
  }

  .streets_btn {
    background: url(@/images/mapImg/map_street.png) no-repeat center center;
  }

  .distances_btn {
    background: url(@/images/mapImg/map_rule_24.png) no-repeat center center;
  }

  .maplibregl-ctrl-mapLevel {
    background: #fff;
    border-radius: 4px;
    padding: 2px 4px;
    text-align: center;
    position: absolute;
    bottom: 2px;
    left: 2px;
    z-index: 2000;
  }

  .mapLevel,
  .distance {
    color: #f00;
    font-size: 1.3em;
    font-weight: bold;
  }

  .distance-container {
    position: absolute;
    top: 10px;
    left: 50%;
    z-index: 1;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 1);
    color: #fff;
    font-size: 16px;
    line-height: 18px;
    display: block;
    margin: 0;
    padding: 5px 10px;
    border-radius: 3px;
  }

  .map_coordinates {
    position: absolute;
    top: 50%;
    left: 50%;
    margin-left: -20.5px;
    margin-top: -20.5px;
    z-index: 9999;
  }

  .map_coordinates:before {
    content: '';
    display: table;
    border-bottom: 1px solid #003eff;
    position: absolute;
    top: 0;
    left: 0;
    width: 41px;
    margin-top: 20.5px;
  }

  .map_coordinates:after {
    content: '';
    display: table;
    border-right: 1px solid #003eff;
    position: absolute;
    top: 0;
    left: 0;
    height: 41px;
    margin-left: 20.5px;
  }

  .floor-select-container {
    position: absolute;
    top: 50%;
    left: 5px;
    z-index: 100;
    display: flex;
    flex-direction: column;
    transform: translate(0, -50%);
    background-color: #fff;
  }

  .floor-select-container button {
    border: none !important;
  }

  .floor-switch-top,
  .floor-switch-bottom,
  .floors-container-wrap,
  .btn-select-floor {
    flex: auto;
  }

  .floor-switch-top {
    border-radius: 3px 3px 0 0;
  }

  .floor-switch-bottom {
    border-radius: 0 0 3px 3px;
  }

  .floors-container-wrap {
    max-height: 208px;
    min-height: 26px;
    overflow: hidden;
  }

  .floors-container {
    display: flex;
    flex-direction: column-reverse;
  }

  .floor-switch-top,
  .floor-switch-bottom,
  .floor-select-container button.btn-select-floor {
    padding: 7px;
  }

  .floor-select-container button.btn-select-floor {
    margin-left: 0;
    border-radius: unset;
  }

  .btn-select-floor.floor-selected {
    background-color: #409eff !important;
    border-color: #409eff !important;
    color: #fff !important;
  }

  .maplibregl-ctrl-top-left,
  .maplibregl-ctrl-top-right,
  .maplibregl-ctrl-bottom-left,
  .maplibregl-ctrl-bottom-right {
    z-index: 2000;
  }

  .marker-wrapper {
    display: flex;
    justify-content: center;
    align-content: center;
  }

  .marker-wrapper .map_marker {
    flex: none;
    position: unset;
    transform: unset;
  }

  .marker-wrapper .marker-image {
    width: 100%;
    border-radius: 50%;
    vertical-align: top;
  }

  .marker-wrapper ~ .mapDisplayName {
    top: 16px;
  }

  .marker-wrapper .marker-badge {
    flex: none;
    background-color: #409eff;
    border-radius: 10px;
    color: #fff;
    line-height: 1.2;
    padding: 0 2px;
    text-align: center;
    white-space: nowrap;
    border: 1px solid #fff;
    /*margin-left: 2px;*/
  }

  .popup-active-device-list {
    max-height: 100%;
    overflow: auto;
  }
</style>
