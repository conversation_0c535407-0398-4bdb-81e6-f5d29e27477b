<template>
  <section v-show="mapVisible" class="base-map-container map-wrapper">
    <!--地图缩放级别-->
    <div class="maplibregl-ctrl-mapLevel">
      <span class="mapLevelLabel" v-text="$t('map.mapLevel')" />
      <span class="mapLevel" v-text="mapLevel" />
    </div>

    <!--地图中心十字架-->
    <div class="map_coordinates" />

    <!--自定义控件按钮-->
    <el-tooltip :content="$t('map.zoomIn')" popper-class="bf-tooltip" :show-after="500" placement="left">
      <button ref="zoomIn" class="zoom_in_btn" @click="zoomIn" />
    </el-tooltip>
    <el-tooltip :content="$t('map.zoomOut')" popper-class="bf-tooltip" :show-after="500" placement="left">
      <button ref="zoomOut" class="zoom_out_btn" @click="zoomOut" />
    </el-tooltip>
    <el-tooltip :content="$t('map.resetBearingToNorth')" popper-class="bf-tooltip" :show-after="500" placement="left">
      <button ref="location" class="location_btn" @click="resetBearingToNorth" @mousedown="startCompassDrag" @mouseup="endCompassDrag" />
    </el-tooltip>
    <el-tooltip :content="$t('map.satellite')" popper-class="bf-tooltip" :show-after="500" placement="left">
      <button ref="satellite" class="satellite_btn" @click="toggleSapStyle(MapStyleName.satellite)" />
    </el-tooltip>
    <el-tooltip :content="$t('map.streets')" popper-class="bf-tooltip" :show-after="500" placement="left">
      <button ref="streets" class="streets_btn" @click="toggleSapStyle(MapStyleName.streets)" />
    </el-tooltip>
    <el-tooltip :content="$t('map.stop')" popper-class="bf-tooltip" :show-after="500" placement="left">
      <button ref="quitMap" class="quitMap_btn" @click="close" />
    </el-tooltip>

    <!-- 地图顶部居中的自定义内容 -->
    <div class="maplibregl-custom-top-container">
      <slot name="topCenter" />
    </div>
  </section>
</template>

<script>
  import { MapLibreProtoColName, MapStyleName, registerCustomProtocol, rpcUrl } from '@/utils/map'
  import { SupportedLang } from '@/modules/i18n'
  import bfStorage from '@/utils/storage'
  import maplibregl from 'maplibre-gl'
  import 'maplibre-gl/dist/maplibre-gl.css'
  import { cloneDeep } from 'lodash'
  import { BASE_URL } from '@/envConfig'
  import '@/css/map.scss'

  const defaultCenter = [118.62188333333333, 25.003556666666668]

  export default {
    name: 'BaseMap',
    emits: ['update:visible', 'close', 'init'],
    props: {
      /**
       * 控件列表
       * @example [{position: 'top-right', control: any}]
       */
      controls: {
        type: Array,
      },
      mapId: {
        type: String,
        default: 'base-map-' + Date.now(),
      },
      visible: {
        type: Boolean,
        default: false,
      },
    },
    data() {
      return {
        map: null, // 保存当前地图实例
        mapLevel: 15,
        // mapStyle: {}, // 不需要vue响应式,不显示声明
        personalMapSettings: {
          center: null,
          style: 'streets',
          zoom: 16,
        },
        // 指南针拖动相关状态
        isDraggingCompass: false,
        dragStartX: 0,
        dragStartY: 0,
        initialBearing: 0,
      }
    },
    methods: {
      close() {
        this.$emit('close')
        // 主要用于向控件传递地图窗口关闭信号
        bfglob.emit('map-close', this.mapId)
      },

      // 地图缩放控制
      zoomIn() {
        if (this.map) {
          this.map.zoomIn()
        }
      },
      zoomOut() {
        if (this.map) {
          this.map.zoomOut()
        }
      },

      // 重置地图方向到北方
      resetBearingToNorth() {
        if (this.map) {
          this.map.setBearing(0)
        }
      },

      // 指南针拖动相关方法
      startCompassDrag(event) {
        this.isDraggingCompass = true
        this.dragStartX = event.clientX
        this.dragStartY = event.clientY
        this.initialBearing = this.map.getBearing()

        // 添加全局事件监听器
        document.addEventListener('mousemove', this.onCompassDrag)
        document.addEventListener('mouseup', this.endCompassDrag)

        event.preventDefault()
      },

      onCompassDrag(event) {
        if (!this.isDraggingCompass) return

        const deltaX = event.clientX - this.dragStartX
        // const deltaY = event.clientY - this.dragStartY

        // 根据水平拖动距离计算新的方向
        const sensitivity = 0.5 // 调整灵敏度
        const newBearing = this.initialBearing + deltaX * sensitivity

        if (this.map) {
          this.map.setBearing(newBearing)
        }
      },

      endCompassDrag() {
        this.isDraggingCompass = false

        // 移除全局事件监听器
        document.removeEventListener('mousemove', this.onCompassDrag)
        document.removeEventListener('mouseup', this.endCompassDrag)
      },
      toggleSapStyle(styleName) {
        // 判断是否需要切换地图
        // roadmap-tiles 街道图，hybrid-tiles 卫星图
        const currentStyle = this.map.getStyle()
        if (currentStyle.name === styleName) return

        const newStyles = cloneDeep(this.mapStyle[styleName] ?? this.mapStyle[MapStyleName.satellite])

        // 拷贝原有的图层
        const sources = currentStyle.sources
        const layers = currentStyle.layers
        for (const layer of layers) {
          if (layer.type === 'raster') {
            continue
          }
          // 跳过没有源的图层
          const sourceId = layer.source
          const source = sources[sourceId]
          if (!source) {
            continue
          }

          newStyles.layers.push(layer)
          newStyles.sources[sourceId] = source
        }

        this.map.setStyle(newStyles)
      },

      // 修复切换地图图层语言后，缩放控件没有切换title属性问题
      resetMapboxCtrlTitle() {
        this.$el?.querySelector('.maplibregl-ctrl-zoom-in')?.setAttribute('title', this.$t('map.zoomIn'))
        this.$el?.querySelector('.maplibregl-ctrl-zoom-out')?.setAttribute('title', this.$t('map.zoomOut'))
        this.$el?.querySelector('.maplibregl-ctrl-compass')?.setAttribute('title', this.$t('map.resetBearingToNorth'))
      },
      loadCustomCtrlIcon() {
        // 创建自定义控件容器
        const customControlContainer = document.createElement('div')
        customControlContainer.className = 'maplibregl-ctrl maplibregl-ctrl-group custom-map-controls'

        // 添加所有按钮到容器中
        customControlContainer.appendChild(this.$refs.zoomIn)
        customControlContainer.appendChild(this.$refs.zoomOut)
        customControlContainer.appendChild(this.$refs.location)
        customControlContainer.appendChild(this.$refs.satellite)
        customControlContainer.appendChild(this.$refs.streets)
        customControlContainer.appendChild(this.$refs.quitMap)

        // 创建自定义控件类
        class CustomMapControls {
          onAdd(map) {
            this._map = map
            this._container = customControlContainer
            return this._container
          }

          onRemove() {
            this._container.parentNode.removeChild(this._container)
            this._map = undefined
          }
        }

        // 添加自定义控件到地图
        this.map.addControl(new CustomMapControls(), 'top-right')
      },

      // 初始化地图逻辑
      setMapStyle() {
        // 获取地图语言类型
        const uiLang2MapLang = (() => {
          try {
            // 检查 $i18n 是否存在
            if (!this.$i18n || !this.$i18n.locale) {
              return 'zh_CN' // 只有在$i18n不存在时才默认中文
            }

            // 兼容Vue 3中locale可能是ref对象或字符串的情况
            let currentLocale = this.$i18n.locale
            if (typeof currentLocale === 'object' && currentLocale.value !== undefined) {
              currentLocale = currentLocale.value
            }

            // zh-cn => zh_CN (只转换中文的格式)
            if (currentLocale === SupportedLang.zhCN) {
              return 'zh_CN'
            }

            // 其他语言直接返回原值
            return currentLocale
          } catch (_e) {
            // 发生异常时返回默认值，但这里应该尽量保持原有语言
            // 如果能获取到locale就用locale，否则才用默认中文
            try {
              if (this.$i18n && this.$i18n.locale) {
                let locale = this.$i18n.locale
                if (typeof locale === 'object' && locale.value !== undefined) {
                  locale = locale.value
                }
                return locale === SupportedLang.zhCN ? 'zh_CN' : locale
              }
            } catch (_e2) {
              // 完全无法获取时才返回中文
            }
            return 'zh_CN'
          }
        })()
        const roadmap_tiles = [`${MapLibreProtoColName}://${rpcUrl}/gmap?lang=` + uiLang2MapLang + '&mtype=roadmap&x={x}&y={y}&z={z}']
        const hybrid_tiles = [`${MapLibreProtoColName}://${rpcUrl}/gmap?lang=` + uiLang2MapLang + '&mtype=hybrid&x={x}&y={y}&z={z}']

        // 本地化资源，在开发环境下无法使用代理服务器
        // let glyphs = "mapbox://fonts/mapbox/{fontstack}/{range}.pbf";
        const path = `${window.location.origin}${BASE_URL}localMapbox`
        const glyphs = `${path}/glyphs/{fontstack}/{range}.pbf`
        const sprite = `${path}/sprite/sprite`

        const getMinzoom = () => {
          if (typeof bfglob.mapConfig.tilesMinzoom === 'number' && bfglob.mapConfig.tilesMinzoom >= 0 && bfglob.mapConfig.tilesMinzoom < 18) {
            return bfglob.mapConfig.tilesMinzoom
          }

          return 0
        }
        const getMaxzoom = () => {
          if (typeof bfglob.mapConfig.tilesMaxzoom === 'number' && bfglob.mapConfig.tilesMaxzoom > 0 && bfglob.mapConfig.tilesMaxzoom <= 18) {
            return bfglob.mapConfig.tilesMaxzoom
          }

          return 18
        }

        this.mapStyle = {
          [MapStyleName.streets]: {
            name: MapStyleName.streets,
            version: 8,
            sources: {
              'roadmap-tiles': {
                type: 'raster',
                // point to our third-party tiles. Note that some examples
                // show a "url" property. This only applies to tilesets with
                // corresponding TileJSON (such as mapbox tiles).
                tiles: roadmap_tiles,
                tileSize: 256,
                minzoom: getMinzoom(),
                maxzoom: getMaxzoom(),
              },
            },
            layers: [
              {
                id: 'roadmap-tiles',
                type: 'raster',
                source: 'roadmap-tiles',
              },
            ],
            glyphs: glyphs,
            sprite: sprite,
          },
          [MapStyleName.satellite]: {
            name: MapStyleName.satellite,
            version: 8,
            sources: {
              'hybrid-tiles': {
                type: 'raster',
                // point to our third-party tiles. Note that some examples
                // show a "url" property. This only applies to tilesets with
                // corresponding TileJSON (such as mapbox tiles).
                tiles: hybrid_tiles,
                tileSize: 256,
                minzoom: getMinzoom(),
                maxzoom: getMaxzoom(),
              },
            },
            layers: [
              {
                id: 'hybrid-tiles',
                type: 'raster',
                source: 'hybrid-tiles',
              },
            ],
            glyphs: glyphs,
            sprite: sprite,
          },
        }
      },
      getMapStyle() {
        const styleName = this.personalMapSettings.style || MapStyleName.streets
        return this.mapStyle[styleName]
      },
      getLocalCenter() {
        return this.personalMapSettings.center || bfglob.mapConfig.mapCenter || defaultCenter
      },
      getLocalZoom() {
        return this.personalMapSettings.zoom || 16
      },
      getMapConfigMaxZoom() {
        if (typeof bfglob.mapConfig.maxZoom === 'number' && bfglob.mapConfig.maxZoom > 0 && bfglob.mapConfig.maxZoom <= 23) {
          return bfglob.mapConfig.maxZoom
        }
        return bfglob.mapSetting.maxZoom || 23
      },
      getMapConfigMinZoom() {
        if (typeof bfglob.mapConfig.minZoom === 'number' && bfglob.mapConfig.minZoom >= 0 && bfglob.mapConfig.minZoom < 23) {
          return bfglob.mapConfig.minZoom
        }
        return bfglob.mapSetting.minZoom || 1.5
      },
      initPersonalMapSettings() {
        const saveMapData = bfStorage.getItem('bfdx_saveMapData')
        if (saveMapData) {
          this.personalMapSettings = JSON.parse(saveMapData)
        }
      },
      onMapZoom() {
        const zoom = this.map.getZoom()
        // 更新地图级别的显示
        this.mapLevel = zoom.toFixed(2)
      },
      mapOnload() {
        this.map.on('zoom', this.onMapZoom)
      },
      initBaseMap() {
        this.map = new maplibregl.Map({
          container: this.$el,
          style: this.getMapStyle(),
          center: this.getLocalCenter(),
          zoom: this.getLocalZoom(),
          attributionControl: false,
          maxZoom: this.getMapConfigMaxZoom(),
          minZoom: this.getMapConfigMinZoom(),
          transformRequest: (url, resourceType) => {
            // 请求瓦片地图数据时，添加上token
            // headers会覆盖原有请求的headers，暂不可用
            if (resourceType === 'Tile') {
              return {
                url: `${url}&bftk=${bfglob.bftk}&bfsid=${bfglob.sessionId}`,
              }
            }
            return { url }
          },
        })
        this.map.mapId = this.mapId
        this.$emit('init', this.map)

        this.mapLevel = this.map.getZoom().toFixed(2)
        // 不添加默认的导航控件，使用自定义按钮
        this.resetMapboxCtrlTitle()
        this.loadCustomCtrlIcon()
        this.controls?.forEach(item => {
          this.map.addControl(item.control, item.position || 'top-right')
        })

        // 监听地图事件程序
        this.map.on('load', this.mapOnload)
      },
      // 绘制设备标记点和经纬度范围
      // drawPointAndRange(lonlatObj) {
      //
      // }
    },
    computed: {
      MapStyleName() {
        return MapStyleName
      },
      mapVisible: {
        get() {
          return this.visible
        },
        set(val) {
          this.$emit('update:visible', val)
        },
      },
    },
    watch: {
      visible(newVal) {
        if (!newVal || !this.map) {
          return
        }
        this.$nextTick(() => {
          this.map.resize()
        })
      },
    },
    beforeMount() {
      this.initPersonalMapSettings()
    },
    mounted() {
      this.setMapStyle()
      registerCustomProtocol()
      this.initBaseMap()
    },
    beforeUnmount() {
      // 清理指南针拖动事件监听器
      document.removeEventListener('mousemove', this.onCompassDrag)
      document.removeEventListener('mouseup', this.endCompassDrag)
    },
  }
</script>

<style scoped lang="scss">
  .base-map-container {
    width: 100%;
    height: 100%;

    /* 按钮样式已在 map.scss 中定义，这里只定义 quitMap_btn */
    .quitMap_btn {
      background: url(@/images/mapImg/map_close.png) no-repeat center center;
      background-size: 42px 42px;
    }

    .quitMap_btn:hover {
      background-color: #f8f8f8;
    }

    .maplibregl-custom-top-container {
      position: absolute;
      z-index: 100;
      top: 12px;
      left: 50%;
      transform: translateX(-50%);
      background-color: rgba(0, 0, 0, 0.8);
      color: #fff;
      padding: 6px 10px;
      border-radius: 6px;
      font-size: 1rem;
      max-width: 80%;
      word-break: break-word;

      & > * {
        line-height: 1.5;
      }
    }
  }
</style>
