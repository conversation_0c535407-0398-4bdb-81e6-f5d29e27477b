<template>
  <div class="dispatch-tree-wrapper">
    <VxeTableTree
      ref="tableTree"
      :withPageHeader="true"
      :filter="true"
      :enable-checkbox="props.enableCheckbox"
      :menuConfig="menuConfig"
      @menu-click="menuEventHandler"
      @cell-dblclick.prevent="cellDbClickHandler"
      @checkbox-change="checkboxChange"
    />
  </div>
</template>

<script lang="ts" setup>
  import { computed, useTemplateRef } from 'vue'
  import { useI18n } from 'vue-i18n'
  import { DeviceTypes, getDeviceMapLonLat } from '@/utils/bfutil'
  import { MenuConfig, MenuEventHandler, TreeNodeData, TreeNodeType } from '@/components/common/tableTree'
  import openDialog from '@/utils/dialog'
  import BfSpeaking from '@/platform/dispatch/CommunicationDispatch/dialog/bfSpeaking.vue'
  import { VxeTableEvents } from 'vxe-table'
  import { mapFlyTo } from '@/utils/map'
  import { setSpeakTarget } from '@/utils/speak'
  import { useRoute } from 'vue-router'

  const route = useRoute()

  import { useCommonContact } from '@/utils/callContact/commonContact'
  import {
    deleteDbGroupCallContactByOrgId2db,
    deleteDbSingleCallContactByDeviceId2db,
    insertDbGroupCallContact2db,
    insertDbSingleCallContact2db,
  } from '@/utils/callContact'
  import bfNotify from '@/utils/notify'

  // 支持查看设备状态的终端类型
  const SupportStatusDeviceTypes = [
    DeviceTypes.Device,
    DeviceTypes.Mobile,
    DeviceTypes.VirtualClusterDevice,
    DeviceTypes.PocDevice,
    DeviceTypes.VirtualRepeater,
  ]

  const props = withDefaults(
    defineProps<{
      enableCheckbox?: boolean
    }>(),
    {
      enableCheckbox: true,
    }
  )

  const statusContextMenuCode = ['stats', 'cb01', 'cb02', 'cb09']

  const { t } = useI18n()

  const tableTreeRef = useTemplateRef('tableTree')

  const { commonContacts } = useCommonContact()

  const menuConfig = computed<MenuConfig>(() => {
    return {
      body: {
        options: [
          [
            {
              name: t('tree.quickCall'),
              code: 'quickCall',
            },
            {
              disabled: false,
              visible: true,
              name: t('tree.addToCommonContact'),
              code: 'addToCommonContact',
            },
            {
              disabled: true,
              visible: false,
              name: t('tree.removeFromCommonContact'),
              code: 'removeFromCommonContact',
            },
            {
              disabled: true,
              visible: false,
              name: t('dialog.locateCtrl'),
              code: 'cb01',
            },
            {
              disabled: true,
              visible: false,
              name: t('dialog.trailCtrl'),
              code: 'cb02',
            },
            {
              disabled: true,
              visible: false,
              name: t('dialog.telecontrol'),
              code: 'cb09',
            },
            {
              disabled: true,
              visible: false,
              name: t('tree.status'),
              code: 'stats',
            },
          ],
          [
            {
              name: t('tree.collapseAll'),
              code: 'collapseAll',
            },
            {
              name: t('tree.expandAll'),
              code: 'expandAll',
            },
            {
              name: t('tree.online'),
              code: 'displayOnline',
            },
            {
              name: t('tree.displayAllDev'),
              code: 'displayAll',
            },
          ],
        ],
      },
      visibleMethod: ({ options, row }) => {
        if (!row) return true
        let hasStatus = false

        if (row.nodeType === TreeNodeType.Terminal) {
          const device = bfglob.gdevices.get(row.rid)
          // 只有指定的设备才显示状态
          if (device && SupportStatusDeviceTypes.includes(device.deviceType)) {
            hasStatus = true
          }
        }

        const visible = row.nodeType === TreeNodeType.Org ? false : hasStatus
        const disabled = !visible

        options?.forEach(list => {
          list.forEach(item => {
            if (statusContextMenuCode.includes(item.code)) {
              item.visible = visible
              item.disabled = disabled
            }

            if (item.code === 'addToCommonContact') {
              const isInCommonContact = commonContacts.value.some(contact => contact.targetRid === row.rid)
              item.visible = !isInCommonContact
              item.disabled = isInCommonContact
            }

            if (item.code === 'removeFromCommonContact') {
              const isInCommonContact = commonContacts.value.some(contact => contact.targetRid === row.rid)
              item.visible = isInCommonContact
              item.disabled = !isInCommonContact
            }
          })
        })

        return true
      },
    } satisfies MenuConfig
  })

  const menuEventHandler: MenuEventHandler = ({ menu, row }) => {
    // todo: handle click
    switch (menu.code) {
      case 'quickCall':
        // 检查是否已经打开了 BfSpeaking 对话框
        if (bfglob.vspeaking && bfglob.vspeaking.visible) {
          // 如果已经打开，直接设置目标并快速呼叫
          let dmrId = ''
          if (row.nodeType === TreeNodeType.Terminal) {
            const data = bfglob.gdevices.get(row.rid)
            dmrId = data?.dmrId ?? ''
          } else {
            const data = bfglob.gorgData.get(row.rid)
            dmrId = data?.dmrId ?? ''
          }
          setSpeakTarget(row.rid)
          bfglob.vspeaking.speakFast(dmrId)
        } else {
          // 如果没有打开，则打开对话框
          let dmrId = ''
          if (row.nodeType === TreeNodeType.Terminal) {
            const data = bfglob.gdevices.get(row.rid)
            dmrId = data?.dmrId ?? ''
          } else {
            const data = bfglob.gorgData.get(row.rid)
            dmrId = data?.dmrId ?? ''
          }
          openDialog(BfSpeaking).then(vm => {
            setSpeakTarget(row.rid)
            ;(vm.value as InstanceType<typeof BfSpeaking>).speakFast(dmrId)
          })
        }
        break
      case 'addToCommonContact':
        handleAddToCommonContact(row)
        break
      case 'removeFromCommonContact':
        handleRemoveFromCommonContact(row)
        break
      case 'cb01':
        handleLocationMonitor(row)
        break
      case 'cb02':
        handleTrackMonitor(row)
        break
      case 'cb09':
        handleTelecontrol(row)
        break
      case 'stats':
        handleStatusCheck(row)
        break
      case 'collapseAll':
        tableTreeRef.value?.collapseAll()
        tableTreeRef.value
        break
      case 'expandAll':
        tableTreeRef.value?.expandAll()
        break
      case 'displayOnline':
        tableTreeRef.value?.displayOnline()
        break
      case 'displayAll':
        tableTreeRef.value?.displayAll()
        break
    }
  }

  function handleAddToCommonContact(row: TreeNodeData) {
    if (row.nodeType === TreeNodeType.Org) {
      insertDbGroupCallContact2db(row.rid, 0)
        .then(() => {
          bfNotify.messageBox(t('msgbox.addSuccess'), 'success')
        })
        .catch(() => {
          bfNotify.messageBox(t('msgbox.addError'), 'error')
        })
    } else if (row.nodeType === TreeNodeType.Terminal) {
      insertDbSingleCallContact2db(row.rid, 0)
        .then(() => {
          bfNotify.messageBox(t('msgbox.addSuccess'), 'success')
        })
        .catch(() => {
          bfNotify.messageBox(t('msgbox.addError'), 'error')
        })
    }
  }

  function handleRemoveFromCommonContact(row: TreeNodeData) {
    if (row.nodeType === TreeNodeType.Org) {
      deleteDbGroupCallContactByOrgId2db(row.rid)
        .then(() => {
          bfNotify.messageBox(t('msgbox.delSuccess'), 'success')
        })
        .catch(() => {
          bfNotify.messageBox(t('msgbox.delError'), 'error')
        })
    } else if (row.nodeType === TreeNodeType.Terminal) {
      deleteDbSingleCallContactByDeviceId2db(row.rid)
        .then(() => {
          bfNotify.messageBox(t('msgbox.delSuccess'), 'success')
        })
        .catch(() => {
          bfNotify.messageBox(t('msgbox.delError'), 'error')
        })
    }
  }

  function handleLocationMonitor(row: TreeNodeData) {
    console.log('定位监控:', row.rid)
    // 打开定位监控对话框
    import('@/platform/dispatch/gisApplication/quickSendCmd.vue').then(DeviceControlComponent => {
      openDialog(DeviceControlComponent.default, { rid: row.rid, cmdType: 'cb01' })
    })
  }

  function handleTrackMonitor(row: TreeNodeData) {
    console.log('跟踪监控:', row.rid)
    // 打开跟踪监控对话框
    import('@/platform/dispatch/gisApplication/quickSendCmd.vue').then(DeviceControlComponent => {
      openDialog(DeviceControlComponent.default, { rid: row.rid, cmdType: 'cb02' })
    })
  }

  function handleTelecontrol(row: TreeNodeData) {
    console.log('遥开遥毙:', row.rid)
    // 打开遥开遥毙对话框
    import('@/platform/dispatch/gisApplication/quickSendCmd.vue').then(DeviceControlComponent => {
      openDialog(DeviceControlComponent.default, { rid: row.rid, cmdType: 'cb09' })
    })
  }

  function handleStatusCheck(row: TreeNodeData) {
    // 通过 rid 获取设备数据
    const device = bfglob.gdevices.get(row.rid)
    if (!device) {
      bfglob.console.error('未找到设备:', row.rid)
      return
    }

    // 触发创建设备状态表事件
    bfglob.emit('create_device_status_table', device)
  }

  const cellDbClickHandler: VxeTableEvents.CellDblclick<TreeNodeData> = ({ row }) => {
    // 处理单元格双击事件
    if (row.nodeType === TreeNodeType.Org) {
      const org = bfglob.gorgData.get(row.rid)
      if (!org) {
        return
      }
      // use relative org marker lonlat
      const lonlatInfo = bfglob.gorgData.getOrgMapMakerPointLonlatInfo(org.rid)
      if (!lonlatInfo.lonlat) {
        return
      }
      mapFlyTo(lonlatInfo.lonlat, lonlatInfo.showLevel)
    } else {
      const device = bfglob.gdevices.get(row.rid)
      if (!device) {
        return
      }
      mapFlyTo(getDeviceMapLonLat(device))
    }
  }

  const getDeviceRidsFormOrgRid = (row: TreeNodeData): string[] => {
    const deviceRids: string[] = []
    deviceRids.push(row.rid)
    for (let i = 0; i < row.children.length; i++) {
      const child = row.children[i]
      if (child.nodeType === TreeNodeType.Terminal) {
        deviceRids.push(child.rid)
      } else {
        deviceRids.push(...getDeviceRidsFormOrgRid(child))
      }
    }
    return deviceRids
  }

  // checked 为勾选前状态
  const checkboxChange = (row: TreeNodeData, checked: boolean) => {
    if (route.name !== 'GisApplication') {
      return
    }
    if (row.nodeType === TreeNodeType.Terminal) {
      bfglob.emit('visible-map-device-point', [row.rid], !checked)
    } else {
      const rids = getDeviceRidsFormOrgRid(row)
      bfglob.emit('visible-map-device-point', rids, !checked)
    }
  }
</script>

<style lang="scss">
  .dispatch-tree-wrapper {
    height: 100%;
    width: 100%;
  }
</style>
