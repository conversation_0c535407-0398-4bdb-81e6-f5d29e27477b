<template>
  <el-form ref="bdSetting" :model="dmrSetting" label-position="top" class="dmr-setting" :rules="dmrSettingRules">
    <el-row :gutter="20" class="no-margin-x">
      <el-col :xs="24">
        <DmridInput v-model="dmrSetting.dmrid" :minNo="1" isDec />
      </el-col>
      <el-col :xs="24" :sm="12">
        <el-form-item :label="$t('dialog.repeaterId')" prop="repeatorId">
          <el-input-number v-model="dmrSetting.repeatorId" :min="1" :max="16777215" />
        </el-form-item>
      </el-col>
      <el-col :xs="24" :sm="12">
        <el-form-item :label="$t('dialog.priority')" prop="priorityLevel">
          <el-select v-model="dmrSetting.priorityLevel" :placeholder="$t('dialog.select')" filterable :no-match-text="$t('dialog.noMatchText')">
            <el-option v-for="(item, i) in priorityLevelList" :key="i" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-col>
      <el-col :xs="24" :sm="12">
        <el-form-item :label="$t('dialog.repeaterPluginHangTime')" prop="rciHoldTime">
          <el-input-number v-model="dmrSetting.rciHoldTime" :min="1" :max="30" :step="1" />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>
  import { DefModel } from '@/writingFrequency/repeater'
  import bfutil from '@/utils/bfutil'
  import { defineAsyncComponent } from 'vue'

  export default {
    name: 'TR925DmrSetting',
    props: {
      repeaterData: {
        type: [Object, undefined],
      },
      repeater: {
        type: String,
        default: '',
      },
      getRepeaterId: {
        type: Function,
        default: bfutil.noop,
      },
      disabled: {
        type: Boolean,
        default: false,
      },
      packageName: {
        type: String,
        default: '',
      },
      deviceModel: {
        type: String,
        default: DefModel,
      },
      saveMethod: {
        type: Function,
        default: bfutil.noop,
      },
    },
    data() {
      return {
        dmrSetting: {
          dmrid: 1,
          repeatorId: 1,
          priorityLevel: 0,
          rciHoldTime: 15,
        },
      }
    },
    computed: {
      dmrSettingRules() {
        return {}
      },
      priorityLevelList() {
        return [
          { label: this.$t('dialog.nothing'), value: 0 },
          { label: this.$t('dialog.low'), value: 1 },
          { label: this.$t('dialog.mid'), value: 2 },
          { label: this.$t('dialog.high'), value: 3 },
        ]
      },
    },
    components: {
      DmridInput: defineAsyncComponent(() => import('@/components/common/DmridInput.vue')),
    },
  }
</script>

<style>
  .dmr-setting .generate-dmrId-wrap .dmrId-number-wrap {
    padding-left: 0 !important;
  }

  .dmr-setting .generate-dmrId-wrap .dmrId-label-wrap {
    padding-right: 0 !important;
  }
</style>
