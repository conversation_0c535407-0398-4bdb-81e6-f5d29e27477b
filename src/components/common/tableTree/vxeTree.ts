import { watch } from 'vue'
import { VxeTableInstance } from 'vxe-table/types/all'
import { cloneDeep } from 'lodash'
import { HasPrivilegeDataLoaded } from '@/utils/bfprocess'
import { TreeNodeData, TreeNodeType } from './types'
import { updateTreeOrgDeviceCount } from './treeUtil'
import i18n from '@/modules/i18n'

// 自定义 fancytree 节点排序比较方法
export const fullCallDmrIdStr: string = bfglob.fullCallDmrId.toString()

export function getFullCallData() {
  return {
    dmrId: bfglob.fullCallDmrId.toString() as string,
    rid: bfglob.fullCallDmrId.toString() as string,
    parentOrgId: '00000000-0000-0000-0000-000000000000',
    orgShortName: i18n.global.t('dialog.fullCall'),
  }
}

export function defaultVxeTableTreeSortCompareFn(a: TreeNodeData, b: TreeNodeData): number {
  // 全呼目标不参与排序
  if (a.rid === fullCallDmrIdStr || b.rid === fullCallDmrIdStr) {
    return 0
  }

  const aData = a.nodeType == TreeNodeType.Org ? bfglob.gorgData.get(a.rid) : bfglob.gdevices.get(a.rid)
  const bData = b.nodeType == TreeNodeType.Org ? bfglob.gorgData.get(b.rid) : bfglob.gdevices.get(b.rid)

  const sortType = bfglob.userInfo.setting.fancytreeSortType

  // 将单位节点排在设备节点后面,sortType 为 false 则将单位和设备排序反转
  if (a.nodeType === TreeNodeType.Org && b.nodeType !== TreeNodeType.Org) {
    return sortType ? 1 : -1
  }
  if (a.nodeType !== TreeNodeType.Org && b.nodeType === TreeNodeType.Org) {
    return sortType ? -1 : 1
  }

  // 单位节点排序，必须按排序值排序
  if (a.nodeType === TreeNodeType.Org && b.nodeType === TreeNodeType.Org) {
    return aData.sortString - bData.sortString
  }

  return aData.dmrId.localeCompare(bData.dmrId)
}

let GlobalVxeTreeManager: VxeTreeManager | null = null
export function getGlobalVxeTreeManager(): Readonly<VxeTreeManager | null> {
  return GlobalVxeTreeManager
}
export function setGlobalVxeTreeManager(tree: VxeTreeManager): void {
  if (!GlobalVxeTreeManager) {
    GlobalVxeTreeManager = tree
  }
}

export function destroyGlobalVxeTreeManager() {
  GlobalVxeTreeManager = null
}

type VxeTreeManagerOptions = {
  needFullCallNode?: boolean
  shouldSyncSourceData?: boolean
  sortMethod?: (a: TreeNodeData, b: TreeNodeData) => number
  filterFunc?: (node: TreeNodeData) => boolean
}

export const DeviceOnlineFilterReg =
  /device_status_green|device_status_yellow|device_status_red|device_status_other_red|device_status_icon_th|device_status_emergency_yellow|device_status_emergency_green/i

function loadTreeDataFromBfglob() {
  const orgData = bfglob.gorgData?.getAll()
  const deviceData = bfglob.gdevices?.getAll()

  const allTreeData: TreeNodeData[] = []

  for (const key in orgData) {
    const node: TreeNodeData = {
      rid: orgData[key].rid,
      parentOrgId: orgData[key].parentOrgId,
      nodeType: TreeNodeType.Org,
    }
    allTreeData.push(node)
  }

  for (const key in deviceData) {
    const node: TreeNodeData = {
      rid: deviceData[key].rid,
      parentOrgId: deviceData[key].orgId,
      nodeType: TreeNodeType.Terminal,
    }
    allTreeData.push(node)
  }

  return allTreeData
}

// 从 bfglob 中加载树形数据，如果blglob没有初始化成功，等待并重试
export async function loadTreeData(): Promise<TreeNodeData[]> {
  return await new Promise(resolve => {
    const interval = setInterval(() => {
      if (HasPrivilegeDataLoaded()) {
        clearInterval(interval)
        resolve(loadTreeDataFromBfglob())
      }
    }, 100)
  })
}

export const syncAddOrgNode = <T extends { rid: string; parentOrgId: string }>(orgItem: T) => {
  const treeData = {
    rid: orgItem.rid,
    parentOrgId: orgItem.parentOrgId,
    nodeType: TreeNodeType.Org,
  }
  bfglob.emit('treeInsert', treeData)
  updateTreeOrgDeviceCount()
}

export const syncUpdateOneOrgNode = <T extends { rid: string; parentOrgId: string }>(orgItem: T) => {
  const treeData = {
    rid: orgItem.rid,
    parentOrgId: orgItem.parentOrgId,
    nodeType: TreeNodeType.Org,
  }
  bfglob.emit('treeUpdate', treeData)
  updateTreeOrgDeviceCount()
}

export const syncRemoveOneOrgNode = (orgItemRid: string) => {
  const treeData = {
    rid: orgItemRid,
    nodeType: TreeNodeType.Org,
  }
  bfglob.emit('treeRemove', treeData)
  updateTreeOrgDeviceCount()
}

export const syncAddOneDeviceNode = <T extends { rid: string; orgId: string }>(deviceItem: T) => {
  const treeData = {
    rid: deviceItem.rid,
    parentOrgId: deviceItem.orgId,
    nodeType: TreeNodeType.Terminal,
  }
  bfglob.emit('treeInsert', treeData)
  updateTreeOrgDeviceCount()
}

export const syncUpdateOneDeviceNode = <T extends { rid: string; orgId: string }>(deviceItem: T) => {
  const treeData = {
    rid: deviceItem.rid,
    parentOrgId: deviceItem.orgId,
    nodeType: TreeNodeType.Terminal,
  }
  bfglob.emit('treeUpdate', treeData)
  updateTreeOrgDeviceCount()
}

export const syncRemoveOneDeviceNode = (deviceItemRid: string) => {
  const treeData = {
    rid: deviceItemRid,
    nodeType: TreeNodeType.Terminal,
  }
  bfglob.emit('treeRemove', treeData)
  updateTreeOrgDeviceCount()
}

export class VxeTreeManager {
  treeRef: VxeTableInstance<TreeNodeData> | null = null
  options: VxeTreeManagerOptions = {}
  constructor(treeRef: VxeTableInstance<TreeNodeData> | null, options: VxeTreeManagerOptions = {}) {
    this.treeRef = treeRef
    this.options = {
      sortMethod: defaultVxeTableTreeSortCompareFn,
      shouldSyncSourceData: true,
      ...options,
    }

    if (!GlobalVxeTreeManager) {
      setGlobalVxeTreeManager(this)

      loadTreeData().then(data => {
        this.initTreeData(data)
      })
    }

    if (this.options.shouldSyncSourceData) {
      bfglob.on('treeInsert', this.handleInsert.bind(this))
      bfglob.on('treeRemove', this.handleRemove.bind(this))
      bfglob.on('treeUpdate', this.handleUpdate.bind(this))
      bfglob.on('treeSortChildren', this.sort.bind(this))
      bfglob.on('updateDeviceNodeTitle', this.handleUpdateDeviceNodeTitle.bind(this))
      watch(
        () => treeRef,
        value => {
          if (!value) {
            bfglob.off('treeInsert', this.handleInsert.bind(this))
            bfglob.off('treeRemove', this.handleRemove.bind(this))
            bfglob.off('treeUpdate', this.handleUpdate.bind(this))
            bfglob.off('treeSortChildren', this.sort.bind(this))
            bfglob.off('updateDeviceNodeTitle', this.handleUpdateDeviceNodeTitle.bind(this))
          }
        }
      )
    }
  }

  sort() {
    const data = this.treeRef.getData()
    data.sort(this.options.sortMethod)
    this.treeRef?.loadData(data)
  }

  reload() {
    this.treeRef?.updateData()
  }

  initTreeData(data: TreeNodeData[]) {
    let d = data
    if (this.options.filterFunc) {
      d = data.filter(this.options.filterFunc)
    }

    if (this.options.needFullCallNode) {
      d.push({
        rid: fullCallDmrIdStr,
        nodeType: TreeNodeType.Org,
        parentOrgId: '00000000-0000-0000-0000-000000000000',
      } satisfies TreeNodeData)
    }

    d.sort(this.options.sortMethod)
    this.treeRef?.reloadData(d)
  }

  handleInsert(node: TreeNodeData) {
    const parentRow = this.treeRef.getRowById(node.parentOrgId)
    if (parentRow) {
      this.treeRef?.insertChild(node, parentRow)
    } else {
      this.treeRef?.insert(node)
    }
  }
  handleRemove(node: TreeNodeData) {
    // 传入的node没有parentOrgId，尝试使用rid获取row
    const rawNode = this.treeRef?.getRowById(node.rid)
    this.treeRef?.remove(rawNode)
  }
  handleUpdate(node: TreeNodeData) {
    const rawNode = this.treeRef?.getRowById(node.rid)
    if (!rawNode) {
      return
    }
    this.treeRef?.setRow(rawNode, rawNode)
  }

  handleUpdateDeviceNodeTitle(device) {
    const rawNode = this.treeRef?.getRowById(device.rid)
    if (!rawNode) {
      return
    }

    this.treeRef?.reloadRow(rawNode)
  }

  cloneTree(filter: (node: TreeNodeData) => boolean = () => true): TreeNodeData[] {
    return cloneDeep(this.treeRef.getData()).filter(filter)
  }
}
