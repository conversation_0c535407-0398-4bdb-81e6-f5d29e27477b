<template>
  <section ref="bdContactContainer" class="dmr-contact-wrap">
    <div class="dmr-contact-operation-buttons left">
      <el-button type="primary" @click="addContact" v-text="$t('dialog.add')" />
      <el-button type="danger" :disabled="selectedContact.length === 0" @click="deleteContact" v-text="$t('dialog.delete')" />
    </div>
    <el-table
      ref="bdContact"
      :data="bdContact"
      tooltip-effect="dark"
      style="width: 100%"
      :height="bdContactTableHeight"
      border
      highlight-current-row
      @selection-change="handleSelectionChange"
      @row-dblclick="rowDblclickHandler"
    >
      <el-table-column type="selection" width="40" />
      <el-table-column type="index" width="50" />
      <el-table-column :label="$t('dialog.contact')" prop="userName" sortable min-width="130" />
      <el-table-column :label="$t('dialog.callType')" min-width="130" sortable :formatter="formatterCallType" />
      <el-table-column label="DMRID" min-width="130" :formatter="formatterCallerId" />
    </el-table>
    <el-dialog
      v-model="showContactSetting"
      :title="$t('dialog.dmrAddressBook')"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :modal="false"
      :fullscreen="fullscreen"
      top="0"
      width="30%"
      class="dmr-contact-dlg drag-dialog"
      modal-class="drag-dialog-modal"
      @close="closeDlgFn"
    >
      <template #header>
        <div v-bfdrag>
          {{ $t('dataTable.emergency') }}
        </div>
      </template>
      <el-form ref="dmr-contact-form" :model="oneContact" label-width="70px" class="dmr-contact-form" :rules="contactRules">
        <el-row :gutter="20" class="no-margin-x">
          <el-col :xs="24">
            <el-form-item :label="$t('dialog.contact')" prop="userName">
              <el-input v-model="oneContact.userName" :maxlength="16" :disabled="isFullCall" />
            </el-form-item>
          </el-col>
          <el-col :xs="24">
            <el-form-item :label="$t('dialog.callType')">
              <el-select v-model="oneContact.callerType" :placeholder="$t('dialog.select')" filterable :no-match-text="$t('dialog.noMatchText')">
                <el-option v-for="(item, i) in callerTypeList" :key="i" :label="item.label" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :xs="24">
            <el-form-item v-if="isFullCall" prop="callerId" label="DMRID">
              <el-input v-model="dmrId" disabled />
            </el-form-item>
            <DmridInput v-else v-model="dmrId" :minNo="1" :disabled="isFullCall" :sm="24" isDec />
          </el-col>
          <el-col :span="24">
            <el-form-item label-width="0" class="center actions dmr-contact-form-operation">
              <el-button type="primary" :disabled="disAddBtn" @click="saveOneContact" v-text="saveBtnLabel" />
              <el-button type="primary" :disabled="disContinueAdd" @click="saveOneContactAndContinue" v-text="$t('dialog.keepAdding')" />
              <el-button type="danger" @click="cancelSaveContact" v-text="$t('dialog.cancel')" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-dialog>
  </section>
</template>

<script>
  import { DefModel } from '@/writingFrequency/repeater'
  import bfutil from '@/utils/bfutil'
  import { cloneDeep, merge } from 'lodash'
  import validateRules from '@/utils/validateRules'
  import bfproto from '@/modules/protocol'
  import { defineAsyncComponent } from 'vue'

  const TableId = bfproto.lookupEnum('TableId')
  const OneContact = {
    contactId: 0,
    sortId: 0,
    callerId: 0,
    userName: '',
    inUse: 1,
    callerType: 0x01,
  }

  export default {
    name: 'TR925DmrContact',
    props: {
      repeaterData: {
        type: [Object, undefined],
      },
      repeater: {
        type: String,
        default: '',
      },
      getRepeaterId: {
        type: Function,
        default: bfutil.noop,
      },
      disabled: {
        type: Boolean,
        default: false,
      },
      packageName: {
        type: String,
        default: '',
      },
      deviceModel: {
        type: String,
        default: DefModel,
      },
      saveMethod: {
        type: Function,
        default: bfutil.noop,
      },
      tableId: {
        type: Number,
        default: 0,
      },
      dataId: {
        type: Number,
        default: -1,
      },
    },
    data() {
      return {
        bdContactTableHeight: '300',
        // 所有联系人数据
        bdContact: [],
        selectedContact: [],
        oneContact: {
          ...OneContact,
        },
        // 控制编辑联系人对话框
        showContactSetting: false,
        // 标记已经使用的索引id
        usedIndexIds: {},
        // 标记当前对话框是否处于修改数据状态
        isEdit: false,
        curEditIndexId: -1,
        fullCallDmrId: 0x04ffffff,
        idLimit: 1600,
      }
    },
    methods: {
      // 表格选中行事件
      handleSelectionChange(val) {
        this.selectedContact = val
      },
      // 双击表格行弹出窗口以修改数据
      rowDblclickHandler(row, event, option) {
        this.isEdit = true
        this.showContactSetting = true
        this.curEditIndexId = row.contactId
        this.restoreOneContact(row)
      },
      // 弹出添加联系人对话框
      addContact() {
        this.showContactSetting = true
        this.restoreOneContact()
        this.autoFillValue()
      },
      // 删除联系人
      deleteContact() {
        const newBdContact = cloneDeep(this.bdContact)
        for (let i = 0; i < this.selectedContact.length; i++) {
          const item = this.selectedContact[i]

          // 找到相同索引数据id，删除数据，回收索引id
          for (let k = 0; k < newBdContact.length; k++) {
            const bdContact = newBdContact[k]
            if (item.contactId === bdContact.contactId) {
              delete this.usedIndexIds[item.contactId]
              newBdContact.splice(k, 1)
              break
            }
          }
        }

        this.bdContact = newBdContact
      },
      getIndexId() {
        let id = 0
        while (id < this.idLimit) {
          if (typeof this.usedIndexIds[id] === 'undefined') {
            this.usedIndexIds[id] = 1
            return id
          }

          id++
        }
        return -1
      },
      // 恢复表单默认参数配置
      restoreOneContact(data = OneContact) {
        this.oneContact = { ...data }
      },
      autoFillUserName() {
        const callerTypeLabel = this.getCallTypeLabel(this.oneContact.callerType)
        if (this.isFullCall) {
          this.oneContact.userName = callerTypeLabel
        } else {
          this.oneContact.userName = callerTypeLabel ? callerTypeLabel + ' ' + (this.oneContact.contactId + 1) : ''
        }
      },
      autoFillValue() {
        this.oneContact.sortId = this.oneContact.contactId = this.getIndexId()
        this.autoFillUserName()
      },
      // 将数据添加到联系人数组集合
      saveToBdContact() {
        this.bdContact.push({ ...this.oneContact })
      },
      updateBdContact(data = this.oneContact) {
        for (let i = 0; i < this.bdContact.length; i++) {
          const item = this.bdContact[i]
          if (item.contactId === data.contactId) {
            this.bdContact[i] = merge(item, data)
            break
          }
        }
        this.bdContact = [...this.bdContact]
      },
      // 保存当前添加的联系人，并关闭对话框
      saveOneContact() {
        this.$refs['dmr-contact-form'].validate(valid => {
          if (!valid) {
            return false
          }

          this.showContactSetting = false

          // 判断是添加还是保存
          if (this.isEdit) {
            this.updateBdContact()
          } else {
            this.saveToBdContact()
          }
        })
      },
      // 保存并继续添加联系人
      saveOneContactAndContinue() {
        this.$refs['dmr-contact-form'].validate(valid => {
          if (!valid) {
            return false
          }

          const cacheContact = cloneDeep(this.oneContact)
          cacheContact.callerId++

          this.saveToBdContact()

          // 判断是否已经到数据上限，不到上限则继续添加
          if (this.isToLimit) {
            return
          }
          this.restoreOneContact(cacheContact)
          this.autoFillValue()
        })
      },
      // 取消添加联系人
      cancelSaveContact() {
        if (!this.showContactSetting) {
          return
        }

        this.showContactSetting = false

        if (!this.isEdit) {
          // 删除当前的索引数据
          delete this.usedIndexIds[this.oneContact.contactId]
        }

        // 取消对应标记
        this.isEdit = false
        this.curEditIndexId = -1

        this.$refs['dmr-contact-form'].clearValidate()
      },
      closeDlgFn() {
        this.cancelSaveContact()
      },
      getBdContact(contactId) {
        for (let i = 0; i < this.bdContact.length; i++) {
          const item = this.bdContact[i]
          if (item.contactId === contactId) {
            return item
          }
        }

        return undefined
      },
      resetUsedIndex() {
        this.usedIndexIds = this.bdContact
          .map(item => {
            return { [item.contactId]: 1 }
          })
          .reduce((p, c) => {
            return Object.assign(p, c)
          }, {})
      },
      decodeDmrContactId(callerId = this.oneContact.callerId) {
        return callerId & 0x00ffffff
      },
      getDmrContactLabel(callerId = 1) {
        const dmrId = this.decodeDmrContactId(callerId)
        return `${dmrId.toString(16).padStart(8, '0').toUpperCase()} / ${dmrId}`
      },
      encodeDmrContactId(val, callerType = this.oneContact.callerType) {
        if (typeof val !== 'number') {
          val = 1
        }

        return (callerType << 24) + val
      },
      decodeCallType(callerId) {
        // 默认返回组呼
        if (!callerId) {
          return 0x01
        }

        return (callerId >> 24) & 0xff
      },
      getCallTypeLabel(callType) {
        switch (callType) {
          case 0x01:
            return this.$t('dialog.groupCall')
          case 0x02:
            return this.$t('dialog.singleCall')
          case 0x04:
            return this.$t('dialog.fullCall')
          default:
            return ''
        }
      },
      getCallTypeLabelFromCallerId(callerId) {
        const callType = this.decodeCallType(callerId)
        return this.getCallTypeLabel(callType)
      },
      formatterCallType(row) {
        return this.getCallTypeLabelFromCallerId(row.callerId)
      },
      formatterCallerId(row) {
        return this.getDmrContactLabel(row.callerId)
      },
    },
    computed: {
      fullscreen() {
        return this.$root.layoutLevel === 0
      },
      isToLimit() {
        return this.bdContact.length >= this.idLimit
      },
      disAddBtn() {
        return !this.isEdit && this.isToLimit
      },
      disContinueAdd() {
        return this.isEdit || this.isToLimit
      },
      contactRules() {
        const uniqueRule = (prop, message) => {
          return {
            validator: (rule, value, callback) => {
              const tempData = this.bdContact.filter(item => {
                // 如果处于编辑修改状态，数据未变化也通过验证
                let valid = item[prop] === value
                if (prop === 'callerId') {
                  valid = this.decodeDmrContactId(item[prop]) === this.decodeDmrContactId(value)
                }
                return valid && this.isEdit && this.curEditIndexId === item.contactId ? false : valid
              })

              if (tempData.length > 0) {
                callback(new Error(message))
              } else {
                callback()
              }
            },
            trigger: ['blur', 'change'],
          }
        }

        return {
          userName: [validateRules.required(), uniqueRule('userName', this.$t('msgbox.bdUserNameUnique'))],
          callerId: [validateRules.required(), uniqueRule('callerId', this.$t('msgbox.bdNumberUnique'))],
        }
      },
      saveBtnLabel() {
        return this.isEdit ? this.$t('dialog.save') : this.$t('dialog.add')
      },
      // 计算dmrId
      // Uint32 = BB AA AA AA
      // BB：呼叫类型
      // 0x01：组呼
      // 0x02：单呼
      // 0x04：全呼
      // AAAAAA：实际DMRID
      // 值范围：1-16776415（1-0x00FFFCDF)
      // 号码类型为全呼时，号码为固定值：0x04FFFFFF
      dmrId: {
        get() {
          if (this.isFullCall) {
            // 去掉了类型
            return 0x00ffffff
          }

          return this.decodeDmrContactId(this.oneContact.callerId)
        },
        set(val) {
          this.oneContact.callerId = this.encodeDmrContactId(val, this.oneContact.callerType)
        },
      },
      callerTypeList() {
        return [
          {
            label: this.$t('dialog.groupCall'),
            value: 0x01,
          },
          {
            label: this.$t('dialog.singleCall'),
            value: 0x02,
          },
          {
            label: this.$t('dialog.fullCall'),
            value: 0x04,
          },
        ]
      },
      isFullCall() {
        return this.oneContact.callerType === 0x04
      },
      wfSettings() {
        return this.repeaterData && this.repeaterData.writeFrequencySetting
      },
      localBdContact() {
        return this.wfSettings && this.wfSettings[TableId[this.tableId]]
      },
    },
    watch: {
      isFullCall(val) {
        if (val) {
          this.dmrId = 0x00ffffff
        }
      },
      'oneContact.callerType'(val, oldVal) {
        if (this.isFullCall || oldVal === 0x04 || !this.isEdit) {
          this.autoFillUserName()
        }

        // 类型变化后，只要不是全呼，则需要重新计算callerId
        if (!this.isFullCall) {
          this.dmrId = this.decodeDmrContactId(this.oneContact.callerId)
        }
      },
      bdContact: {
        deep: true,
        handler(newVal) {
          // 保存本地DMR联系人
          this.saveMethod(TableId[this.tableId], newVal)
        },
      },
      localBdContact: {
        deep: true,
        handler(val) {
          // if (!val) {
          //   this.bdContact = []
          //   return
          // }
          //
          // this.bdContact = [...val]
        },
      },
    },
    components: {
      DmridInput: defineAsyncComponent(() => import('@/components/common/DmridInput.vue')),
    },
  }
</script>

<style>
  .dmr-contact-wrap {
    padding: 10px;
    height: 100%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
  }

  .dmr-contact-wrap > .el-table {
    flex: auto;
    height: 100% !important;
  }

  .dmr-contact-operation-buttons {
    margin-bottom: 10px;
  }

  .dmr-contact-wrap .dmr-contact-dlg .el-dialog__body {
    height: unset;
  }

  .dmr-contact-dlg .dmr-contact-form .el-form-item {
    margin-bottom: 8px;
  }

  .dmr-contact-form-operation {
    margin-top: 8px;
  }
</style>
