/* 定义全局通用的css变量 */

:root {
  /* 自定义css变量 */
  --bf-base-font-size: 16px;
  --bf-border-size: 2px;
  --bf-text-highlight: #ff811d;
  --bf-btn-info-color: 117, 117, 117; //#757575;
  --bf-btn-default-color: 96, 172, 218; //#60acda;
  --bf-btn-warning-color: 255, 129, 29; //#ff811d;
  --bf-btn-danger-color: 255, 74, 74; //#ff4a4a;
  --bf-btn-primary-color: var(--bf-btn-warning-color);
  --bf-btn-stroke-color: var(--bf-btn-warning-color);
  --bf-btn-stroke-width: 2;
  --bf-btn-fill-color: var(--bf-btn-warning-color);
  --bf-btn-text-color: 255, 255, 255; //#fff
  --bf-btn-corner-size: 6px;
  --bf-form-item-btn-border-color: rgba(148, 204, 232, 1);
  --bf-form-item-btn-hover-border-color: rgba(148, 204, 232, 0.7);

  // 设计稿的通用定义
  --bf-base-font-family: 'YouSheBiaoTiHei';
  --bf-font-color: #fff;
  --bf-primary-blue-color: #1398E9;
  --bf-accent-color: #E8741A;


  /* 重写element-plus全局变量 */
  --el-text-color-regular: #fff;
}

.bf-component-size {
  --el-component-size-large: 68px;
  --el-component-size: 50px;
  --el-component-size-small: 38px;
}

// 写频表单通用的css
.write-frequency-form {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  gap: 10px 20px;

  .el-form-item {
    width: 400px;

    &.is-required label {
      display: flex;
    }

    .el-form-item__label {
      margin: 0;
    }
  }
}