<template>
  <!-- 设备控制弹窗 -->
  <bf-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    :modal="false"
    class="header-border shadow-md shadow-slate-800 device-control-dialog drag-dialog"
    modal-class="drag-dialog-modal"
    append-to-body
    draggable
    center
    top="25vh"
    @open="openDlgFn"
  >
    <!-- 定位监控表单 (cb01) -->
    <el-form v-if="cmdType === 'cb01' || cmdType === '1'" :model="cb01Cmd" label-position="left" class="cb01 w-[400px]">
      <el-form-item>
        <template #label>
          <EllipsisText :content="$t('dialog.locateCount') + ':'" style="width: 148px; height: 50px; line-height: 50px; font-size: 16px" />
        </template>
        <bf-input-numberV2 v-model="cb01Cmd.count" :min="1" :max="9999" class="!w-full" style="height: 50px" />
      </el-form-item>

      <el-form-item>
        <template #label>
          <EllipsisText :content="$t('dialog.locateSpacing') + ':'" style="width: 148px; height: 50px; line-height: 50px; font-size: 16px" />
        </template>
        <bf-input-numberV2 v-model="cb01Cmd.spaceTime" :min="5" :max="9995" :step="5" class="!w-full" style="height: 50px" />
      </el-form-item>

      <el-form-item>
        <template #label>
          <EllipsisText :content="$t('dialog.size') + ':'" style="width: 148px; height: 50px; line-height: 50px; font-size: 16px" />
        </template>
        <bf-input-numberV2 v-model="cb01Cmd.size" :min="0" :max="495" :step="5" class="!w-full" style="height: 50px" />
      </el-form-item>
    </el-form>

    <!-- 跟踪监控表单 (cb02) -->
    <el-form v-if="cmdType === 'cb02' || cmdType === '2'" :model="cb02Cmd" label-position="left" class="cb02 w-[400px]">
      <el-form-item>
        <template #label>
          <EllipsisText :content="$t('dialog.cmdOpts') + ':'" style="width: 148px; height: 50px; line-height: 50px; font-size: 16px" />
        </template>
        <bf-select v-model="cb02Cmd.track" @change="cb02Cmd_changed" class="!w-full" style="height: 50px">
          <el-option v-for="(item, index) in cb02Cmd_options" :key="index" :label="$t(item.label)" :value="item.value" />
        </bf-select>
      </el-form-item>

      <el-form-item>
        <template #label>
          <EllipsisText :content="$t('dialog.trailSpacing') + ':'" style="width: 148px; height: 50px; line-height: 50px; font-size: 16px" />
        </template>
        <bf-input-numberV2 v-model="cb02Cmd.spaceTime" :min="5" :max="9995" :step="5" :disabled="disabled.cb02Cmd" class="!w-full" style="height: 50px" />
      </el-form-item>

      <el-form-item>
        <template #label>
          <EllipsisText :content="$t('dialog.size') + ':'" style="width: 148px; height: 50px; line-height: 50px; font-size: 16px" />
        </template>
        <bf-input-numberV2 v-model="cb02Cmd.size" :min="0" :max="495" :step="5" :disabled="disabled.cb02Cmd" class="!w-full" style="height: 50px" />
      </el-form-item>
    </el-form>

    <!-- 遥开遥毙表单 (cb09) -->
    <el-form v-if="cmdType === 'cb09' || cmdType === '9'" :model="cb09Cmd" label-position="left" class="cb09 w-[400px]">
      <el-form-item>
        <template #label>
          <EllipsisText :content="$t('dialog.cmdOpts') + ':'" style="width: 108px; height: 50px; line-height: 50px; font-size: 16px" />
        </template>
        <bf-select v-model="cb09Cmd.setCmd" @change="cb09Cmd_changed" class="!w-full" style="height: 50px">
          <el-option v-for="(item, index) in cb09Cmd_options" :key="index" :label="$t(item.label)" :value="item.value" />
        </bf-select>
      </el-form-item>

      <el-form-item>
        <template #label>
          <EllipsisText :content="$t('dialog.lockedStatus') + ':'" style="width: 108px; height: 50px; line-height: 50px; font-size: 16px" />
        </template>
        <bf-select v-model="cb09Cmd.status" :disabled="disabled.cb09Cmd" class="!w-full" style="height: 50px">
          <el-option v-for="(item, index) in cb09Cmd_stats_options" :key="index" :label="$t(item.label)" :value="item.value" :disabled="item.disabled" />
        </bf-select>
      </el-form-item>
    </el-form>

    <!-- 文本短信表单 (cb31) -->
    <el-form v-if="cmdType === 'cb31' || cmdType === '31'" :model="bc31Cmd" label-position="left" class="cb31 w-[400px]">
      <el-form-item>
        <template #label>
          <EllipsisText :content="$t('dialog.smsType') + ':'" style="width: 108px; height: 50px; line-height: 50px; font-size: 16px" />
        </template>
        <bf-select v-model="bc31Cmd.smsType" class="!w-full" style="height: 50px">
          <el-option v-for="(item, index) in smsTypeList" :key="index" :label="item.label" :value="item.value" />
        </bf-select>
      </el-form-item>

      <el-form-item>
        <template #label>
          <EllipsisText :content="$t('dialog.smsContent') + ':'" style="width: 108px; height: 50px; line-height: 50px; font-size: 16px" />
        </template>
        <bf-input v-model="bc31Cmd.message" :maxlength="smsLenLimit" :autosize="{ minRows: 3, maxRows: 5 }" type="textarea" resize="none" class="!w-full" />
        <div
          class="text-gray-400 text-xs text-right sms-input-tips"
          :class="{
            'text-red-500': bc31Cmd.message.length >= smsLenLimit,
          }"
        >
          {{ bc31Cmd.message.length }} / {{ smsLenLimit }}
        </div>
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="flex justify-center">
        <bf-button color-type="primary" :disabled="sendCmdBtn" @click="handleSendCommand">
          {{ $t('dialog.sendCmdTitle') }}
        </bf-button>
      </div>
    </template>
  </bf-dialog>
</template>

<script setup lang="ts">
  import { ref, reactive, computed } from 'vue'
  import { useI18n } from 'vue-i18n'
  import bfDialog from '@/components/bfDialog/main'
  import bfButton from '@/components/bfButton/main'
  import bfInputNumberV2 from '@/components/bfInputNumber/main'
  import bfSelect from '@/components/bfSelect/main'
  import bfInput from '@/components/bfInput/main'
  import EllipsisText from '@/components/common/EllipsisText.vue'
  import bfNotify from '@/utils/notify'
  import bfprocess from '@/utils/bfprocess'

  // Props 定义
  interface Props {
    rid?: string
    cmdType?: string
    modelValue?: boolean
    dialogVisible?: boolean
  }

  const props = withDefaults(defineProps<Props>(), {
    rid: '',
    cmdType: 'cb01',
    modelValue: false,
    dialogVisible: false,
  })

  // Emits 定义
  const emit = defineEmits<{
    'update:modelValue': [value: boolean]
    'update:dialogVisible': [value: boolean]
  }>()

  // 国际化
  const { t } = useI18n()

  // 响应式数据
  const dialogVisible = computed({
    get: () => props.modelValue || props.dialogVisible,
    set: value => {
      emit('update:modelValue', value)
      emit('update:dialogVisible', value)
    },
  })
  const sendCmdBtn = ref(false)

  // 对话框标题
  const dialogTitle = computed(() => {
    switch (props.cmdType) {
      case 'cb01':
      case '1':
        return t('dialog.locateCtrl')
      case 'cb02':
      case '2':
        return t('dialog.trailCtrl')
      case 'cb09':
      case '9':
        return t('dialog.turnOn') + '/' + t('dialog.close')
      case 'cb31':
      case '31':
        return t('dialog.textInfo')
      default:
        return ''
    }
  })

  // cb01 命令数据
  const cb01Cmd = reactive({
    count: 1,
    spaceTime: 5,
    size: 20,
  })

  // cb02 命令数据
  const cb02Cmd = reactive({
    track: 1,
    spaceTime: 5,
    size: 20,
  })

  // cb02 命令选项
  const cb02Cmd_options = [
    {
      value: 0,
      label: t('dialog.cancel'),
    },
    {
      value: 1,
      label: t('dialog.starting'),
    },
    {
      value: 2,
      label: t('nav.enquiry'),
    },
  ]

  // cb09 命令数据
  const cb09Cmd = reactive({
    setCmd: 1,
    status: 2,
  })

  // cb09 命令选项
  const cb09Cmd_options = [
    {
      value: 0,
      label: t('dataTable.powerOn'),
    },
    {
      value: 1,
      label: t('dialog.lockedDev'),
    },
    {
      value: 2,
      label: t('nav.enquiry'),
    },
  ]

  // cb09 状态选项
  const cb09Cmd_stats_options = reactive([
    {
      value: 0,
      label: t('dataTable.powerOn'),
      disabled: false,
    },
    {
      value: 1,
      label: t('dialog.disListen'),
      disabled: false,
    },
    {
      value: 2,
      label: t('dialog.disSend'),
      disabled: false,
    },
    {
      value: 3,
      label: t('dialog.disSL'),
      disabled: false,
    },
  ])

  // bc31 文本短信命令数据
  const bc31Cmd = reactive({
    setCmd: 1,
    message: '',
    sendTime: '',
    codeTP: 2,
    smsType: '02',
  })

  // 短信类型列表
  const smsTypeList = computed(() => [
    {
      label: t('dialog.textInfo'),
      value: '02',
    },
    {
      label: t('dialog.autoPlaySms'),
      value: '12',
    },
  ])

  // 短信长度限制
  const smsLenLimit = ref(140)

  // 禁用状态
  const disabled = reactive({
    cb02Cmd: false,
    cb09Cmd: false,
  })

  // 计算属性：根据 rid 判断是设备还是组织，并获取对应的 dmrId
  const cmdTarget = computed(() => {
    if (!props.rid) {
      return {
        groud: [],
        device: [],
      }
    }

    // 首先尝试从设备数据中查找
    const deviceData = bfglob.gdevices?.get(props.rid)
    if (deviceData?.dmrId) {
      return {
        groud: [],
        device: [deviceData.dmrId],
      }
    }

    // 然后尝试从组织数据中查找
    const orgData = bfglob.gorgData?.get(props.rid)
    if (orgData?.dmrId) {
      return {
        groud: [orgData.dmrId],
        device: [],
      }
    }

    // 如果都没找到，返回空目标
    return {
      groud: [],
      device: [],
    }
  })

  // 方法
  const openDlgFn = () => {
    // 发布关闭最小化导航区对应按钮事件
    // 这里可以根据需要添加相关逻辑
  }

  // cb02 命令选项变化处理
  const cb02Cmd_changed = (val: number) => {
    if (val === 1) {
      disabled.cb02Cmd = false
    } else {
      disabled.cb02Cmd = true
    }
  }

  // cb09 命令选项变化处理
  const cb09Cmd_changed = (val: number) => {
    disabled.cb09Cmd = false
    if (val === 0) {
      cb09Cmd.status = 0
      cb09Cmd_stats_options[0].disabled = false
      cb09Cmd_stats_options[1].disabled = true
      cb09Cmd_stats_options[2].disabled = true
      cb09Cmd_stats_options[3].disabled = true
    } else if (val === 1) {
      cb09Cmd.status = 2
      cb09Cmd_stats_options[0].disabled = true
      cb09Cmd_stats_options[1].disabled = false
      cb09Cmd_stats_options[2].disabled = false
      cb09Cmd_stats_options[3].disabled = false
    } else if (val === 2) {
      cb09Cmd.status = 2
      disabled.cb09Cmd = true
    }
  }

  // 发送 cb01 定位监控命令
  const send_cb01_cmd = () => {
    // 使用计算属性获取目标对象
    const targetObj = cmdTarget.value

    // 检查是否有目标
    if (targetObj.groud.length === 0 && targetObj.device.length === 0) {
      bfNotify.warningBox(t('msgbox.selectTarget'), 'error')
      return
    }

    // 数据处理，确保是5的倍数
    const _size = Math.round(cb01Cmd.size / 5) * 5
    const _spaceTime = Math.round(cb01Cmd.spaceTime / 5) * 5
    cb01Cmd.size = _size
    cb01Cmd.spaceTime = _spaceTime

    // 发送命令
    bfprocess.cb01(targetObj, cb01Cmd)

    // 禁用按钮3秒
    disabledSendCmdFunc()

    // 发送命令后关闭对话框
    dialogVisible.value = false
  }

  // 发送 cb02 跟踪监控命令
  const send_cb02_cmd = () => {
    // 使用计算属性获取目标对象
    const targetObj = cmdTarget.value

    // 检查是否有目标
    if (targetObj.groud.length === 0 && targetObj.device.length === 0) {
      bfNotify.warningBox(t('msgbox.selectTarget'), 'error')
      return
    }

    // 数据处理，确保是5的倍数
    const _size = Math.round(cb02Cmd.size / 5) * 5
    const _spaceTime = Math.round(cb02Cmd.spaceTime / 5) * 5
    cb02Cmd.size = _size
    cb02Cmd.spaceTime = _spaceTime

    // 发送命令
    bfprocess.cb02(targetObj, cb02Cmd)

    // 禁用按钮3秒
    disabledSendCmdFunc()

    // 发送命令后关闭对话框
    dialogVisible.value = false
  }

  // 发送 cb09 遥开遥毙命令
  const send_cb09_cmd = () => {
    // 使用计算属性获取目标对象
    const targetObj = cmdTarget.value

    // cb09 命令不支持组织，只支持设备
    if (targetObj.groud.length > 0) {
      bfNotify.warningBox(t('msgbox.canNotDeliveredByGroup'), 'error')
      return
    }

    // 检查是否有设备目标
    if (targetObj.device.length === 0) {
      bfNotify.warningBox(t('msgbox.selectTarget'), 'error')
      return
    }

    // 发送命令
    bfprocess.cb09(targetObj, cb09Cmd)

    // 禁用按钮3秒
    disabledSendCmdFunc()

    // 发送命令后关闭对话框
    dialogVisible.value = false
  }

  // 发送 bc31 文本短信命令
  const send_bc31_cmd = () => {
    // 使用计算属性获取目标对象
    const targetObj = cmdTarget.value

    // 检查是否有目标
    if (targetObj.groud.length === 0 && targetObj.device.length === 0) {
      bfNotify.warningBox(t('msgbox.selectTarget'), 'error')
      return
    }

    // 检查短信内容是否为空
    if (!bc31Cmd.message) {
      bfNotify.warningBox(t('msgbox.smsNotEmpty'), 'warning')
      return
    }

    // 发送命令
    bfprocess.bc31(targetObj, bc31Cmd)

    // 禁用按钮3秒
    disabledSendCmdFunc()

    // 发送命令后关闭对话框
    dialogVisible.value = false
  }

  // 统一的发送命令处理函数
  const handleSendCommand = () => {
    switch (props.cmdType) {
      case 'cb01':
      case '1':
        send_cb01_cmd()
        break
      case 'cb02':
      case '2':
        send_cb02_cmd()
        break
      case 'cb09':
      case '9':
        send_cb09_cmd()
        break
      case 'cb31':
      case '31':
        send_bc31_cmd()
        break
      default:
        console.warn('Unknown command type:', props.cmdType)
    }
  }

  // 发送命令后禁用按钮
  const disabledSendCmdFunc = (sec = 3000) => {
    sendCmdBtn.value = true
    setTimeout(() => {
      sendCmdBtn.value = false
    }, sec)
  }
</script>

<style lang="scss">
  .el-dialog.device-control-dialog {
    width: 480px;

    .el-dialog__body {
      display: flex;
      justify-content: center;
    }
  }
</style>
