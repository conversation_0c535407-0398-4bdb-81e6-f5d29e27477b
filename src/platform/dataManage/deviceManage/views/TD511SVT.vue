<template>
  <div class="writer-frequency-wrap">
    <selectDevice v-model="selectedDeviceDmrId" :disabled="isReading || isWriting" />
    <el-tabs v-model="writerFrequencyTabName" class="writer-frequency-tabs" tab-position="left">
      <!--设备信息-->
      <el-tab-pane lazy :label="$t('dialog.deviceInfo')" name="deviceWriteInfo" class="deviceInfo-box settings-box">
        <deviceInfo ref="deviceWriteInfo" v-model="deviceWriteInfo" v-model:optsFunType="optsFunType" :model="Model" showOptFeatures hasOptsFunType />
      </el-tab-pane>
      <!--常规设置-->
      <el-tab-pane lazy :label="$t('dialog.generalSetting')" name="generalSettings" class="general-settings-box settings-box">
        <el-form
          ref="generalSettings"
          class="general-settings-form"
          :model="generalSettings"
          label-width="95px"
          label-position="top"
          :rules="generalSettingsRules"
        >
          <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
            <el-col :xs="24" :sm="12">
              <el-form-item :label="$t('dialog.terminalName')" prop="deviceName">
                <el-input v-model="generalSettings.deviceName" :maxlength="16" disabled />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item label="DMRID">
                <el-input :value="dmrIdLabel" disabled />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item :label="$t('dialog.repeaterId')" prop="repeaterId">
                <el-input v-model.number="generalSettings.repeaterId" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item :label="$t('dialog.programmingPwd')">
                <el-input v-model="passwordInfo.md5Key" type="password" :maxlength="8" @input="fixMd5KeyValue" />
              </el-form-item>
            </el-col>
            <!--            <el-col :xs="24" :sm='12'>-->
            <!--              <el-form-item :label="$t('dialog.voiceCryptoType')" prop="soundEncryptType">-->
            <!--                <el-select v-model="generalSettings.soundEncryptType"-->
            <!--                           -->
            <!--                           :placeholder="$t('dialog.select')"-->
            <!--                           filterable-->
            <!--                           :no-match-text="$t('dialog.noMatchText')">-->
            <!--                  <el-option v-for="(item,i) in soundEncryptTypeList" :key="i"-->
            <!--                             :label="item.label"-->
            <!--                             :value="item.value"></el-option>-->
            <!--                </el-select>-->
            <!--              </el-form-item>-->
            <!--            </el-col>-->
            <!--            <el-col :xs="24" :sm='12'>-->
            <!--              <el-form-item :label="$t('dialog.voiceCryptoKey')" prop="soundEncryptValue">-->
            <!--                <el-input v-model="generalSettings.soundEncryptValue"-->
            <!--                          type="password"-->
            <!--                          @input="fixSoundEncryptValue"-->
            <!--                          :maxlength="10"-->
            <!--                          :disabled='generalSettings.soundEncryptType===0'></el-input>-->
            <!--              </el-form-item>-->
            <!--            </el-col>-->
            <el-col v-if="!DeviceNoLocale" :xs="24" :sm="12">
              <el-form-item :label="$t('dialog.languageType')" prop="soundAndDisplayTip.locale">
                <el-select
                  v-model="generalSettings.soundAndDisplayTip.locale"
                  :placeholder="$t('dialog.select')"
                  filterable
                  :no-match-text="$t('dialog.noMatchText')"
                >
                  <el-option v-for="item in localeList" :key="item.label" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item :label="$t('dialog.voiceLevel')" prop="soundCtrlLevel">
                <bf-input-number
                  v-model="generalSettings.soundCtrlLevel"
                  step-strictly
                  :min="0"
                  :max="8"
                  :step="1"
                  :formatter="
                    v => {
                      return v === 0 ? $t('writeFreq.off') : v
                    }
                  "
                />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item :label="$t('dialog.voiceDelay')" prop="soundCtrlDelay">
                <el-input-number v-model="generalSettings.soundCtrlDelay" step-strictly :min="500" :max="10000" :step="500" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item :label="$t('dialog.sendPreambleDuration')" prop="sendLeadCodeTime">
                <el-input-number v-model="generalSettings.sendLeadCodeTime" step-strictly :min="0" :max="8640" :step="240" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item :label="$t('dialog.offNetworkGroupCallHangTime')" prop="offlineGroupCallHungTime">
                <el-input-number v-model="generalSettings.offlineGroupCallHungTime" step-strictly :min="0" :max="7000" :step="500" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item :label="$t('dialog.offNetworkSingleCallHangTime')" prop="offlineSingleCallHungTime">
                <el-input-number v-model="generalSettings.offlineSingleCallHungTime" step-strictly :min="0" :max="7000" :step="500" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item :label="$t('dialog.powerSavingMode')" prop="powerBluetooth.savePowerMode">
                <el-select
                  v-model="generalSettings.powerBluetooth.savePowerMode"
                  :placeholder="$t('dialog.select')"
                  filterable
                  :no-match-text="$t('dialog.noMatchText')"
                >
                  <el-option v-for="(item, i) in savePowerModeList" :key="i" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item :label="$t('dialog.chDisplayMode')" prop="soundAndDisplayTip.freqDisplay">
                <el-select
                  v-model="generalSettings.soundAndDisplayTip.freqDisplay"
                  :placeholder="$t('dialog.select')"
                  filterable
                  :no-match-text="$t('dialog.noMatchText')"
                >
                  <el-option v-for="(item, i) in freqDisplayList" :key="i" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="disabledAllLED">
                <el-checkbox v-model="generalSettings.soundAndDisplayTip.disabledAllLED">
                  <span v-text="$t('dialog.disabledAllLed')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="soundAndDisplayTip.rejectUnfamiliarCall">
                <el-checkbox v-model="generalSettings.soundAndDisplayTip.rejectUnfamiliarCall">
                  <span v-text="$t('dialog.rejectingStrangeCalls')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="soundAndDisplayTip.directMode">
                <el-checkbox v-model="generalSettings.soundAndDisplayTip.directMode">
                  <span v-text="$t('dialog.passThroughMode')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="soundAndDisplayTip.menuOff">
                <el-checkbox v-model="generalSettings.soundAndDisplayTip.menuOff">
                  <span v-text="$t('dialog.closeMenuButton')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="soundAndDisplayTip.showAlias">
                <el-checkbox v-model="generalSettings.soundAndDisplayTip.showAlias">
                  <span v-text="$t('dialog.displaysCallIdAndAlias')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="soundAndDisplayTip.showAlias">
                <el-checkbox v-model="generalSettings.recordFunc.allowErasing">
                  <span v-text="$t('writeFreq.allowErasingDevice')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
            <el-divider>
              <el-icon>
                <CaretBottom />
              </el-icon>
              <span v-text="$t('writeFreq.locateMode')" />
            </el-divider>
            <el-col :xs="24" :sm="12">
              <el-form-item>
                <el-checkbox v-model="generalSettings.recordFunc.gpsEnable">
                  <span v-text="$t('writeFreq.gpsLocate')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item>
                <el-checkbox v-model="generalSettings.recordFunc.bdsEnable">
                  <span v-text="$t('writeFreq.bdsLocate')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
            <el-divider>
              <el-icon>
                <CaretBottom />
              </el-icon>
              <span v-text="$t('writeFreq.voicePrompt')" />
            </el-divider>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="channelSignal.muteAll">
                <el-checkbox v-model="generalSettings.channelSignal.muteAll">
                  <span v-text="$t('dialog.muteAll')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="channelSignal.voiceNotice">
                <el-checkbox v-model="generalSettings.channelSignal.voiceNotice" :disabled="!!generalSettings.channelSignal.muteAll">
                  <span v-text="$t('dialog.voiceIndication')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="channelSignal.channelFreeNotice">
                <el-checkbox v-model="generalSettings.channelSignal.channelFreeNotice" :disabled="!!generalSettings.channelSignal.muteAll">
                  <span v-text="$t('dialog.channelIdleIndication')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item :label="$t('dialog.callPermissionIndication')" prop="channelSignal.allowCallInstruction">
                <el-select
                  v-model="generalSettings.channelSignal.allowCallInstruction"
                  :disabled="!!generalSettings.channelSignal.muteAll"
                  :placeholder="$t('dialog.select')"
                  filterable
                  :no-match-text="$t('dialog.noMatchText')"
                >
                  <el-option v-for="(item, i) in allowCallInstructionList" :key="i" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item :label="$t('dialog.receiveLowPowerPromptInterval')" prop="powerInfoAlert">
                <el-input-number v-model="generalSettings.powerInfoAlert" step-strictly :min="0" :max="635" :step="5" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
            <el-divider>
              <el-icon>
                <CaretBottom />
              </el-icon>
              <span v-text="$t('loginDlg.password')" />
            </el-divider>
            <el-col :xs="24" :sm="12">
              <el-form-item :label="$t('dialog.powerOnPwd')" prop="powerOnPassword">
                <el-input v-model="generalSettings.powerOnPassword" type="password" :maxlength="6" :minlength="6" @input="fixPowerOnValue" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item :label="$t('dialog.channelConfigPassword')" prop="channelConfigPassword">
                <el-input v-model="generalSettings.channelConfigPassword" type="password" :maxlength="6" :minlength="6" />
              </el-form-item>
            </el-col>
            <el-col v-if="isLConfig" :xs="24" :sm="12">
              <el-form-item :label="$t('dialog.uDiskModePassword')" prop="uDiskModePassword">
                <el-input v-model="generalSettings.uDiskModePassword" type="password" :maxlength="6" :minlength="6" />
              </el-form-item>
            </el-col>
          </el-row>

          <el-row v-if="isLConfig" :gutter="20" class="no-margin-x" type="flex" align="middle">
            <el-divider>
              <el-icon>
                <CaretBottom />
              </el-icon>
              <span v-text="$t('dialog.recording')" />
            </el-divider>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="recordFunc.recordEnable">
                <el-checkbox v-model="generalSettings.recordFunc.recordEnable">
                  <span v-text="$t('dialog.recordEnable')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item :label="$t('dialog.recordCompRatio')" prop="recordFunc.recordCompressionRatio">
                <el-select
                  v-model="generalSettings.recordFunc.recordCompressionRatio"
                  :disabled="!!!generalSettings.recordFunc.recordEnable"
                  :placeholder="$t('dialog.select')"
                  filterable
                  :no-match-text="$t('dialog.noMatchText')"
                >
                  <el-option v-for="(item, i) in recordCompressionRatioList" :key="i" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <!-- 蓝牙 -->
          <el-row v-if="enableBluetoothConfig" :gutter="20" class="no-margin-x" type="flex" align="middle">
            <el-divider>
              <el-icon>
                <CaretBottom />
              </el-icon>
              <span v-text="$t('writeFreq.bluetooth')" />
            </el-divider>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="powerBluetooth.blueToothSw">
                <el-checkbox v-model="generalSettings.powerBluetooth.blueToothSw">
                  <span v-text="$t('writeFreq.bluetoothSwitch')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="powerBluetooth.blueToothPttKeepEn">
                <el-checkbox v-model="generalSettings.powerBluetooth.blueToothPttKeepEn">
                  <span v-text="$t('writeFreq.bluetoothPTTKeep')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
          </el-row>

          <TimeZone v-if="isGConfig || isLConfig" ref="timeZone" v-model="timeZone" />
        </el-form>
      </el-tab-pane>
      <!--按键设置-->
      <el-tab-pane lazy :label="$t('dialog.buttonDefinition')" name="buttonDefine" class="buttonDefine-box settings-box">
        <el-form ref="buttonDefine" class="buttonDefine-form" :model="buttonDefined" label-position="top">
          <el-row :gutter="20" class="no-margin-x">
            <el-col :xs="24" :sm="12">
              <el-form-item :label="$t('dialog.longPressDuration')">
                <el-input-number v-model="buttonDefined.longPressDuration" step-strictly :min="250" :max="3750" :step="250" />
              </el-form-item>
            </el-col>
            <el-col :xs="24">
              <el-table :data="buttonDefined.shortLongPressFuncDefine" :empty-text="$t('msgbox.emptyText')" class="lineDetail">
                <el-table-column label="" min-width="65">
                  <template #default="scope">
                    <span v-text="getButtonDefinedLabel(scope.$index)" />
                  </template>
                </el-table-column>
                <el-table-column :label="$t('dialog.shortPress')" min-width="100">
                  <template #default="scope">
                    <el-form-item label-width="0">
                      <el-select v-model="scope.row.short" :placeholder="$t('dialog.select')" filterable :no-match-text="$t('dialog.noMatchText')">
                        <el-option v-for="(shortKey, i) in getSoftKeyFuncDefine(scope, 0)" :key="i" :label="shortKey.label" :value="shortKey.value" />
                      </el-select>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column :label="$t('dialog.longPress')" min-width="85">
                  <template #default="scope">
                    <el-form-item label-width="0">
                      <el-select v-model="scope.row.long" :placeholder="$t('dialog.select')" filterable :no-match-text="$t('dialog.noMatchText')">
                        <el-option v-for="(shortKey, i) in getSoftKeyFuncDefine(scope, 1)" :key="i" :label="shortKey.label" :value="shortKey.value" />
                      </el-select>
                    </el-form-item>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
            <el-col :xs="24">
              <el-table :data="buttonDefined.oneTouchFuncCall" :empty-text="$t('msgbox.emptyText')" class="lineDetail">
                <el-table-column label="" type="index" />
                <el-table-column :label="$t('dialog.callTarget')" min-width="100">
                  <template #default="scope">
                    <el-form-item label-width="0">
                      <el-select v-model="scope.row.addrId" :placeholder="$t('dialog.select')" filterable :no-match-text="$t('dialog.noMatchText')">
                        <el-option v-for="(item, i) in buttonDefineAddressList" :key="i" :label="item.label" :value="item.value" />
                      </el-select>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column :label="$t('dialog.callType')" min-width="85">
                  <template #default="scope">
                    <el-form-item v-if="scope.row.addrId !== 0xffff" label-width="0">
                      <el-select v-model="scope.row.optType" :placeholder="$t('dialog.select')" filterable :no-match-text="$t('dialog.noMatchText')">
                        <el-option v-for="(callType, i) in getSoftKeyCallTypeList(scope.row)" :key="i" :label="callType.label" :value="callType.value" />
                      </el-select>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column :label="$t('writeFreq.sms')" min-width="85">
                  <template #default="scope">
                    <el-form-item v-if="!(scope.row.addrId === 0xffff || scope.row.optType !== SoftKeyCallType.MSG)" label-width="0">
                      <el-select
                        v-model="scope.row.smsId"
                        :placeholder="$t('dialog.select')"
                        filterable
                        :no-match-text="$t('dialog.noMatchText')"
                        popper-class="sms-selection-container"
                      >
                        <el-option v-for="sms in smsList" :key="sms.msgId" :label="sms.msgContent" :value="sms.msgId" />
                      </el-select>
                    </el-form-item>
                  </template>
                </el-table-column>
              </el-table>
            </el-col>
          </el-row>
        </el-form>
      </el-tab-pane>
      <!--短信内容-->
      <el-tab-pane lazy :label="$t('dialog.smsContent')" name="smsContent" class="h-full">
        <shortMessage :ref="refSms" v-model="smsContent" />
      </el-tab-pane>
      <!--   加密配置   -->
      <el-tab-pane lazy :label="$t('writeFreq.encryptionConfig')" name="encryptSettings" class="encrypt-settings-box settings-box">
        <encryptSettings
          v-model:encryptEnable="encryptConfig.config.enable"
          v-model:encryptXORList="encryptList"
          v-model:encryptARC4List="encryptARC4List"
          v-model:encryptAES256List="encryptAES256List"
          :encryptListLimit="encryptListLimit"
        />
      </el-tab-pane>
      <!--菜单设置-->
      <el-tab-pane lazy :label="$t('dialog.menuSettings')" name="menuSettings" class="menu-settings-box settings-box">
        <el-form ref="menuSettings" class="menu-settings-form" :model="menuSettings" label-position="top">
          <el-row :gutter="20" class="no-margin-x">
            <el-col :xs="24" :sm="12">
              <el-form-item :label="$t('dialog.menuHangTime')" :label-width="menuHangTimeLabelWidth" prop="menuConfig.menuHangTime">
                <el-input-number v-model="menuSettings.menuConfig.menuHangTime" step-strictly :min="0" :max="30" :step="1" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item :label="$t('dialog.idShownMinLen')" :label-width="menuHangTimeLabelWidth" prop="menuConfig.idShownMinLen">
                <el-input-number v-model="idShownMinLen" step-strictly :min="1" :max="8" :step="1" />
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="timeSlotSetting.quickDailEnable">
                <el-checkbox v-model="menuSettings.timeSlotSetting.quickDailEnable">
                  <span v-text="$t('dialog.quickDailEnable')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="addressSetting.message">
                <el-checkbox v-model="menuSettings.addressSetting.message">
                  <span v-text="$t('dialog.sms')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
            <el-divider>
              <el-icon>
                <CaretBottom />
              </el-icon>
              <span v-text="$t('dialog.addressBook')" />
            </el-divider>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="addressSetting.callTip">
                <el-checkbox v-model="menuSettings.addressSetting.callTip">
                  <span v-text="$t('dialog.callReminder')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="addressSetting.editAddressBook">
                <el-checkbox v-model="menuSettings.addressSetting.editAddressBook">
                  <span v-text="$t('dialog.editContacts')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="addressSetting.manualDialing">
                <el-checkbox v-model="menuSettings.addressSetting.manualDialing">
                  <span v-text="$t('dialog.manualDialing')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="addressSetting.deviceDetect">
                <el-checkbox v-model="menuSettings.addressSetting.deviceDetect">
                  <span v-text="$t('dialog.deviceDetect')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="addressSetting.deviceRemoteDeath">
                <el-checkbox v-model="menuSettings.addressSetting.deviceRemoteDeath">
                  <span v-text="$t('dialog.deviceRemoteDeath')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="addressSetting.deviceActive">
                <el-checkbox v-model="menuSettings.addressSetting.deviceActive">
                  <span v-text="$t('dialog.deviceActive')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="addressSetting.remoteMonitor">
                <el-checkbox v-model="menuSettings.addressSetting.remoteMonitor">
                  <span v-text="$t('dialog.remoteMonitor')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
            <el-divider>
              <el-icon>
                <CaretBottom />
              </el-icon>
              <span v-text="$t('writeFreq.scan')" />
            </el-divider>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="scanAndCallRecord.scanEnable">
                <el-checkbox v-model="menuSettings.scanAndCallRecord.scanEnable">
                  <span v-text="$t('dialog.scanEnable')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="scanAndCallRecord.editScanList">
                <el-checkbox v-model="menuSettings.scanAndCallRecord.editScanList">
                  <span v-text="$t('dialog.editScanList')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
            <el-divider>
              <el-icon>
                <CaretBottom />
              </el-icon>
              <span v-text="$t('dialog.callRecord')" />
            </el-divider>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="scanAndCallRecord.missedRecord">
                <el-checkbox v-model="menuSettings.scanAndCallRecord.missedRecord">
                  <span v-text="$t('dialog.missedCall')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="scanAndCallRecord.receivedRecord">
                <el-checkbox v-model="menuSettings.scanAndCallRecord.receivedRecord">
                  <span v-text="$t('dialog.answeredCall')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="scanAndCallRecord.outgoingRecord">
                <el-checkbox v-model="menuSettings.scanAndCallRecord.outgoingRecord">
                  <span v-text="$t('dialog.outgoingCall')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
            <el-divider>
              <el-icon>
                <CaretBottom />
              </el-icon>
              <span v-text="$t('header.setting')" />
            </el-divider>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="soundLightPassword.locale">
                <el-checkbox v-model="menuSettings.soundLightPassword.locale">
                  <span v-text="$t('writeFreq.langEnv')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="scanAndCallRecord.offline">
                <el-checkbox v-model="menuSettings.scanAndCallRecord.offline">
                  <span v-text="$t('dialog.offNetwork')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="scanAndCallRecord.toneTip">
                <el-checkbox v-model="menuSettings.scanAndCallRecord.toneTip">
                  <span v-text="$t('dialog.toneOrTip')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="scanAndCallRecord.transmitPower">
                <el-checkbox v-model="menuSettings.scanAndCallRecord.transmitPower">
                  <span v-text="$t('dialog.txPower')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="soundLightPassword.backLight">
                <el-checkbox v-model="menuSettings.soundLightPassword.backLight">
                  <span v-text="$t('dialog.backlight')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="soundLightPassword.bootInterface">
                <el-checkbox v-model="menuSettings.soundLightPassword.bootInterface">
                  <span v-text="$t('dialog.bootInterface')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="soundLightPassword.powerOnPassword">
                <el-checkbox v-model="menuSettings.soundLightPassword.powerOnPassword" :disabled="generalSettings.powerOnPassword.length !== 6">
                  <span v-text="$t('dialog.powerOnPwd')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="soundLightPassword.keyboardLock">
                <el-checkbox v-model="menuSettings.soundLightPassword.keyboardLock">
                  <span v-text="$t('dialog.keyboardLock')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="soundLightPassword.ledIndicator">
                <el-checkbox v-model="menuSettings.soundLightPassword.ledIndicator">
                  <span v-text="$t('dialog.ledIndicator')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="soundLightPassword.quieting">
                <el-checkbox v-model="menuSettings.soundLightPassword.quieting">
                  <span v-text="$t('dialog.Quieting')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="soundLightPassword.soundCtrl">
                <el-checkbox v-model="menuSettings.soundLightPassword.soundCtrl">
                  <span v-text="$t('dialog.voiceControl')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="timeModeGps.chLockEnable">
                <el-checkbox v-model="menuSettings.timeModeGps.chLockEnable">
                  <span v-text="$t('writeFreq.softKeyFuncDefine.CH_LOCK_SW')" />
                </el-checkbox>
              </el-form-item>
            </el-col>

            <el-col v-if="isLConfig" :xs="24" :sm="12">
              <el-form-item prop="timeModeGps.recording">
                <el-checkbox v-model="menuSettings.timeModeGps.recording">
                  <span v-text="$t('dialog.recording')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col v-if="isLConfig" :xs="24" :sm="12">
              <el-form-item prop="timeModeGps.uDiskMode">
                <el-checkbox v-model="menuSettings.timeModeGps.uDiskMode">
                  <span v-text="$t('dialog.uDiskMode')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col v-if="isGConfig" :xs="24" :sm="12">
              <el-form-item prop="timeModeGps.gps">
                <el-checkbox v-model="menuSettings.timeModeGps.gps">
                  <span v-text="$t('dialog.satellitePosition')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col v-if="isGConfig || isLConfig" :xs="24" :sm="12">
              <el-form-item prop="timeModeGps.timeSetting">
                <el-checkbox v-model="menuSettings.timeModeGps.timeSetting">
                  <span v-text="$t('dialog.timeSetting')" />
                </el-checkbox>
              </el-form-item>
            </el-col>

            <el-col :xs="24" :sm="12">
              <el-form-item prop="timeModeGps.roamInterface">
                <el-checkbox v-model="menuSettings.timeModeGps.roamInterface">
                  <span v-text="$t('dialog.roamInterface')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
          </el-row>

          <el-row :gutter="20" class="no-margin-x" type="flex" align="middle">
            <el-divider>
              <el-icon>
                <CaretBottom />
              </el-icon>
              <span v-text="$t('writeFreq.interphoneConfig')" />
            </el-divider>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="channelSetting.chConfigEnable">
                <el-checkbox v-model="menuSettings.channelSetting.chConfigEnable">
                  <span v-text="$t('dialog.chConfigSwitch')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="channelSetting.receivingFrequency">
                <el-checkbox v-model="menuSettings.channelSetting.receivingFrequency">
                  <span v-text="$t('dialog.receiveFrequency')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="channelSetting.transmittingFrequency">
                <el-checkbox v-model="menuSettings.channelSetting.transmittingFrequency">
                  <span v-text="$t('dialog.transFrequency')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="channelSetting.channelName">
                <el-checkbox v-model="menuSettings.channelSetting.channelName">
                  <span v-text="$t('dialog.chName')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="channelSetting.roamInterface">
                <el-checkbox v-model="menuSettings.channelSetting.roamInterface">
                  <span v-text="$t('dialog.transTimeLimit')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="channelSetting.subAudioSetting">
                <el-checkbox v-model="menuSettings.channelSetting.subAudioSetting">
                  <span v-text="$t('dialog.subaudioSetting')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="channelSetting.launchContact">
                <el-checkbox v-model="menuSettings.channelSetting.launchContact">
                  <span v-text="$t('dialog.launchContact')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="channelSetting.colorCode">
                <el-checkbox v-model="menuSettings.channelSetting.colorCode">
                  <span v-text="$t('dialog.colorCodes')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24" :sm="12">
              <el-form-item prop="timeSlotSetting.timeSlot">
                <el-checkbox v-model="menuSettings.timeSlotSetting.timeSlot">
                  <span v-text="$t('dialog.timeSlots')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <!--            <el-col :xs="24" :sm="12" v-if='false'>-->
            <!--              <el-form-item prop="timeSlotSetting.virtualClusterTimeSlot">-->
            <!--                <el-checkbox v-model="menuSettings.timeSlotSetting.virtualClusterTimeSlot">-->
            <!--                  <span v-text="$t('dialog.virtualTimeSlot')"></span>-->
            <!--                </el-checkbox>-->
            <!--              </el-form-item>-->
            <!--            </el-col>-->
            <el-col :xs="24" :sm="12">
              <el-form-item prop="timeSlotSetting.receivingList">
                <el-checkbox v-model="menuSettings.timeSlotSetting.receivingList">
                  <span v-text="$t('dialog.receivingList')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-tab-pane>
      <!--卫星定位-->
      <el-tab-pane
        v-if="isGConfig"
        lazy
        :label="$t('dialog.satellitePosition')"
        name="gpsData"
        class="gpsData-box flex justify-center settings-box one-column-box"
      >
        <el-form ref="gpsData" class="gpsData-form" :model="gpsData" :label-width="gpsDataLabelWidth">
          <el-row :gutter="20" class="no-margin-x">
            <el-col :xs="24">
              <el-form-item>
                <el-checkbox v-model="gpsData.gpsSettings.enable">
                  <span v-text="$t('writeFreq.gpsEnable')" />
                </el-checkbox>
              </el-form-item>
            </el-col>
            <el-col :xs="24">
              <el-form-item :label="$t('writeFreq.gpsMode')">
                <el-select
                  v-model="gpsData.gpsSettings.mode"
                  :disabled="!gpsData.gpsSettings.enable"
                  :placeholder="$t('dialog.select')"
                  filterable
                  :no-match-text="$t('dialog.noMatchText')"
                >
                  <el-option v-for="(item, i) in gpsModeList" :key="i" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="24">
              <el-form-item :label="$t('writeFreq.connectionTimes')">
                <el-input-number v-model="gpsData.connectionCount" :disabled="!gpsData.gpsSettings.enable" step-strictly :min="0" :max="255" :step="1" />
              </el-form-item>
            </el-col>
            <el-col :xs="24">
              <el-form-item :label="$t('writeFreq.pttTimes')">
                <el-input-number v-model="gpsData.pttCount" :disabled="!gpsData.gpsSettings.enable" step-strictly :min="0" :max="255" :step="1" />
              </el-form-item>
            </el-col>
            <el-col :xs="24">
              <el-form-item :label="$t('writeFreq.controlCenter')">
                <el-input-number v-model="gpsData.centerId" :disabled="!gpsData.gpsSettings.enable" step-strictly :min="1" :max="16777215" :step="1" />
              </el-form-item>
            </el-col>
            <el-col :xs="24">
              <el-form-item :label="$t('writeFreq.queryCommand')">
                <el-input
                  v-model="gpsData.queryCmd"
                  :disabled="!gpsData.gpsSettings.enable"
                  @input="val => (gpsData.queryCmd = gpsSettingsQueryCmdInputEvent(val))"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-tab-pane>
      <!--信令系统-->
      <el-tab-pane lazy :label="$t('writeFreq.signalingSystem')" name="signalingSystem" class="h-full has-tabs-child">
        <el-tabs class="signalingSystem-tabs settings-tabs-box h-full" type="border-card" model-value="signalingConfig">
          <el-tab-pane :label="$t('dialog.configure')" name="signalingConfig" class="flex justify-center h-full settings-box">
            <el-form class="grid grid-cols-2 gap-2 full-width-form signaling-config-form" :model="signalingSystem" label-position="top">
              <el-form-item>
                <el-checkbox v-model="signalingSystem.remoteDeathDecode">
                  <span v-text="$t('writeFreq.deviceRemoteDeadDecode')" />
                </el-checkbox>
              </el-form-item>
              <el-form-item>
                <el-checkbox v-model="signalingSystem.remoteMonitorDecode">
                  <span v-text="$t('writeFreq.remoteMonitorDecode')" />
                </el-checkbox>
              </el-form-item>
              <el-form-item :label="$t('writeFreq.remoteMonitorDuration')">
                <el-input-number v-model="signalingSystem.remoteMonitorDuration" step-strictly :min="10" :max="120" :step="10" />
              </el-form-item>

              <template v-if="isWConfig">
                <el-divider class="col-span-2">
                  <el-icon>
                    <CaretBottom />
                  </el-icon>
                  <span v-text="$t('writeFreq.workAlone')" />
                </el-divider>

                <el-form-item>
                  <el-checkbox v-model="signalingSystem.aloneWorkEnable">
                    <span v-text="$t('writeFreq.workAlone')" />
                  </el-checkbox>
                </el-form-item>

                <el-form-item :label="$t('writeFreq.workResTimeAlone')">
                  <el-input-number
                    v-model="signalingSystem.aloneWorkTime"
                    :disabled="workAloneUnEnable"
                    step-strictly
                    :min="0"
                    :max="255"
                    :step="1"
                    @change="aloneWorkTimeChange"
                  />
                </el-form-item>

                <el-form-item :label="$t('writeFreq.workAloneReminderTime')">
                  <el-input-number
                    v-model="signalingSystem.aloneWorkRemindTime"
                    :disabled="workAloneUnEnable"
                    step-strictly
                    :min="0"
                    :max="maxAloneWorkRemindTime"
                    :step="1"
                  />
                </el-form-item>

                <el-form-item :label="$t('writeFreq.workResOptAlone')">
                  <el-select
                    v-model="signalingSystem.aloneWorkResOpt"
                    :placeholder="$t('dialog.select')"
                    :disabled="workAloneUnEnable"
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option v-for="(item, i) in aloneWorkOptList" :key="i" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item>
              </template>

              <template v-if="isBConfig">
                <el-divider class="col-span-2">
                  <el-icon>
                    <CaretBottom />
                  </el-icon>
                  <span v-text="$t('writeFreq.runBackward')" />
                </el-divider>

                <el-form-item>
                  <el-checkbox v-model="signalingSystem.reversePlayEnable">
                    <span v-text="$t('writeFreq.runBackward')" />
                  </el-checkbox>
                </el-form-item>

                <el-form-item :label="$t('writeFreq.entryDelay')">
                  <el-input-number v-model="signalingSystem.entryDelay" :disabled="reversePlayUnEnable" step-strictly :min="5" :max="255" :step="1" />
                </el-form-item>

                <el-form-item :label="$t('writeFreq.exitDelay')">
                  <el-input-number v-model="signalingSystem.quitDelay" :disabled="reversePlayUnEnable" step-strictly :min="0" :max="255" :step="1" />
                </el-form-item>

                <el-form-item :label="$t('writeFreq.promptTimeBackwards')">
                  <el-input-number
                    v-model="signalingSystem.reversePlayOption.rewindTime"
                    :disabled="reversePlayUnEnable"
                    step-strictly
                    :min="0"
                    :max="10"
                    :step="1"
                  />
                </el-form-item>

                <el-form-item :label="$t('writeFreq.backwardsTriggered')">
                  <el-select
                    v-model="signalingSystem.reversePlayOption.triggerMode"
                    :disabled="reversePlayUnEnable"
                    :placeholder="$t('dialog.select')"
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option v-for="(item, i) in triggerModeList" :key="i" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item>

                <el-form-item :label="$t('writeFreq.triggerInclination')">
                  <el-select
                    v-model="signalingSystem.reversePlayOption.triggerTilt"
                    :disabled="disTriggerTilt"
                    :placeholder="$t('dialog.select')"
                    :no-match-text="$t('dialog.noMatchText')"
                  >
                    <el-option v-for="(item, i) in triggerTiltList" :key="i" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item>
              </template>
            </el-form>
          </el-tab-pane>
          <el-tab-pane
            :label="$t('writeFreq.digitalEmergencyAlarm')"
            name="digitAlarmList"
            class="h-full flex justify-center settings-box one-column-box digit-alarm"
          >
            <div class="label-list">
              <div
                v-for="(item, index) in digitAlarmList"
                :key="index"
                class="list-item"
                :class="{
                  selected: digitAlarmIndex === index,
                  disToggle: !digitAlarm.alarmName,
                }"
                @click="digitAlarmLabelClick(item, index)"
              >
                <span class="list-item-name" v-text="item.alarmName" />
                <el-button type="danger" icon="delete" class="list-item-close" size="small" circle @click.stop="deleteDigitAlarm(item, index)" />
              </div>
              <el-button type="primary" icon="circle-plus" class="new-list-item" :disabled="disAddDigital" @click="addOneDigitAlarm" />
            </div>
            <div v-if="digitAlarmList.length" class="digit-alarm-config">
              <el-form ref="digitAlarm" class="digit-alarm-config-form" :model="digitAlarm" :rules="digitAlarmRules" label-position="top">
                <el-row :gutter="20" class="no-margin-x">
                  <el-col :xs="24" :sm="responseColumn">
                    <el-form-item :label="$t('dialog.name')" prop="alarmName">
                      <el-input v-model="digitAlarm.alarmName" :maxlength="16" />
                    </el-form-item>
                  </el-col>
                  <el-col :xs="24" :sm="responseColumn">
                    <el-form-item :label="$t('dataTable.alarmType')">
                      <el-select v-model="digitAlarm.alarmType" :placeholder="$t('dialog.select')" :no-match-text="$t('dialog.noMatchText')">
                        <el-option v-for="(item, i) in digitAlarmTypeList" :key="i" :label="item.label" :value="item.value" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="24" :sm="responseColumn">
                    <el-form-item :label="$t('dialog.mode')">
                      <el-select
                        v-model="digitAlarm.alarmMode"
                        :disabled="forbidAlarm"
                        :placeholder="$t('dialog.select')"
                        :no-match-text="$t('dialog.noMatchText')"
                      >
                        <el-option v-for="(item, i) in digitAlarmModeList" :key="i" :label="item.label" :value="item.value" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="24" :sm="responseColumn">
                    <el-form-item :label="$t('writeFreq.replyChannel')">
                      <el-select
                        v-model="digitAlarm.replyChannel"
                        :disabled="forbidReplyChannel"
                        :placeholder="$t('dialog.select')"
                        :no-match-text="$t('dialog.noMatchText')"
                      >
                        <el-option v-for="(item, i) in replyChannelList" :key="i" :label="item.label" :value="item.value" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :xs="24" :sm="responseColumn">
                    <el-form-item :label="$t('writeFreq.notPoliteRetry')">
                      <el-input-number v-model="digitAlarm.impoliteRetry" :disabled="forbidAlarm" step-strictly :min="1" :max="15" :step="1" />
                    </el-form-item>
                  </el-col>
                  <el-col :xs="24" :sm="responseColumn">
                    <el-form-item :label="$t('writeFreq.politeRetry')">
                      <el-input-number v-model="digitAlarm.politeRetry" :disabled="forbidAlarm" step-strictly :min="0" :max="14" :step="1" />
                    </el-form-item>
                  </el-col>
                  <el-col :xs="24" :sm="responseColumn">
                    <el-form-item :label="$t('writeFreq.micActiveTime')">
                      <el-input-number v-model="digitAlarm.hotMicDuration" :disabled="disMicActiveTime" step-strictly :min="10" :max="120" :step="10" />
                    </el-form-item>
                  </el-col>
                </el-row>
              </el-form>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-tab-pane>
      <!--通讯录-->
      <el-tab-pane :label="$t('dialog.addressBook')" name="addressBook" class="h-full address-book">
        <addressBook
          :ref="addrBookTreeId"
          class="settings-box"
          :treeId="addrBookTreeId"
          :redrawTree="writerFrequencyTabName === 'addressBook'"
          :callTypes="addressBookCallTypes"
          @select="selectAddressBooks"
        />
      </el-tab-pane>
      <!--接收组列表-->
      <el-tab-pane :label="$t('dialog.rxGroup')" name="rxGroup" class="h-full">
        <receiveGroup
          :ref="refReceiveGroup"
          v-model="rxGroupList"
          :channels="selectedChannels"
          :addressTreeId="addrBookTreeId"
          :getDefaultAddress="getDefaultAddress"
          :getAddressName="getAddressNameByDmrId"
          :getOriginAddress="getOriginAddressBook"
        />
      </el-tab-pane>
      <!--信道设置-->
      <el-tab-pane :label="$t('dialog.channelSetting')" name="channelSettings" class="h-full has-tabs-child">
        <channelV9
          ref="channelV9"
          v-model:roamList="roamList"
          v-model:scanList="scanList"
          v-model:virtualCluster="virtualCluster"
          :minFrequency="deviceWriteInfo.minFrequency"
          :maxFrequency="deviceWriteInfo.maxFrequency"
          :zoneDataList="zoneDataList"
          :channelDataList="channelDataList"
          :rxGroupList="rxGroupList"
          :digitAlarmList="digitAlarmList"
          :selectedAddressBook="selectedAddressBook"
          :originAddressBook="originAddressBook"
          :encryptEnable="encryptEnable"
          :encryptXORList="encryptList"
          :encryptARC4List="encryptARC4List"
          :encryptAES256List="encryptAES256List"
          :version="channelVersion"
          :siteInfoList="siteInfoList"
        />
      </el-tab-pane>
      <!--扫描-->
      <el-tab-pane :label="$t('writeFreq.scan')" name="scanFunction" class="h-full has-tabs-child">
        <el-tabs class="scanFunction-tabs settings-tabs-box h-full" type="border-card" model-value="scanConfig">
          <el-tab-pane :label="$t('dialog.configure')" name="scanConfig" class="flex justify-center h-full settings-box one-column-box">
            <el-form class="fixed-width-form grid grid-cols-1 scan-config-form" :model="scanConfig" label-position="top" :label-width="scanConfigLabelWidth">
              <el-form-item :label="$t('dialog.hangTime')">
                <el-input-number v-model="scanConfig.scanHangTime" step-strictly :min="500" :max="10000" :step="500" />
              </el-form-item>
            </el-form>
          </el-tab-pane>
          <el-tab-pane :label="$t('writeFreq.scanningGroup')" name="scanningGroup" class="h-full scanning-group">
            <div class="label-list">
              <div
                v-for="(item, index) in scanList"
                :key="index"
                class="list-item"
                :class="{
                  selected: scanGroupIndex === index,
                  disToggle: !scanGroup.name,
                }"
                @click="scanGroupIndexLabelClick(item, index)"
              >
                <span class="list-item-name" v-text="item.name" />
                <el-button type="danger" icon="delete" class="list-item-close" size="small" circle @click.stop="deleteScanGroup(item, index)" />
              </div>
              <el-button type="primary" icon="circle-plus" class="new-list-item" :disabled="disAddScanGroup" @click="addOneScanGroup" />
            </div>
            <div v-if="scanList.length" class="config-container scanning-group-config">
              <el-form ref="scanGroup" class="scanning-group-config-form" :model="scanGroup" :rules="scanGroupRules" label-position="top">
                <el-form-item :class="['transfer-wrapper', locale]">
                  <el-transfer
                    v-model="scanGroup.membersList"
                    :titles="scanningGroupTransferTitles"
                    :data="availableChannelList"
                    :props="{
                      key: 'value',
                    }"
                    @change="scanListMembersChange"
                  >
                    <template #default="{ option }">
                      <span>{{ option.label }}</span>
                    </template>
                  </el-transfer>
                </el-form-item>
                <el-form-item>
                  <el-checkbox v-model="scanGroup.answer">
                    <span v-text="$t('dialog.answer')" />
                  </el-checkbox>
                </el-form-item>
                <el-form-item :label="$t('dialog.name')" prop="name">
                  <el-input v-model="scanGroup.name" :maxlength="16" />
                </el-form-item>
                <el-form-item :label="$t('writeFreq.specifyTransmitChannel')">
                  <el-select v-model="scanGroup.specifiedTrChID" :placeholder="$t('dialog.select')" filterable :no-match-text="$t('dialog.noMatchText')">
                    <el-option v-for="(item, i) in specifiedTrChIDList" :key="i" :label="item.label" :value="item.value" />
                  </el-select>
                </el-form-item>
              </el-form>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-tab-pane>
      <!--漫游-->
      <el-tab-pane v-if="isRConfig" :label="$t('writeFreq.roaming')" name="roamFunction" class="h-full has-tabs-child">
        <el-tabs class="roamFunction-tabs settings-tabs-box h-full" type="border-card" model-value="roamConfig">
          <el-tab-pane :label="$t('dialog.configure')" name="roamConfig" class="flex justify-center h-full settings-box one-column-box">
            <el-form class="roam-config-form" :model="roamConfig" label-position="top">
              <el-row :gutter="20" class="no-margin-x">
                <el-col :xs="24">
                  <el-form-item>
                    <el-checkbox v-model="roamConfig.activeSiteEnable">
                      <span v-text="$t('writeFreq.activeSiteEnable')" />
                    </el-checkbox>
                  </el-form-item>
                </el-col>
                <el-col :xs="24">
                  <el-form-item :label="$t('writeFreq.siteSearchTimer')">
                    <el-input-number v-model="roamConfig.siteSearchTime" step-strictly :min="0" :max="255" :step="1" />
                  </el-form-item>
                </el-col>
                <el-col :xs="24">
                  <el-form-item :label="$t('writeFreq.autoSiteSearchTimer')">
                    <el-input-number v-model="roamConfig.autoSiteSearchTime" step-strictly :min="1" :max="300" :step="1" />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-tab-pane>
          <el-tab-pane :label="$t('writeFreq.roamingGroup')" name="roamingGroup" class="h-full roaming-group">
            <div class="label-list">
              <div
                v-for="(item, index) in roamList"
                :key="index"
                class="list-item"
                :class="{
                  selected: roamGroupIndex === index,
                  disToggle: !roamGroup.name,
                }"
                @click="roamGroupIndexLabelClick(item, index)"
              >
                <span class="list-item-name" v-text="item.name" />
                <el-button type="danger" icon="delete" class="list-item-close" size="small" circle @click.stop="deleteRoamGroup(item, index)" />
              </div>
              <el-button type="primary" icon="circle-plus" class="new-list-item" :disabled="disAddRoamGroup" @click="addOneRoamGroup" />
            </div>
            <div v-if="roamList.length" class="config-container roaming-group-config">
              <el-form ref="roamGroup" class="roaming-group-config-form" :model="roamGroup" :rules="roamGroupRules" label-position="top">
                <el-form-item :class="['transfer-wrapper', locale]">
                  <el-transfer
                    v-model="roamGroup.memberList"
                    :titles="scanningGroupTransferTitles"
                    :data="roamGroupChannelList"
                    :props="{
                      key: 'value',
                    }"
                    @change="roamListMembersChange"
                  >
                    <template #default="{ option }">
                      <span>{{ option.label }}</span>
                    </template>
                  </el-transfer>
                </el-form-item>
                <el-form-item :label="$t('dialog.name')" prop="name">
                  <el-input v-model="roamGroup.name" :maxlength="16" />
                </el-form-item>
                <el-form-item :label="$t('dialog.rssiThreshold')">
                  <el-input-number v-model="roamGroup.rssi" step-strictly :min="-120" :max="-80" :step="1" />
                </el-form-item>
              </el-form>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-tab-pane>
      <!--电话本-->
      <el-tab-pane :label="$t('dialog.phoneBook')" name="phoneBook" class="h-full phoneBook-box-container">
        <phoneBook
          :ref="phoneBookTreeId"
          class="settings-box phoneBook-box"
          :treeId="phoneBookTreeId"
          :redrawTree="writerFrequencyTabName === 'phoneBook'"
          @select="selectPhoneBooks"
        />
      </el-tab-pane>
      <!--系统功能-->
      <el-tab-pane lazy :label="$t('writeFreq.systemFunction')" name="systemFunction" class="h-full patrol-system-box settings-box">
        <el-tabs v-model="patrolSystemTabName" class="patrol-system-tabs settings-tabs-box" type="border-card">
          <el-tab-pane :label="$t('dialog.configure')" name="configure" class="h-full">
            <patrolConfig v-model="patrolConfig" />
          </el-tab-pane>
          <el-tab-pane lazy :label="$t('dialog.emergency')" name="emergency" class="h-full">
            <emergencyAlarmConfig v-model="emergencyAlarm" :addressBooks="selectedAddressBook" />
          </el-tab-pane>
          <el-tab-pane lazy :label="$t('dialog.trailCtrl')" name="trailCtrl" class="h-full">
            <el-form ref="patrolSystemTrailCtrl" class="patrol-system-trailCtrl-form" :model="trackMonitor" label-width="95px" label-position="top">
              <el-row :gutter="20" class="no-margin-x">
                <el-col :xs="24">
                  <el-form-item>
                    <el-checkbox v-model="trackMonitor.trackEnable">
                      <span v-text="$t('writeFreq.enable')" />
                    </el-checkbox>
                  </el-form-item>
                </el-col>
                <el-col :xs="24">
                  <el-form-item :label="$t('dialog.trailSpacing')">
                    <el-input-number v-model="trackMonitor.rollTime" :disabled="!trackMonitor.trackEnable" step-strictly :min="0" :max="9995" :step="5" />
                  </el-form-item>
                </el-col>
                <el-col :xs="24">
                  <el-form-item :label="$t('dialog.shortestDistance')">
                    <el-input-number v-model="trackMonitor.rollDistant" :disabled="!trackMonitor.trackEnable" step-strictly :min="0" :max="495" :step="5" />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </el-tab-pane>
        </el-tabs>
      </el-tab-pane>
      <!--虚拟集群-->
      <el-tab-pane :label="$t('writeFreq.virtualCluster')" name="virtualCluster" class="virtual-cluster-box settings-box" lazy>
        <virtualClusterV0
          ref="virtualCluster"
          v-model="virtualCluster"
          labelPosition="top"
          :selectDeviceData="selectDeviceData"
          :selectedAddressBook="selectedAddressBook"
          :channelDataList="channelDataList"
          :version="virtualClusterVersion"
        />
      </el-tab-pane>
      <!-- 站点信息 -->
      <el-tab-pane :label="$t('writeFreq.siteInfo')" name="siteInfo" class="site-info-container settings-box has-tabs-child" lazy>
        <div class="label-list">
          <div
            v-for="(item, index) in siteInfoList"
            :key="item.name"
            class="list-item"
            :class="{
              selected: siteInfoIndex === index,
              disToggle: !item.name,
            }"
            @click="siteInfoLabelClick(item, index)"
          >
            <span class="list-item-name" v-text="item.name" />
            <el-button type="danger" icon="delete" class="list-item-close" size="small" circle @click.stop="deleteSiteInfo(item, index)" />
          </div>
          <el-button type="primary" icon="circle-plus" class="new-list-item" :disabled="siteInfoList.length >= siteInfoLimit" @click="addOneSiteInfo" />
        </div>
        <div v-if="siteInfoList.length" class="config-container">
          <SiteInfo_511svt ref="SiteInfo" v-model="siteInfoList[siteInfoIndex]" @update:model-value="siteInfoListChange" />
        </div>
      </el-tab-pane>
    </el-tabs>
    <write-freq-footer
      :is-reading="isReading"
      :is-writing="isWriting"
      :disable-read="disReadBtn"
      :disable-write="disWriteBtn"
      @new-config="newConfig"
      @read-config="readDataConfig"
      @write-config="writeInFrequency"
      @export-config="exportConfig"
      @import-config="importConfig"
    />
  </div>
</template>

<script>
  import { cloneDeep, merge } from 'lodash'
  import bftree from '@/utils/bftree'
  import bfutil, { DeviceTypes } from '@/utils/bfutil'
  import bfNotify, { messageBox, Types } from '@/utils/notify'
  import bfStorage from '@/utils/storage'
  import validateRules from '@/utils/validateRules'
  import { AddressBookCallTypes, ExportStructIndex, getClassInstance, Model, SoftKeyCallType, SoftKeyFuncDefine } from '@/writingFrequency/interphone/511SVT00'
  import commonToolMixin from '@/writingFrequency/interphone/commonToolMixin'
  import wfTool from '@/writingFrequency/interphone/tool'
  import { SupportedLang, SupportedLangList } from '@/modules/i18n'
  import {
    filterChannelIdWhenDeviceChanged,
    fixPowerOnPassword,
    fixProgramPassword,
    fixVoiceEncryptKey,
    resetDigitalAlertReplyChannel,
    gpsSettingsQueryCmdInputEvent,
  } from '@/writingFrequency/interphone/common'
  import { getDefaultChannel } from '@/platform/dataManage/deviceManage/common/channel/channel'
  import deviceInfo from '@/platform/dataManage/deviceManage/common/deviceInfo'
  import selectDevice from '@/platform/dataManage/deviceManage/common/selectDevice'
  import WriteFreqFooter from '@/platform/dataManage/deviceManage/common/writeFreqFooter.vue'
  import { defineAsyncComponent } from 'vue'

  const timeZoneKeys = ['timeZoneHour', 'timeZoneMinute', 'second', 'minute', 'hour', 'day', 'month', 'year']

  const Password = {
    md5Key: '',
  }
  const DefaultFrequency = {
    value: 400000000,
    label: '400',
  }
  const GeneralSettings = {
    deviceName: '',
    ids: 0,
    repeaterId: 16776415,
    soundCtrlLevel: 0,
    soundCtrlDelay: 500,
    sendLeadCodeTime: 960,
    offlineGroupCallHungTime: 2000,
    offlineSingleCallHungTime: 2000,
    powerInfoAlert: 120,
    channelSignal: {
      afterDecodeSignalNotify: false,
      muteAll: false,
      allowCallInstruction: 2,
      analogTransmitSignaling: false,
      channelFreeNotice: false,
      voiceNotice: true,
    },
    second: 0,
    minute: 0,
    hour: 0,
    day: 0,
    month: 0,
    year: 0,
    timeZoneHour: 0,
    timeZoneMinute: 0,
    recordFunc: {
      recordEnable: false,
      recordCompressionRatio: 0,
      allowErasing: false,
      gpsEnable: true,
      bdsEnable: true,
    },
    powerBluetooth: {
      savePowerMode: 2,
      blueToothSw: false,
      blueToothPttKeepEn: false,
    },
    soundAndDisplayTip: {
      locale: 0,
      disabledAllLED: false,
      freqDisplay: 2,
      rejectUnfamiliarCall: false,
      directMode: false,
      menuOff: false,
      showAlias: true,
    },
    soundEncryptType: 0,
    soundEncryptValue: '',
    uDiskModePassword: '',
    channelConfigPassword: '',
    powerOnPassword: '',
  }
  const ButtonDefined = {
    longPressDuration: 1000,
    oneTouchFuncCall: [
      {
        addrId: 65535,
        optType: 1,
        smsId: 255,
      },
      {
        addrId: 65535,
        optType: 1,
        smsId: 255,
      },
      {
        addrId: 65535,
        optType: 1,
        smsId: 255,
      },
      {
        addrId: 65535,
        optType: 1,
        smsId: 255,
      },
      {
        addrId: 65535,
        optType: 1,
        smsId: 255,
      },
      {
        addrId: 65535,
        optType: 1,
        smsId: 255,
      },
    ],
    shortLongPressFuncDefine: [
      {
        short: 0,
        long: 0,
      },
      {
        short: 7,
        long: 26,
      },
      {
        short: 13,
        long: 11,
      },
    ],
  }
  const MenuSettings = {
    menuConfig: {
      menuHangTime: 10,
      idShownMinLen: 0,
    },
    addressSetting: {
      message: true,
      callTip: true,
      editAddressBook: true,
      manualDialing: true,
      deviceDetect: false,
      remoteMonitor: false,
      deviceActive: false,
      deviceRemoteDeath: false,
    },
    scanAndCallRecord: {
      scanEnable: true,
      editScanList: true,
      missedRecord: true,
      receivedRecord: true,
      outgoingRecord: true,
      offline: true,
      toneTip: true,
      transmitPower: true,
    },
    soundLightPassword: {
      backLight: true,
      bootInterface: true,
      keyboardLock: true,
      ledIndicator: false,
      quieting: true,
      powerOnPassword: false,
      locale: true,
      soundCtrl: true,
    },
    timeModeGps: {
      recording: true,
      uDiskMode: true,
      gps: true,
      timeSetting: true,
      roamInterface: true,
      chLockEnable: true,
    },
    channelSetting: {
      chConfigEnable: true,
      receivingFrequency: true,
      transmittingFrequency: true,
      channelName: true,
      roamInterface: true,
      subAudioSetting: true,
      launchContact: true,
      colorCode: true,
    },
    timeSlotSetting: {
      timeSlot: true,
      virtualClusterTimeSlot: true,
      receivingList: true,
      quickDailEnable: true,
    },
  }
  const GPSData = {
    gpsSettings: {
      hourDiff: 8,
      minuteDiff: 0,
      areaId: 0,
      earlyOrLate: 1,
      enable: true,
      mode: 1,
    },
    centerId: 1,
    connectionCount: 8,
    pttCount: 10,
    queryCmd: '',
  }
  const TrackMonitor = {
    rollTime: 300,
    rollDistant: 50,
    trackEnable: true,
  }
  const ScanConfig = {
    scanHangTime: 4000,
    priorityTone: 1,
  }
  const OneScanGroup = {
    groupId: 0,
    membersList: [0xfffe],
    firstPriorityChannel: 0xffff,
    secondPriorityChannel: 0xffff,
    prioritySamplingTime: 2000,
    channelCount: 1,
    answer: true,
    specifiedTrChID: 0xfffe,
    name: '',
  }
  const RoamConfig = {
    activeSiteEnable: false,
    siteSearchTime: 30,
    autoSiteSearchTime: 60,
  }
  const OneRoamGroup = {
    roamId: 0,
    memberCount: 1,
    rssi: -120,
    memberList: [0xfffe],
    name: '漫游组 1',
  }
  const SignalingSystem = {
    remoteDeathDecode: true,
    remoteMonitorDecode: false,
    urgentRemoteMonitorDecode: 1,
    remoteMonitorDuration: 10,
    aloneWorkEnable: false,
    aloneWorkTime: 10,
    aloneWorkRemindTime: 10,
    aloneWorkResOpt: 0,
    reversePlayEnable: false,
    entryDelay: 10,
    quitDelay: 10,
    reversePlayOption: {
      rewindTime: 5,
      triggerTilt: 0,
      triggerMode: 0,
    },
  }
  const DigitAlarm = {
    alarmID: 0,
    alarmType: 0,
    alarmMode: 0,
    replyChannel: 0xffff,
    impoliteRetry: 15,
    politeRetry: 5,
    hotMicDuration: 10,
    alarmName: '',
  }
  const ZoneData = {
    areaId: 0,
    areaName: '',
    chFlag: 0,
    chIdList: [],
    validFlag: 1,
  }

  // 加密配置
  const EncryptConfig = { config: { enable: false } }
  // 默认站点信息参数
  const SiteInfo = {
    id: 0,
    count: 1,
    txFreqList: [bfutil.frequencyMhz2Hz(401)],
    rxFreqList: [bfutil.frequencyMhz2Hz(400)],
    name: 'site info 1',
  }

  export default {
    name: 'TD511SVT',
    mixins: [commonToolMixin, wfTool],
    data() {
      // 默认显示高版本的信道
      const channelVersion = 10
      const channelData = getDefaultChannel(channelVersion, 0)

      return {
        selectedDeviceDmrId: '',
        writerFrequencyTabName: 'deviceWriteInfo',
        patrolSystemTabName: 'configure',
        defaultFrequency: cloneDeep(DefaultFrequency),
        optsFunType: 0,
        // 设备信息
        deviceWriteInfo: {
          config: {
            locate: true,
            recording: true,
            runBackward: true,
            workAlone: true,
            roaming: true,
            bluetooth: false,
          },
        },
        // 缓存身份信息
        identityInfo: {},
        // 编程密码
        passwordInfo: cloneDeep(Password),
        // 常规设置
        generalSettings: cloneDeep(GeneralSettings),
        // 按键设置
        buttonDefined: cloneDeep(ButtonDefined),
        // 菜单设置
        menuSettings: cloneDeep(MenuSettings),
        // 卫星定位
        gpsData: cloneDeep(GPSData),
        // 系统功能巡查配置
        patrolConfig: {},
        // 系统功能紧急报警,
        emergencyAlarm: {},
        // 系统功能自动定位监控
        trackMonitor: cloneDeep(TrackMonitor),
        // 扫描
        scanConfig: cloneDeep(ScanConfig),
        scanList: [],
        scanGroup: cloneDeep(OneScanGroup),
        scanGroupIndex: -1,
        scanGroupLimit: 32,
        oneScanGroupLimit: 16,
        // 漫游
        roamConfig: cloneDeep(RoamConfig),
        roamList: [],
        roamGroup: cloneDeep(OneRoamGroup),
        roamGroupIndex: -1,
        roamGroupLimit: 32,
        oneRoamGroupLimit: 16,
        // 信令系统
        signalingSystem: cloneDeep(SignalingSystem),
        // 数字报警
        digitAlarm: cloneDeep(DigitAlarm),
        digitAlarmList: [],
        digitAlarmIndex: -1,
        digitAlarmLimit: 4,
        // 区域列表
        zoneDataList: [],
        zoneDataLimit: 32,
        zoneDataCache: undefined,
        // 信道与区域关系索引
        channelZoneIndex: {},
        // 信道设置
        channelVersion,
        channelData,
        channelDataList: [],
        // 缓存读取回来的信道，以便在切换选中终端时，将相同ID的信道参数合并
        originChannelDataList: [],
        channelLimit: 16,
        // 信道表格右键菜单事件数据源缓存
        channelCache: undefined,
        // 频率偏移值
        freqOffset: 0,

        selectedAddressBook: [],
        addressBookCache: [],
        originAddressBook: [],
        addrBookTreeId: 'TD511AddressBookTree',

        // 接收组配置
        rxGroupList: [],
        refReceiveGroup: 'receiveGroup',

        // 电话本
        phoneBookTreeId: 'TD511PhoneBook',
        phoneBook: [],

        // 多个数据的结构写频时，几个写一次
        wfCountLimit: 3,
        availableChannels: {
          areaChannelIndexTable: [],
        },
        // 短信内容
        smsContent: [],
        refSms: 'shortMessage',
        // 信道总数
        totalChannels: 38,
        readCount: 0,
        isReading: false,
        isWriting: false,
        readWriteFlag: '',
        // 默认的信道配置
        defaultChannelConfig: [],

        // 加密配置
        encryptConfig: cloneDeep(EncryptConfig),
        encryptListLimit: 32,
        encryptList: [],
        encryptARC4List: [],
        encryptAES256List: [],

        // 虚拟集群
        virtualCluster: {
          vcGroupId: 0xffff,
          rssiValue: 0,
          siteListMemCount: 1,
          authKey: ''.padEnd(32, 'F'),
          siteList: [0xfffe],
        },
        virtualClusterVersion: 1,

        //  站点信息
        siteInfoList: [],
        siteInfoIndex: 0,
        siteInfoLimit: 16,
      }
    },
    methods: {
      /**
       * 当站点信息列表删除其中一个站点配置，需要同步检查所有信道使用该配置的参数
       * @param siteInfo 被删除的站点信息
       */
      syncChannelSvtSiteInfoWhenDeleteSiteInfo(siteInfo) {
        for (let i = 0; i < this.channelDataList.length; i++) {
          const channel = this.channelDataList[i]
          if (siteInfo.id !== channel.subChannelData.svtSiteInfo) {
            continue
          }
          channel.subChannelData.svtSiteInfo = 0xff
        }
      },
      /**
       * 站点信息列表参数变更事件，需要检查所有信道的虚拟集群站点配置
       * @param siteInfo 当前编辑的站点信息
       */
      siteInfoListChange(siteInfo) {
        for (let i = 0; i < this.channelDataList.length; i++) {
          const channel = this.channelDataList[i]
          if (siteInfo.id !== channel.subChannelData.svtSiteInfo) {
            continue
          }

          // 当前信道的发射与接收频率
          const txFreq = channel.transmittingFrequency
          const rxFreq = channel.receivingFrequency
          // 判断站点信息中对应的频率是否与当前信道的频率相同，如果不同，重置信道的站点信息配置参数
          const txFreqIndex = siteInfo.txFreqList.findIndex(freq => freq === txFreq)
          if (txFreqIndex === -1 || siteInfo.rxFreqList[txFreqIndex] !== rxFreq) {
            channel.subChannelData.svtSiteInfo = 0xff
          }
        }
      },
      initSiteInfoList() {
        this.siteInfoList = []
        this.siteInfoIndex = 0
        this.addOneSiteInfo()
      },
      newSiteInfo() {
        const idList = this.siteInfoList.map(v => {
          return v.id
        })
        const id = this.getNextId(idList, this.siteInfoLimit)
        const data = cloneDeep(SiteInfo)
        data.id = id
        data.name = `${this.$t('writeFreq.siteInfo')} ${id + 1}`
        return data
      },
      addOneSiteInfo() {
        const data = this.newSiteInfo()
        this.siteInfoList[this.siteInfoList.length] = data
        this.siteInfoIndex = this.siteInfoList.length - 1
      },
      siteInfoLabelClick(data, index) {
        this.siteInfoIndex = index
      },
      deleteSiteInfo(data, index) {
        if (this.siteInfoList.length === 1) {
          return
        }

        this.siteInfoList.splice(index, 1)
        this.siteInfoIndex = index === this.siteInfoList.length ? index - 1 : index

        this.syncChannelSvtSiteInfoWhenDeleteSiteInfo(data)
      },
      gpsSettingsQueryCmdInputEvent,
      aloneWorkTimeChange() {
        // 判断是否要重置提醒时间
        if (this.signalingSystem.aloneWorkRemindTime > this.maxAloneWorkRemindTime) {
          this.signalingSystem.aloneWorkRemindTime = this.maxAloneWorkRemindTime
        }
      },
      fixMd5KeyValue(value) {
        this.passwordInfo.md5Key = fixProgramPassword(value)
      },
      fixSoundEncryptValue(value) {
        this.generalSettings.soundEncryptValue = fixVoiceEncryptKey(value)
      },
      fixPowerOnValue(value) {
        this.generalSettings.powerOnPassword = fixPowerOnPassword(value)
      },
      getAddressByDmrId(dmrId) {
        for (let i = 0; i < this.selectedAddressBook.length; i++) {
          const item = this.selectedAddressBook[i]
          if (item.dmrId === dmrId) {
            return item
          }
        }
        return undefined
      },
      getAddressBookFromCache(addrId) {
        for (let i = 0; i < this.addressBookCache.length; i++) {
          const book = this.addressBookCache[i]
          if (book.id === addrId) {
            return book
          }
        }
        return undefined
      },
      getSmsById(msgId) {
        for (let i = 0; i < this.smsContent.length; i++) {
          const sms = this.smsContent[i]
          if (sms.msgId === msgId) {
            return sms
          }
        }
        return undefined
      },
      resetButtonDefined(target) {
        target.addrId = 0xffff
        target.optType = SoftKeyCallType.GROUP
        target.smsId = 0xff

        return target
      },
      detectButtonDefined() {
        for (let i = 0; i < this.buttonDefined.oneTouchFuncCall.length; i++) {
          const item = this.buttonDefined.oneTouchFuncCall[i]
          // 如果对应的通讯录对象不存在，则将按键功能重置为默认值
          // 从缓存通讯录中查找的通讯录记录不在新的通讯录列表中，则重置按键功能
          const contactCache = this.getAddressBookFromCache(item.addrId)
          if (!contactCache) {
            this.resetButtonDefined(item)
            continue
          }
          const contact = this.getAddressByDmrId(contactCache.dmrId)
          if (!contact) {
            this.resetButtonDefined(item)
            continue
          }
          // 如果存在但ID已经变更，则更新按键功能通讯录ID
          if (contactCache.id !== contact.id) {
            item.addrId = contact.id
          }
        }
      },
      detectButtonDefinedFromSmsChange() {
        for (let i = 0; i < this.buttonDefined.oneTouchFuncCall.length; i++) {
          const item = this.buttonDefined.oneTouchFuncCall[i]
          if (item.optType !== 3) {
            continue
          }
          // 如果按键定义中的短信ID对应的短信内容已经不存在，则按键功能短信ID重置为默认值
          const sms = this.getSmsById(item.smsId)
          if (!sms) {
            // 如果对应的通讯录对象不存在，则将按键功能重置为默认值
            const contact = this.getSelectedAddress(item.addrId)
            if (!contact) {
              this.resetButtonDefined(item)
              continue
            }
            item.smsId = 0xff

            // 重置按键功能类型参数
            if (contact.callType === AddressBookCallTypes.SINGLE) {
              item.optType = SoftKeyCallType.SINGLE
            } else {
              item.optType = SoftKeyCallType.GROUP
            }
          }
        }
      },
      selectPhoneBooks(books) {
        this.phoneBook = books
      },
      selectAddressBooks(books) {
        this.addressBookCache = cloneDeep(this.selectedAddressBook)
        this.selectedAddressBook = books
        // 通讯录变化时，检测按键定义中单键呼叫功能的设置
        this.detectButtonDefined()
      },
      getNextId(idList, limit = 0xffff) {
        if (!Array.isArray(idList)) {
          return 0
        }

        let id = 0
        while (id < limit) {
          if (!idList.includes(id)) {
            return id
          }
          id++
        }
        return id
      },
      getZoneDataByRid(rid = bfutil.DefOrgRid) {
        for (let i = 0; i < this.zoneDataList.length; i++) {
          const zoneData = this.zoneDataList[i]
          if (zoneData.zoneRid === rid) {
            return zoneData
          }
        }

        return undefined
      },
      newZoneData() {
        const idList = this.zoneDataList.map(v => {
          return v.areaId
        })
        const id = this.getNextId(idList, this.zoneDataLimit)
        const zoneData = cloneDeep(ZoneData)
        zoneData.areaId = id
        zoneData.areaName = `${this.$t('dialog.area')} ${id + 1}`
        return zoneData
      },

      getOriginChannelById(id) {
        for (let i = 0; i < this.originChannelDataList.length; i++) {
          const channel = this.originChannelDataList[i]
          if (channel.chId === id) {
            return cloneDeep(channel)
          }
        }
        return {}
      },
      newChannel(chType = 0) {
        const idList = this.channelDataList.map(v => {
          return v.chId
        })
        const id = this.getNextId(idList, this.channelLimit * this.zoneDataLimit)
        const channel = getDefaultChannel(this.channelVersion, chType)
        channel.chId = id
        channel.chName = `${this.$t('dialog.channel')} ${id + 1}`
        // 重置信道默认频率
        const minFreq = this.frequencyMhz2Hz(this.deviceWriteInfo.minFrequency)
        channel.receivingFrequency = minFreq || channel.receivingFrequency
        channel.transmittingFrequency = minFreq || channel.transmittingFrequency

        return channel
      },
      addChannelToList(channel) {
        this.channelDataList[this.channelDataList.length] = channel
      },
      initZoneChannel() {
        // 如果没有合法有效的信道，则向用户提示
        if (!this.selectedChannels.length) {
          this.$nextTick(() => {
            bfNotify.messageBox(this.$t('writeFreq.notHaveValidChannel'), 'error')
          })
          return
        }

        // 遍历终端设置的信道参数，生成写频功能的信道数据
        let isNotSetZone = false
        const channelIdLimit = this.zoneDataLimit * this.channelLimit
        const notSetZones = []
        const zoneConfigCache = {}
        // 区域下辖信道上限
        const countLimit = 16
        const channelOverflow = []
        for (let i = 0; i < this.selectedChannels.length; i++) {
          const item = this.selectedChannels[i]
          const zoneRid = item.zoneRid
          // 跳过没有区域数据的信道，并在遍历结束后，向用户提示
          // 缓存信道区域配置，同一个区域配置只查找一次
          const channelZone = zoneConfigCache[zoneRid] || bfglob.gchannelZone.get(zoneRid)
          if (!channelZone || zoneRid === bfutil.DefOrgRid) {
            isNotSetZone = true
            notSetZones.push(item)
            continue
          }
          zoneConfigCache[zoneRid] = channelZone

          let channel = this.newChannel()
          const chId = item.no - 1
          // 判断信道ID是否超出机型的信道范围
          if (chId >= channelIdLimit) {
            continue
          }
          channel.chId = chId
          channel.chName = `${this.$t('dialog.channel')} ${item.no}`
          // 合并读取的信道参数
          channel = merge(channel, this.getOriginChannelById(chId) || cloneDeep(this.channelData))
          channel.subChannelData.receiveGroup = this.getRxGroupId(channel.chId)
          channel.subChannelData.defaultAddress = this.getDefaultAddress(item.sendGroup)
          this.addChannelToList(channel)
          // 将信道ID与区域数据相联，如果没有对应的区域，则先创建区域
          let zoneData = this.getZoneDataByRid(channelZone.rid)
          if (!zoneData) {
            zoneData = this.newZoneData()
            zoneData.areaName = channelZone.zoneTitle
            this.zoneDataList[this.zoneDataList.length] = zoneData
          }
          // 判断是否超出区域上限
          if (zoneData.chIdList.length >= countLimit) {
            if (!channelOverflow.some(item => item === channelZone.zoneTitle)) {
              channelOverflow.push(channelZone.zoneTitle)
            }
            continue
          }

          zoneData.zoneRid = channelZone.rid
          zoneData.chIdList.push(channel.chId)
          zoneData.chFlag = this.setZoneChFlag(zoneData.chIdList)
        }

        // 同步信道设置页参数，需要等待子组件加载完成
        this.$nextTick(() => {
          if (this.channelDataList.length) {
            const firstChannel = this.channelDataList[0]
            this.$refs.channelV9.zoneChannelListClick(firstChannel)
          }
        })

        if (isNotSetZone) {
          let msg = this.$t('writeFreq.channelNotSetArea') + ': '
          msg += notSetZones
            .map(item => {
              return `${this.$t('dialog.channel')} ${item.no}`
            })
            .join(',')
          bfNotify.messageBox(msg, 'warning')
        }
        for (let i = 0; i < channelOverflow.length; i++) {
          ;(zoneTitle => {
            setTimeout(() => {
              const msg = this.$t('writeFreq.maxChannelLimit', {
                zoneTitle,
                count: countLimit,
              })
              bfNotify.messageBox(msg, 'warning')
            }, 0)
          })(channelOverflow[i])
        }
      },

      // 数字报警
      initDigitalAlarm() {
        const digitAlarm = this.newDigitAlarm()
        this.digitAlarm = digitAlarm
        this.asyncDigitWarning({ result: [digitAlarm] })
      },
      digitAlarmLabelClick(data, index) {
        if (!this.digitAlarm.alarmName) {
          return
        }
        this.digitAlarmIndex = index
        this.digitAlarm = data
      },
      deleteDigitAlarm(data, index) {
        if (this.digitAlarmList.length === 1) {
          return
        }

        this.digitAlarmList.splice(index, 1)
        this.digitAlarmIndex = index === this.digitAlarmList.length ? index - 1 : index
        this.digitAlarm = this.digitAlarmList[this.digitAlarmIndex]
      },
      newDigitAlarm() {
        const idList = this.digitAlarmList.map(v => {
          return v.alarmID
        })
        const id = this.getNextId(idList, this.digitAlarmLimit)
        const digitAlarm = cloneDeep(DigitAlarm)
        digitAlarm.alarmID = id
        digitAlarm.alarmName = `${this.$t('writeFreq.system')} ${digitAlarm.alarmID + 1}`
        return digitAlarm
      },
      addOneDigitAlarm() {
        const digitAlarm = this.newDigitAlarm()
        this.digitAlarmList[this.digitAlarmList.length] = digitAlarm
        this.digitAlarm = digitAlarm
        this.digitAlarmIndex = this.digitAlarmList.length - 1
      },

      // 扫描配置
      scanGroupIndexLabelClick(data, index) {
        if (!this.scanGroup.name) {
          return
        }
        this.scanGroupIndex = index
        this.scanGroup = data
      },
      deleteScanGroup(data, index) {
        if (this.scanList.length === 1) {
          return
        }

        this.scanList.splice(index, 1)
        this.scanGroupIndex = index === this.scanList.length ? index - 1 : index
        this.scanGroup = this.scanList[this.scanGroupIndex]
      },
      addOneScanGroup() {
        const oneScanGroup = this.newScanGroup()
        this.scanList[this.scanList.length] = oneScanGroup
        this.scanGroup = oneScanGroup
        this.scanGroupIndex = this.scanList.length - 1
      },
      newScanGroup() {
        const idList = this.scanList.map(v => {
          return v.groupId
        })
        const id = this.getNextId(idList, this.scanGroupLimit)
        const oneScanGroup = cloneDeep(OneScanGroup)
        oneScanGroup.groupId = id
        oneScanGroup.name = `${this.$t('writeFreq.scanningGroup')} ${oneScanGroup.groupId + 1}`
        return oneScanGroup
      },
      initScanGroup() {
        const oneScanGroup = this.newScanGroup()
        this.scanGroup = oneScanGroup
        this.asyncScanList({ result: [oneScanGroup] })
      },

      // 漫游配置
      roamGroupIndexLabelClick(data, index) {
        if (!this.roamGroup.name) {
          return
        }
        this.roamGroupIndex = index
        this.roamGroup = data
      },
      deleteRoamGroup(data, index) {
        if (this.roamList.length === 1) {
          return
        }

        this.roamList.splice(index, 1)
        this.roamGroupIndex = index === this.roamList.length ? index - 1 : index
        this.roamGroup = this.roamList[this.roamGroupIndex]
      },
      addOneRoamGroup() {
        const oneRoamGroup = this.newRoamGroup()
        this.roamList[this.roamList.length] = oneRoamGroup
        this.roamGroup = oneRoamGroup
        this.roamGroupIndex = this.roamList.length - 1
      },
      newRoamGroup() {
        const idList = this.roamList.map(v => {
          return v.roamId
        })
        const id = this.getNextId(idList, this.roamGroupLimit)
        const oneRoamGroup = cloneDeep(OneRoamGroup)
        oneRoamGroup.roamId = id
        oneRoamGroup.name = `${this.$t('writeFreq.roamingGroup')} ${id + 1}`
        return oneRoamGroup
      },
      initRoamGroup() {
        const oneRoamGroup = this.newRoamGroup()
        this.roamGroup = oneRoamGroup
        this.asyncRoamList({ result: [oneRoamGroup] })
      },
      roamListMembersChange(data) {
        if (data.length > this.oneRoamGroupLimit) {
          this.roamGroup.memberList = data.slice(0, this.oneRoamGroupLimit)
          messageBox(this.$t('writeFreq.fullList'), Types.warning)
        }

        this.roamGroup.memberCount = this.roamGroup.memberList.length
      },

      // 短信初始化
      initSmsData() {
        this.smsContent = []
      },

      // 以默认的配置覆盖当前的配置
      newConfig() {
        this.optsFunType = 0
        // 清除当前选择的终端设备
        this.clearSelectedDeviceDmrId()
        this.clearDeviceDataConfig(true)
        // 清除设备频率信息
        this.deviceWriteInfo = Object.assign(this.deviceWriteInfo, {
          maxFrequency: 0,
          minFrequency: 0,
        })
        // 常规设置
        this.generalSettings = cloneDeep(GeneralSettings)
        this.$refs.timeZone?.initTimeZone()
        // 按键设置
        this.buttonDefined = cloneDeep(ButtonDefined)
        // 菜单设置
        this.menuSettings = cloneDeep(MenuSettings)
        // 卫星定位
        this.gpsData = cloneDeep(GPSData)
        // 信令系统
        this.signalingSystem = cloneDeep(SignalingSystem)
        // 数字报警
        this.digitAlarmList = []
        this.digitAlarmIndex = -1
        this.initDigitalAlarm()
        // 区域列表
        this.zoneDataList = []
        this.zoneDataCache = undefined
        this.channelZoneIndex = {}
        // 信道设置
        this.channelVersion = 10
        this.channelData = getDefaultChannel(this.channelVersion, 0)
        this.channelDataList = []
        // 扫描
        this.scanConfig = cloneDeep(ScanConfig)
        this.scanList = []
        this.scanGroupIndex = -1
        this.initScanGroup()
        // 漫游
        this.roamConfig = cloneDeep(RoamConfig)
        this.roamList = []
        this.roamGroupIndex = -1
        this.initRoamGroup()
        // 系统功能巡查配置
        this.patrolConfig = {}
        // 系统功能紧急报警,
        this.emergencyAlarm = {}
        // 系统功能自动定位监控
        this.trackMonitor = cloneDeep(TrackMonitor)
        // 恢复通讯录、接收组、短信、电话簿默认配置
        bfStorage.removeItem(`fancytree:${bfglob.userInfo.rid}:${this.addrBookTreeId}`)
        this.initRxGroupList()
        this.initSmsData()

        // 虚拟集群
        this.$refs.virtualCluster?.reset()
        this.virtualClusterVersion = 1
      },
      exportConfig() {
        // 只导出设备的写频配置，不导出设备的接收组、信道数据、设备的dmrId等
        const jsonData = ExportStructIndex.map(type => {
          let config = this.getBeforeEncodeData(type)
          // 删除一些不需要导出的参数
          switch (type) {
            case 4:
              // 将常规设置中的设备dmrId和设备名称去掉
              delete config.deviceName
              delete config.ids
              break
          }
          if (!Array.isArray(config)) {
            config = [config]
          }
          return { [type]: config }
        }).reduce((p, c) => {
          return Object.assign(p, c)
        }, {})

        this.exportData(jsonData)
      },
      readDataConfig() {
        if (!this.canRead() || this.isReading) {
          return
        }
        // 开始读取数据提示
        bfNotify.messageBox(this.$t('msgbox.startReading'))
        this.clearSelectedDeviceDmrId()
        this.clearDeviceDataConfig()
        this.sendAuthentication()
      },
      validateAllRules() {
        return new Promise((resolve, reject) => {
          const validateList = [
            {
              ref: 'generalSettings',
              msg: this.$t('writeFreq.generalSettingsFormValidate'),
            },
            {
              ref: 'digitAlarm',
              msg: this.$t('writeFreq.digitalAlarmFormValidate'),
            },
            {
              ref: 'scanGroup',
              msg: this.$t('writeFreq.scanGroupFormValidate'),
            },
            {
              ref: 'virtualCluster',
              msg: this.$t('writeFreq.virtualClusterFormValidate'),
            },
          ]
          // 信道列表中有数据时才需要验证
          if (this.channelDataList.length) {
            validateList.push({
              ref: 'channelV9',
              msg: this.$t('writeFreq.channelDataFormValidate'),
            })
          }
          const iterator = validateList[Symbol.iterator]()

          const validate = item => {
            if (item.done) {
              resolve()
            }
            const { ref, msg } = item.value
            this.formValidate(ref)
              .then(() => {
                validate(iterator.next())
              })
              .catch(() => {
                reject(msg)
              })
          }

          validate(iterator.next())
        })
      },
      writeInFrequency() {
        this.writeDataConfig()
      },
      getBeforeEncodeData(type) {
        let result
        const typeStr = type + ''
        switch (typeStr) {
          // 设备信息
          case '1':
            return cloneDeep(this.deviceWriteInfo)
          // 身份信息
          case '2':
            return cloneDeep(this.identityInfo)
          // 通讯密码
          case '3':
            return cloneDeep(this.passwordInfo)
          // 总体设置
          case '4':
            return cloneDeep(this.generalSettings)
          // 按键设置
          case '5':
            return cloneDeep(this.buttonDefined)
          // 短信
          case '6':
            return cloneDeep(this.smsContent)
          // 菜单设置
          case '9':
            return cloneDeep(this.menuSettings)
          // 卫星定位设置
          case '10':
            return cloneDeep(this.gpsData)
          // 信令系统
          case '11':
            return cloneDeep(this.signalingSystem)
          // 数字紧急报警
          case '13':
            return cloneDeep(this.digitAlarmList)
          // 数字通讯录设置
          case '15':
            return cloneDeep(this.selectedAddressBook)
          // 接收组列表
          case '16':
            return this.receiveGroup ? this.receiveGroup.getWriteRxGroupList() : []
          // 区域数据
          case '17':
            return cloneDeep(this.zoneDataList)
          // 信道
          case '18':
            return cloneDeep(this.channelDataList).map(channel => {
              if (this.receiveGroup) {
                channel.subChannelData.receiveGroup = this.receiveGroup.getGroupIdByChId(channel.chId)
              } else {
                channel.subChannelData.receiveGroup = 0xff
              }
              if (channel.chType === 1) {
                channel.subChannelData.subsonicDecode = this.encodeSubsonic(channel.subChannelData.subsonicDecode)
                channel.subChannelData.subsonicEncode = this.encodeSubsonic(channel.subChannelData.subsonicEncode)
              }
              return channel
            })
          // 扫描设置
          case '19':
            return cloneDeep(this.scanConfig)
          // 扫描列表
          case '20':
            return cloneDeep(this.scanList)
          // 漫游设置
          case '23':
            return cloneDeep(this.roamConfig)
          // 漫游列表
          case '24':
            return cloneDeep(this.roamList)
          // 电话簿
          case '44':
            return this.phoneBook.filter(item => {
              return !!bfglob.gphoneBook.getDataByIndex(item.phoneNo)
            })
          // 配置
          case '45':
            return cloneDeep(this.patrolConfig)
          // 紧急报警
          case '47':
            return cloneDeep(this.emergencyAlarm)
          // 自动定位监控
          case '48':
            return cloneDeep(this.trackMonitor)
          // 加密配置
          case '54':
            return cloneDeep(this.encryptConfig)
          // 基础密钥列表
          case '55':
            return cloneDeep(this.encryptList)
          // ARC4密钥列表
          case '56':
            return cloneDeep(this.encryptARC4List)
          // AES256密钥列表
          case '57':
            return cloneDeep(this.encryptAES256List)
          // 虚拟集群
          case '58':
            return cloneDeep(this.virtualCluster)
          // 站点信息
          case '59':
            return cloneDeep(this.siteInfoList)
        }
        return result
      },

      saveDefaultFrequency(frequency = 400) {
        this.defaultFrequency.value = bfutil.frequencyMhz2Hz(frequency)
        this.defaultFrequency.label = bfutil.frequencyHz2Mhz(this.defaultFrequency.value)
      },
      asyncDeviceWriteInfo(res) {
        if (!res || !Array.isArray(res.result)) {
          return
        }

        this.deviceWriteInfo = Object.assign(this.deviceWriteInfo, res.result[0])
        this.saveDefaultFrequency(this.deviceWriteInfo.minFrequency)

        if (this.deviceWriteInfo.config.bluetooth && this.deviceWriteInfo.config.recording) {
          messageBox(this.$t('writeFreq.bluetoothRecordingChooseOne'), Types.error)
          bfglob.console.error('asyncDeviceWriteInfo error: 蓝牙选配和录音选配只能二选一')
          return
        }
        this.optsFunType = this.deviceWriteInfo.config.bluetooth ? 1 : 0
      },
      asyncIdentityInfo(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }

        const settings = res.result[0]
        this.identityInfo = merge(this.identityInfo, settings)

        // 显示序列号
        this.deviceWriteInfo['serizeNumber'] = this.identityInfo.serizeNumber
      },
      asyncGeneralSettings(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }

        const settings = res.result[0]
        this.generalSettings = merge(this.generalSettings, settings)
      },
      asyncButtonsDefine(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }

        const settings = res.result[0]
        this.buttonDefined = merge(this.buttonDefined, settings)
      },
      asyncMenuSettings(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }

        const settings = res.result[0]
        this.menuSettings = merge(this.menuSettings, settings)
      },
      asyncGpsSettings(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }

        const settings = res.result[0]
        this.gpsData = merge(this.gpsData, settings)
      },
      asyncSignalingSystemSettings(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }

        const settings = res.result[0]
        this.signalingSystem = merge(this.signalingSystem, settings)
      },
      asyncDigitWarning(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }

        res.result.forEach(item => {
          if (item.alarmID === 0) {
            this.digitAlarmList[0] = merge(this.digitAlarmList[0], item)
          } else {
            this.digitAlarmList.push(item)
          }
        })

        if (this.digitAlarmList.length && this.digitAlarmIndex <= 0) {
          this.digitAlarmLabelClick(this.digitAlarmList[0], 0)
        }
      },
      setZoneChFlag(chIdList) {
        if (!Array.isArray(chIdList) || !chIdList.length) {
          return 0
        }

        let val = 0
        let i = 0
        const limit = chIdList.length
        while (i < limit) {
          val += 1 << i++
        }

        return val
      },
      getZoneChIdList(zoneData) {
        if (!zoneData || !Array.isArray(zoneData.chIdList)) {
          return []
        }
        // 每个区域下只有16个信道，信道使用标志右移后按位与0x01,结果为1则启用该信道
        let i = 0
        const limit = zoneData.chIdList.length
        const chIdList = []
        while (i < limit) {
          if ((zoneData.chFlag >> i) & 0x01) {
            chIdList.push(zoneData.chIdList[i])
          }
          i++
        }
        return chIdList
      },
      setZoneDataChIdIndex(zoneData) {
        // 添加信道对应的区域ID索引
        zoneData.chIdList.forEach(chId => {
          this.channelZoneIndex[chId] = zoneData.areaId
        })
      },
      asyncZoneData(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }
        // 保留区域实际的信道id列表
        res.result.forEach(item => {
          item.chIdList = this.getZoneChIdList(item)
          this.setZoneDataChIdIndex(item)
          this.zoneDataList.push(item)
        })
      },
      asyncChannelDataList(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }

        let displayChannelIndex = -1
        res.result.forEach(item => {
          // 合并信道参数
          const index = this.channelDataList.findIndex(channel => channel.chId === item.chId)
          if (index > -1) {
            const channel = this.channelDataList[index]
            this.channelDataList[index] = merge(channel, item)
          } else {
            this.channelDataList.push(item)
          }

          // 标记当前信道设置中相同ID的数据源索引
          if (this.channelData.chId === item.chId) {
            displayChannelIndex = index > -1 ? index : this.channelDataList.length
          }
        })

        this.originChannelDataList = cloneDeep(this.channelDataList)
        const firstChannel = this.channelDataList[displayChannelIndex > -1 ? displayChannelIndex : 0]
        this.$refs.channelV9.zoneChannelListClick(firstChannel)
      },
      asyncScanConfig(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }

        const settings = res.result[0]
        this.scanConfig = merge(this.scanConfig, settings)
      },
      asyncScanList(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }

        res.result.forEach(item => {
          item.membersList = item.membersList.slice(0, item.channelCount)
          if (item.groupId === 0) {
            this.scanList[0] = merge(this.scanList[0], item)
          } else {
            this.scanList.push(item)
          }
        })

        if (this.scanList.length && this.scanGroupIndex <= 0) {
          this.scanGroupIndexLabelClick(this.scanList[0], 0)
        }
      },
      asyncRoamConfig(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }

        const settings = res.result[0]
        this.roamConfig = merge(this.roamConfig, settings)
      },
      asyncRoamList(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }

        res.result.forEach(item => {
          item.memberList = item.memberList.slice(0, item.memberCount)
          if (item.roamId === 0) {
            this.roamList[0] = merge(this.roamList[0], item)
          } else {
            this.roamList.push(item)
          }
        })

        if (this.roamList.length && this.roamGroupIndex <= 0) {
          this.roamGroupIndexLabelClick(this.roamList[0], 0)
        }
      },
      asyncAddressBook(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }

        // 同步本地通讯录树
        if (this.addressBookTree) {
          this.addressBookTree.asyncNodeSelectStatus(res.result)
        }

        this.originAddressBook = this.originAddressBook.concat(res.result)
      },
      getOriginAddressBook(id) {
        for (let i = 0; i < this.originAddressBook.length; i++) {
          const item = this.originAddressBook[i]
          if (item.id === id) {
            return item
          }
        }
        return undefined
      },
      asyncRxGroup(res) {
        if (!res || !Array.isArray(res.result)) {
          return
        }

        // 处理读取回来的接收组数据
        if (this.receiveGroup) {
          this.receiveGroup.asyncRxGroup(res.result)
        }
      },
      asyncPatrolConfig(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }

        const settings = res.result[0]
        this.patrolConfig = merge(this.patrolConfig, settings)
      },
      asyncEmergencyAlarm(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }

        const settings = res.result[0]
        this.emergencyAlarm = merge(this.emergencyAlarm, settings)
      },
      asyncTrackMonitor(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }

        const settings = res.result[0]
        this.trackMonitor = merge(this.trackMonitor, settings)
      },
      asyncShortMessage(res) {
        if (!res || !Array.isArray(res.result)) {
          return
        }
        for (let i = 0; i < res.result.length; i++) {
          const sms = res.result[i]
          const index = this.smsContent.findIndex(item => item.msgId === sms.msgId)
          if (index === -1) {
            this.smsContent.push(sms)
          } else {
            this.smsContent[index] = sms
          }
        }
      },
      asyncPhoneBook(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }

        // 保存本地数据
        for (let i = 0; i < res.result.length; i++) {
          const pb = res.result[i]
          const index = this.phoneBook.findIndex(item => item.phoneId === pb.phoneId)
          if (index === -1) {
            this.phoneBook.push(pb)
          } else {
            this.phoneBook[index] = pb
          }
        }

        if (this.phoneBookTree) {
          this.phoneBookTree.asyncNodeSelectStatus(res.result)
        }
      },
      asyncLocalConfig(data) {
        // bfglob.console.log('asyncLocalConfig data:', data.type, data)
        switch (data.type) {
          // 单个配置对象
          // "1": "deviceWriteInfo",设备信息
          case 1:
            this.asyncDeviceWriteInfo(data)
            break
          case 2:
            this.asyncIdentityInfo(data)
            break
          // "4": "generalSettings",总体设置
          case 4:
            this.asyncGeneralSettings(data)
            break
          // 5: 按键设置
          case 5:
            this.asyncButtonsDefine(data)
            break
          // "6": "ShortMessage",短信
          case 6:
            this.asyncShortMessage(data)
            break
          // 菜单设置
          case 9:
            this.asyncMenuSettings(data)
            break
          // GPS卫星设置
          case 10:
            this.asyncGpsSettings(data)
            break
          //  信令系统
          case 11:
            this.asyncSignalingSystemSettings(data)
            break
          // 数字警报
          case 13:
            this.asyncDigitWarning(data)
            break
          // 数字通信录
          case 15:
            this.asyncAddressBook(data)
            break
          // 接收组列表
          case 16:
            this.asyncRxGroup(data)
            break
          // ZoneData
          case 17:
            this.asyncZoneData(data)
            break
          // 信道数据
          case 18:
            if ('version' in (this.structInfo[18] ?? {})) {
              this.channelVersion = this.structInfo[18].version
            }
            this.asyncChannelDataList(data)
            break
          // 扫描设置
          case 19:
            this.asyncScanConfig(data)
            break
          // 扫描列表
          case 20:
            this.asyncScanList(data)
            break
          // 漫游配置
          case 23:
            this.asyncRoamConfig(data)
            break
          // 漫游列表
          case 24:
            this.asyncRoamList(data)
            break
          // PhoneBook,电话簿
          case 44:
            this.asyncPhoneBook(data)
            break
          // 巡查系统配置
          case 45:
            this.asyncPatrolConfig(data)
            break
          // 紧急报警
          case 47:
            this.asyncEmergencyAlarm(data)
            break
          // 自动定位监控
          case 48:
            this.asyncTrackMonitor(data)
            break
          // 加密配置
          case 54:
            this.asyncCryptCfg(data)
            break
          // 基础密钥列表
          case 55:
            this.asyncCryptCfgList(data)
            break
          // ARC4密钥列表
          case 56:
            this.asyncCryptCfgArc4List(data)
            break
          // AES256密钥列表
          case 57:
            this.asyncCryptCfgAes256List(data)
            break
          // 虚拟集群
          case 58:
            if ('version' in (this.structInfo[58] ?? {})) {
              this.virtualClusterVersion = this.structInfo[58].version
            }
            this.asyncVirtualCluster(data)
            break
          // 站点信息
          case 59:
            this.asyncSiteInfoList(data)
            break
          default:
            bfglob.console.warn('asyncLocalConfig unknown data:', data)
        }
      },
      asyncSiteInfoList(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }

        res.result.forEach(item => {
          // 过滤超出频率数量的无效数据
          item.rxFreqList = item.rxFreqList.slice(0, item.count)
          item.txFreqList = item.txFreqList.slice(0, item.count)

          // 合并数据，siteInfoList有一个id为0的默认的参数，如果id是0,则需要覆盖
          if (item.id === 0) {
            const index = this.siteInfoList.findIndex(val => val.id === item.id)
            this.siteInfoList[index] = item
          } else {
            this.siteInfoList.push(item)
          }
        })

        if (this.siteInfoList.length && this.siteInfoIndex <= 0) {
          this.siteInfoLabelClick(this.siteInfoList[0], 0)
        }
      },
      asyncVirtualCluster(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }
        const config = res.result[0]
        this.virtualCluster = merge(this.virtualCluster, config)
      },
      asyncCryptCfg(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }

        const settings = res.result[0]
        this.encryptConfig = merge(this.encryptConfig, settings)
      },
      asyncCryptCfgList(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }

        this.encryptList = this.encryptList.concat(res.result)
      },
      asyncCryptCfgArc4List(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }

        this.encryptARC4List = this.encryptARC4List.concat(res.result)
      },
      asyncCryptCfgAes256List(res) {
        if (!res.result || !Array.isArray(res.result)) {
          return
        }

        this.encryptAES256List = this.encryptAES256List.concat(res.result)
      },

      showDeviceInsetOrRemovedMessage() {
        let msg = this.$t('msgbox.usbDeviceInsertSuccess')
        let type = 'success'

        if (this.noDevice) {
          msg = this.$t('msgbox.usbDeviceHasBeenOut')
          type = 'warning'
        }
        bfNotify.messageBox(msg, type)
      },
      resetScanList() {
        this.scanGroupIndex = 0
        this.scanList = []
        this.initScanGroup()
      },
      resetRoamList() {
        this.roamGroupIndex = 0
        this.roamList = []
        this.initRoamGroup()
      },
      resetDigitAlarmList() {
        this.digitAlarmIndex = 0
        this.digitAlarmList = []
        this.initDigitalAlarm()
      },
      resetZoneDataList() {
        this.zoneDataList = []
      },
      resetChannelDataList() {
        if (this.channelDataList.length) {
          this.originChannelDataList = cloneDeep(this.channelDataList)
        }
        this.channelData = getDefaultChannel(this.channelVersion, 0)
        this.channelDataList = []
      },
      clearSelectedDeviceDmrId() {
        this.selectedDeviceDmrId = ''
      },

      // 覆盖常规设置
      resetGeneralSettings(device) {
        this.generalSettings = Object.assign(this.generalSettings, {
          deviceName: device.selfId,
          ids: parseInt(device.dmrId, 16),
        })
      },
      // 接收组列表,将发射组也添加到通讯录中
      initRxGroupList() {
        if (!this.receiveGroup) {
          return Promise.resolve([])
        }
        return this.receiveGroup.initRxGroupList(this.selectedChannels)
      },
      getAddressNameByDmrId(dmrId) {
        // 从读取回来的通讯录中查找对应的dmrId的通讯录名称
        for (let i = 0; i < this.originAddressBook.length; i++) {
          const item = this.originAddressBook[i]
          if (item.dmrId === dmrId) {
            return item.name
          }
        }

        // 在通讯录中无法找到数据，则从本地的数据中查找
        const org = bfglob.gorgData.getDataByIndex(dmrId)
        return org ? org.orgShortName : ''
      },
      // 查找通讯录对应的发射组dmrId
      getDefaultAddress(dmrId) {
        const address = this.getSelectedAddressByDmrId(dmrId)
        return address ? address.id : 0xffff
      },
      getSelectedAddressByDmrId(dmrId) {
        for (let i = 0; i < this.selectedAddressBook.length; i++) {
          const item = this.selectedAddressBook[i]
          if (item.dmrId === dmrId) {
            return item
          }
        }

        return undefined
      },
      getSelectedAddress(id) {
        for (let i = 0; i < this.selectedAddressBook.length; i++) {
          const item = this.selectedAddressBook[i]
          if (item.id === id) {
            return item
          }
        }

        return undefined
      },
      getDeviceChannelData(device, channelId) {
        for (let i = 0; i < device.channels.length; i++) {
          const item = device.channels[i]
          if (item.no === channelId + 1) {
            return item
          }
        }
        return undefined
      },

      // 同步设备管理数据变化
      updateDeviceData(data) {
        // console.log("updateDeviceData:", data);
        if (!data) {
          return
        }
        if (this.selectedDeviceDmrId === data.oldDmrId) {
          this.selectedDeviceDmrId = data.dmrId
        }
        if (this.addressBookTree) {
          this.addressBookTree.updateAddrBookTree(data)
        }
      },
      deleteDeviceData(rid, data) {
        // console.log("deleteDeviceData:", data);
        if (!data) {
          return
        }
        if (this.selectedDeviceDmrId === data.dmrId) {
          this.selectedDeviceDmrId = ''
        }
        if (this.addressBookTree) {
          this.addressBookTree.updateAddrBookTree(data)
        }
      },
      globalDeviceChannelsChanged(device) {
        if (!device) {
          return
        }
        if (this.selectedDeviceDmrId === device.dmrId) {
          this.selectedDeviceDmrId = ''
          this.$nextTick(() => {
            this.selectedDeviceDmrId = device.dmrId
          })
        }
      },

      updateAddrBookTree(data) {
        if (!this.addressBookTree) {
          return
        }
        this.addressBookTree.treeReload()
      },
      getButtonDefinedLabel(index) {
        switch (index) {
          case 0:
            return this.$t('writeFreq.orangeButton')
          case 1:
            return this.$t('writeFreq.key01')
          case 2:
            return this.$t('writeFreq.key02')
          default:
            return ''
        }
      },

      frequencyHz2Mhz: bfutil.frequencyHz2Mhz,
      frequencyMhz2Hz: bfutil.frequencyMhz2Hz,
      getSoftKeyCallTypeList(data) {
        const list = []
        const address = this.getSelectedAddress(data.addrId)
        if (!address) {
          return []
        }
        const hasSms = this.smsContent.length > 0
        if (hasSms) {
          list.push(SoftKeyCallType[SoftKeyCallType.MSG])
        }
        if (address.callType === AddressBookCallTypes.SINGLE) {
          list.push(SoftKeyCallType[SoftKeyCallType.SINGLE])
          list.push(SoftKeyCallType[SoftKeyCallType.TIP])
          if (data.optType === SoftKeyCallType.GROUP) {
            data.optType = SoftKeyCallType.SINGLE
          }
        } else {
          list.push(SoftKeyCallType[SoftKeyCallType.GROUP])
        }

        return list.map(key => {
          return {
            label: this.$t(`writeFreq.softKeyCallType.${key}`),
            value: SoftKeyCallType[key],
          }
        })
      },
      getSoftKeyFuncDefine(scope, typeVal = 0) {
        const { $index } = scope
        let excludes = []
        if ($index === 0) {
          // 短按，没有紧急模式关闭
          // 长按，没有紧急模式开启
          if (typeVal === 0) {
            excludes = [SoftKeyFuncDefine.WARNING_OFF]
          } else {
            excludes = [SoftKeyFuncDefine.WARNING_ON]
          }
        } else {
          excludes = [SoftKeyFuncDefine.WARNING_ON, SoftKeyFuncDefine.WARNING_OFF]
        }

        // 短按没有永久监听参数
        if (typeVal === 0) {
          excludes.push(SoftKeyFuncDefine.LONGMONI)
        }

        return this.softKeyFuncDefine.filter(opt => {
          return !excludes.includes(opt.value)
        })
      },
      scanListMembersChange(data) {
        // 设置选中的信道时，最大为16个
        if (data.length > this.oneScanGroupLimit) {
          this.scanGroup.membersList = data.slice(0, this.oneScanGroupLimit)
          messageBox(this.$t('writeFreq.fullList'), Types.warning)
        }

        this.scanGroup.channelCount = this.scanGroup.membersList.length
      },
      filterChannelId() {
        const channelIdList = this.currentChannelIdList
        this.scanList = this.scanList.map(item => {
          item.membersList = filterChannelIdWhenDeviceChanged(channelIdList, item.membersList)
          item.channelCount = item.membersList.length
          return item
        })
      },
      filterRoamListChannelId() {
        const channelIdList = this.currentChannelIdList
        this.roamList = this.roamList.map(item => {
          item.memberList = filterChannelIdWhenDeviceChanged(channelIdList, item.memberList)
          item.memberCount = item.memberList.length
          return item
        })
      },
      resetDigitalAlarmReplyChannel() {
        const channelIdList = this.currentChannelIdList
        this.digitAlarmList = this.digitAlarmList.map(data => {
          data.replyChannel = resetDigitalAlertReplyChannel(channelIdList, data.replyChannel)
          return data
        })
      },
      async selectDeviceDataChanged(device) {
        // 常规设置
        this.resetGeneralSettings(device)
        // 接收组
        await this.initRxGroupList()
        // 信道
        this.initZoneChannel()
        // 扫描组过滤不存在的信道ID
        this.filterChannelId()
        // 漫游组过滤不存在的信道ID
        this.filterRoamListChannelId()
        // 数字报警重置回复信道参数
        this.resetDigitalAlarmReplyChannel()

        // 处理虚拟群集的归属组
        //所属归属组，从组呼联系人选择, 所以联系人中必须得选中归属组
        if (device.deviceType === DeviceTypes.VirtualClusterDevice) {
          const devGroup = device.devGroup
          // 归属组在通讯录上设置选中且禁用状态
          bfglob.emit(`${this.addrBookTreeId}:unselectableNode`, [devGroup], () => {
            // 等待通讯录渲染完成
            setTimeout(() => {
              this.$nextTick(() => {
                const book = this.selectedAddressBook.find(item => item.dmrId === devGroup)
                // 设置归属组在通讯录上的ID
                if (book) {
                  this.virtualCluster.vcGroupId = book.id
                }
              })
            }, 0)
          })
        }
      },
      // 清除通讯录被禁用的状态
      clearTreeNodeUnselectable() {
        // 清除通讯录被禁用和非系统通讯数据的节点
        this.addressBookTree && this.addressBookTree.removeNotInSystemNodes()
        this.selectedAddressBook.forEach(item => {
          bftree.nodeUnselectable(this.addrBookTreeId, item.nodeKey)
        })
      },
      clearPrivateConfig() {
        // 清除常规设置的设备名称
        this.generalSettings.deviceName = ''
        this.generalSettings.ids = 0
        // 接收组
        this.receiveGroup && this.receiveGroup.resetRxGroupList()
        // 区域
        this.resetZoneDataList()
        // 信道
        this.resetChannelDataList()
        this.clearTreeNodeUnselectable()
        if (this.phoneBookTree) {
          this.phoneBookTree.removeNotInSystemNodes()
        }
      },
      // cleanAll 标记是否清除全部配置
      async clearDeviceDataConfig(cleanAll = false) {
        // 必须清除的数据，接收组、区域、信道等私有数据，包括一些标记参数
        this.clearPrivateConfig()

        // 密钥
        this.encryptList = []
        this.encryptARC4List = []
        this.encryptAES256List = []

        this.resetScanList()
        this.resetRoamList()
        this.resetDigitAlarmList()
        this.initSiteInfoList()

        // 可选的清除数据，常规设置、菜单、按键定义、警报、通讯录、电话本、短信等通用数据
        if (!cleanAll) {
          return
        }
        this.originAddressBook = []
        this.selectedAddressBook = []
        this.addressBookCache = []
        this.addressBookTree.treeReload(true)
        this.phoneBook = []
        this.phoneBookTree && this.phoneBookTree.treeReload(true)
      },
    },
    computed: {
      idShownMinLen: {
        get() {
          return this.menuSettings.menuConfig.idShownMinLen + 1
        },
        set(value) {
          this.menuSettings.menuConfig.idShownMinLen = value - 1
        },
      },
      timeZone: {
        get() {
          return timeZoneKeys
            .map(key => {
              return { [key]: this.generalSettings[key] }
            })
            .reduce((p, c) => {
              return Object.assign(p, c)
            }, {})
        },
        set(value) {
          timeZoneKeys.forEach(key => {
            this.generalSettings[key] = value[key]
          })
        },
      },
      encryptListActiveWidth() {
        return this.isFR ? '120px' : '100px'
      },
      encryptEnable() {
        return this.encryptConfig.config.enable
      },
      currentChannelIdList() {
        return this.channelDataList.map(item => item.chId)
      },
      dmrIdLabel() {
        const intDmrId = this.generalSettings.ids
        if (intDmrId === 0) {
          return ''
        }
        const dmrId = bfutil.int32Dmrid2Hex(intDmrId)
        return dmrId ? ` ${dmrId} / ${intDmrId}` : ''
      },
      isEn() {
        return this.$i18n.locale === 'en'
      },

      fullscreen() {
        return this.bfmaxi ? true : this.$root.layoutLevel === 0
      },
      hasFreqRange() {
        return this.deviceWriteInfo.maxFrequency > 0 && this.deviceWriteInfo.minFrequency > 0
      },
      disWriteBtn() {
        return this.disReadBtn || !this.selectedDeviceDmrId || !this.hasFreqRange || this.selectedChannels.length === 0
      },
      disReadBtn() {
        return this.noQWebServer || this.noDevice || this.isWriting || this.isReading
      },
      noQWebServer() {
        return !this.QWebServer
      },
      channelListColumn() {
        return [
          {
            prop: 'channelId',
            label: this.$t('dialog.chId'),
            width: this.fullscreen ? 'auto' : this.isEn ? '150px' : '100px',
          },
          {
            prop: 'channelName',
            label: this.$t('dialog.chName'),
            width: this.fullscreen ? 'auto' : this.isEn ? '150px' : '100px',
          },
          {
            prop: 'recvFreq1',
            label: this.$t('dialog.receiveNumber', { num: 1 }),
            width: this.fullscreen ? 'auto' : this.isEn ? '150px' : '120px',
          },
          {
            prop: 'sendFreq1',
            label: this.$t('dialog.emissionNumber', { num: 1 }),
            width: this.fullscreen ? 'auto' : this.isEn ? '150px' : '120px',
          },
          {
            prop: 'recvFreq2',
            label: this.$t('dialog.receiveNumber', { num: 2 }),
            width: this.fullscreen ? 'auto' : this.isEn ? '150px' : '120px',
          },
          {
            prop: 'sendFreq2',
            label: this.$t('dialog.emissionNumber', { num: 2 }),
            width: this.fullscreen ? 'auto' : this.isEn ? '220px' : '120px',
          },
          {
            prop: 'recvFreq3',
            label: this.$t('dialog.receiveNumber', { num: 3 }),
            width: this.fullscreen ? 'auto' : this.isEn ? '150px' : '120px',
          },
          {
            prop: 'sendFreq3',
            label: this.$t('dialog.emissionNumber', { num: 3 }),
            width: this.fullscreen ? 'auto' : this.isEn ? '150px' : '120px',
          },
        ]
      },
      generalSettingsRules() {
        const soundEncryptValueRules = [validateRules.mustLength(['change', 'blur'], 10)]
        if (this.generalSettings.soundEncryptType !== 0) {
          soundEncryptValueRules.push(validateRules.required())
        }
        const pwdRule = [validateRules.mustLength(['change', 'blur'], 6), validateRules.mustNumber(['change', 'blur'])]

        return {
          deviceName: [validateRules.required(), validateRules.maxLen(['change', 'blur'], 16)],
          repeaterId: [validateRules.required(), validateRules.mustNumber(), validateRules.range(['blur'], 1, 16777215, '1~16777215')],
          soundEncryptValue: soundEncryptValueRules,
          powerOnPassword: pwdRule,
          channelConfigPassword: pwdRule,
          uDiskModePassword: pwdRule,
        }
      },
      digitAlarmRules() {
        return {
          alarmName: [validateRules.required(['blur', 'change'])],
        }
      },
      scanGroupRules() {
        return {
          name: [validateRules.required(['blur', 'change'])],
        }
      },
      roamGroupRules() {
        return {
          name: [validateRules.required(['blur', 'change'])],
        }
      },
      channelDataRules() {
        const defFreq = this.defaultFrequency
        const minFrequency = this.deviceWriteInfo.minFrequency
        const maxFrequency = this.deviceWriteInfo.maxFrequency
        const min = minFrequency ? bfutil.frequencyMhz2Hz(minFrequency) : defFreq.value
        const max = maxFrequency ? bfutil.frequencyMhz2Hz(maxFrequency) : defFreq.value + 80000000
        const smg = `${minFrequency || bfutil.frequencyHz2Mhz(defFreq.value) || 0}~${maxFrequency || bfutil.frequencyHz2Mhz(defFreq.value + 80000000)}`
        const frequency = [validateRules.required(['blur']), validateRules.range(['blur'], min, max, smg)]
        return {
          chName: [validateRules.required(['blur'])],
          receivingFrequency: frequency,
          transmittingFrequency: frequency,
        }
      },
      repeaterTimeSlotList() {
        return [
          {
            label: '1',
            value: 0,
          },
          {
            label: '2',
            value: 1,
          },
        ]
      },
      permissionConditionsList() {
        // 0 可用彩色码
        // 1 始终
        // 2 信道空闲
        return [
          {
            label: this.$t('dialog.availableColorCode'),
            value: 0,
          },
          {
            label: this.$t('writeFreq.always'),
            value: 1,
          },
          {
            label: this.$t('dialog.channelIdle'),
            value: 2,
          },
        ]
      },
      txPowerTypes() {
        // 0-低
        // 2-高
        return [
          {
            label: this.$t('dialog.low'),
            value: 0,
          },
          {
            label: this.$t('dialog.high'),
            value: 2,
          },
        ]
      },
      soundEncryptTypeList() {
        return [
          {
            value: 0,
            label: this.$t('dialog.unencrypted'),
          },
          {
            value: 1,
            label: this.$t('dialog.staticEncryption'),
          },
          {
            value: 2,
            label: this.$t('dialog.dynamicencryption'),
          },
          {
            value: 3,
            label: this.$t('dialog.advancedDynamicencryption'),
          },
        ]
      },
      onOffList() {
        return [
          {
            label: this.$t('dialog.off'),
            value: 0,
          },
          {
            label: this.$t('dialog.on'),
            value: 1,
          },
        ]
      },
      rfidModeList() {
        return [
          {
            label: this.$t('dialog.autoCardReading'),
            value: 0,
          },
          {
            label: this.$t('dialog.triggerCardReading'),
            value: 1,
          },
        ]
      },
      rfidPowerList() {
        // 0 -18dbm
        // 1 -12dbm
        // 2 -6dbm
        // 3 0dbm
        return [
          {
            label: '-18dbm',
            value: 0,
          },
          {
            label: '-12dbm',
            value: 1,
          },
          {
            label: '-6dbm',
            value: 2,
          },
          {
            label: '0dbm',
            value: 3,
          },
        ]
      },
      rfidAnswerList() {
        // 0 数传指令应答
        // 1 采用芯片自动应答机制
        return [
          {
            label: this.$t('dialog.dataTrsCmdRes'),
            value: 0,
          },
          {
            label: this.$t('dialog.chipAutoResMechanism'),
            value: 1,
          },
        ]
      },

      // 虚拟时隙
      slotModeList() {
        return [
          {
            label: this.$t('dialog.timeSlot', { num: 1 }),
            value: 0,
          },
          {
            label: this.$t('dialog.timeSlot', { num: 2 }),
            value: 1,
          },
          {
            label: this.$t('dialog.virtualCluster'),
            value: 2,
          },
        ]
      },
      virtualTimeSlotList() {
        return [
          {
            label: this.$t('dialog.nothing'),
            value: 0,
          },
          {
            label: this.$t('dialog.timeSlot', { num: 1 }),
            value: 1,
          },
          {
            label: this.$t('dialog.timeSlot', { num: 2 }),
            value: 2,
          },
        ]
      },
      localeList() {
        const locales = []
        if (SupportedLangList.includes(SupportedLang.zhCN)) {
          locales.push({
            label: this.$t('header.CN'),
            value: 0,
          })
        }
        if (SupportedLangList.includes(SupportedLang.enUS)) {
          locales.push({
            label: this.$t('header.EN'),
            value: 1,
          })
        }

        return locales
      },
      savePowerModeList() {
        return [
          {
            label: this.$t('dialog.nothing'),
            value: 0,
          },
          {
            label: '1:1',
            value: 1,
          },
          {
            label: '1:2',
            value: 2,
          },
          {
            label: '1:3',
            value: 3,
          },
          {
            label: '1:4',
            value: 4,
          },
        ]
      },
      freqDisplayList() {
        // 信道显示模式，0 频率显示，1 信道显示，2 频率+信道显示
        return [
          {
            label: this.$t('dialog.freqDisplay'),
            value: 0,
          },
          {
            label: this.$t('dialog.chDisplay'),
            value: 1,
          },
          {
            label: this.$t('dialog.freqAndChDisplay'),
            value: 2,
          },
        ]
      },
      allowCallInstructionList() {
        // 呼叫允许指示 ,000 无  001 模拟  010 模拟和数字 011 数字
        return [
          {
            label: this.$t('dialog.nothing'),
            value: 0,
          },
          {
            label: this.$t('dialog.analog'),
            value: 1,
          },
          {
            label: this.$t('dialog.analogAndDigital'),
            value: 2,
          },
          {
            label: this.$t('dialog.digital'),
            value: 3,
          },
        ]
      },
      recordCompressionRatioList() {
        // 录音压缩比 0 不压缩, 1 3.5倍  当录音使能==0时不可用
        return [
          {
            label: this.$t('dialog.nonCompacting'),
            value: 0,
          },
          {
            label: '3.5',
            value: 1,
          },
        ]
      },
      softKeyFuncDefine() {
        return Object.keys(SoftKeyFuncDefine)
          .filter(key => {
            return this.$te(`writeFreq.softKeyFuncDefine.${key}`)
          })
          .map(key => {
            return {
              label: this.$t(`writeFreq.softKeyFuncDefine.${key}`),
              value: SoftKeyFuncDefine[key],
            }
          })
      },
      SoftKeyCallType() {
        return SoftKeyCallType
      },
      buttonDefineAddressList() {
        const def = [
          {
            label: this.$t('dialog.nothing'),
            value: 0xffff,
          },
        ]
        return def.concat(
          this.selectedAddressBook
            .filter(address => address.dmrId !== bfglob.fullCallDmrId)
            .map(address => {
              return {
                label: address.name,
                value: address.id,
              }
            })
        )
      },
      gpsModeList() {
        // 0:省电模式, 1:高性能模式
        return [
          {
            label: this.$t('writeFreq.powerSavingMode'),
            value: 0,
          },
          {
            label: this.$t('writeFreq.highPerformanceMode'),
            value: 1,
          },
        ]
      },
      smsList() {
        return this.smsContent
      },
      selectDeviceData() {
        return bfglob.gdevices.getDataByIndex(this.selectedDeviceDmrId)
      },
      scanningGroupTransferTitles() {
        return [this.$t('writeFreq.availableChannel'), this.$t('writeFreq.containedChannel')]
      },
      // 没有设置默认发射组的信道集合
      noDefaultAddressChannelList() {
        return this.channelDataList.filter(channel => {
          // 过滤没有设置发射组的信道
          if (typeof channel.subChannelData.defaultAddress === 'undefined' || channel.subChannelData.defaultAddress === 0xffff) {
            return false
          }
          return true
        })
      },
      replyChannelList() {
        const channelList = this.noDefaultAddressChannelList.map(channel => {
          return {
            label: channel.chName,
            value: channel.chId,
          }
        })
        const def = []

        // 没有信道可选时，不可设置回复信道，值为无(0xFFFF)
        if (channelList.length === 0) {
          def.push({
            label: this.$t('dialog.nothing'),
            value: 0xffff,
          })
        } else if (channelList.length === 1) {
          // no-empty
        } else {
          def.push({
            label: this.$t('writeFreq.theSelected'),
            value: 0xfffe,
          })
        }

        return def.concat(channelList)
      },
      specifiedTrChIDList() {
        return [
          {
            label: this.$t('writeFreq.lastActiveChannel'),
            value: 0xfffd,
          },
        ].concat(this.availableChannelList)
      },
      availableChannelList() {
        return [
          {
            label: this.$t('writeFreq.theSelected'),
            value: 0xfffe,
            disabled: true,
          },
        ].concat(
          this.channelDataList.map(channel => {
            return {
              label: channel.chName,
              value: channel.chId,
            }
          })
        )
      },
      roamGroupChannelList() {
        return [
          {
            label: this.$t('writeFreq.theSelected'),
            value: 0xfffe,
            disabled: true,
          },
        ].concat(
          this.channelDataList
            .filter(channel => channel.roamConfig.ipSiteConnect)
            .map(channel => {
              return {
                label: channel.chName,
                value: channel.chId,
              }
            })
        )
      },
      aloneWorkOptList() {
        return [
          {
            label: this.$t('writeFreq.button'),
            value: 0,
          },
          {
            label: this.$t('writeFreq.voiceLaunch'),
            value: 1,
          },
        ]
      },
      triggerModeList() {
        return [
          {
            label: this.$t('writeFreq.onlyTilt'),
            value: 0,
          },
          {
            label: this.$t('writeFreq.motionDetectionOnly'),
            value: 1,
          },
          {
            label: this.$t('writeFreq.tiltOrMotionDetection'),
            value: 2,
          },
        ]
      },
      triggerTiltList() {
        return [
          {
            label: 60,
            value: 0,
          },
          {
            label: 45,
            value: 1,
          },
          {
            label: 30,
            value: 2,
          },
        ]
      },
      maxAloneWorkRemindTime() {
        const sce = this.signalingSystem.aloneWorkTime * 60
        return sce > 0xff ? 0xff : sce
      },
      workAloneUnEnable() {
        return !this.signalingSystem.aloneWorkEnable
      },
      reversePlayUnEnable() {
        return !this.signalingSystem.reversePlayEnable
      },
      disTriggerTilt() {
        return this.reversePlayUnEnable || this.signalingSystem.reversePlayOption.triggerMode === 1
      },
      digitAlarmTypeList() {
        return [
          {
            label: this.$t('writeFreq.forbid'),
            value: 0,
          },
          {
            label: this.$t('dialog.common'),
            value: 1,
          },
          {
            label: this.$t('dialog.silent'),
            value: 2,
          },
          {
            label: this.$t('writeFreq.silenceCarryVoice'),
            value: 3,
          },
        ]
      },
      digitAlarmModeList() {
        return [
          {
            label: this.$t('dialog.emergency'),
            value: 0,
          },
          {
            label: this.$t('writeFreq.emergencyAlarmAndCall'),
            value: 1,
          },
          {
            label: this.$t('writeFreq.emergencyAlarmAndVoice'),
            value: 2,
          },
        ]
      },
      forbidAlarm() {
        return this.digitAlarm.alarmType === 0
      },
      forbidReplyChannel() {
        const result = this.replyChannelList.filter(item => {
          return item.value === 0xffff
        })
        if (result.length > 0) {
          return true
        }

        return this.forbidAlarm
      },
      disMicActiveTime() {
        return this.digitAlarm.alarmMode !== 2 || this.forbidAlarm
      },
      chTypeList() {
        return [
          {
            label: this.$t('dialog.digitalChannel'),
            value: 0,
          },
          {
            label: this.$t('dialog.analogChannel'),
            value: 1,
          },
          {
            label: this.$t('writeFreq.digitalCompatibleAnalog'),
            value: 2,
          },
          {
            label: this.$t('writeFreq.analogCompatibleDigital'),
            value: 3,
          },
        ]
      },
      chScanRoamGroupList() {
        const list = [
          {
            label: this.$t('dialog.nothing'),
            value: 0xff,
          },
        ]
        if (this.channelData.chType === 0 && this.channelData.roamConfig.ipSiteConnect) {
          return list.concat(
            this.roamList.map(item => {
              return {
                label: item.name,
                value: item.roamId,
              }
            })
          )
        }

        return list.concat(
          this.scanList.map(item => {
            return {
              label: item.name,
              value: item.groupId,
            }
          })
        )
      },
      chTimeSlotCalList() {
        return [
          {
            label: this.$t('dataTable.fail'),
            value: 0,
          },
          {
            label: this.$t('dataTable.pass'),
            value: 1,
          },
          {
            label: this.$t('writeFreq.firstChoice'),
            value: 2,
          },
        ]
      },
      bandwidthFlagList() {
        return [
          {
            label: this.$t('writeFreq.broadBand'),
            value: 0,
          },
          {
            label: this.$t('writeFreq.narrowBand'),
            value: 1,
          },
        ]
      },
      emergencySysIdList() {
        const def = [
          {
            label: this.$t('dialog.nothing'),
            value: 0xff,
          },
        ]
        const digitAlarmList = this.digitAlarmList.map(item => {
          return {
            label: item.alarmName,
            value: item.alarmID,
          }
        })
        return def.concat(digitAlarmList)
      },
      receiveGroupList() {
        const def = [
          {
            label: this.$t('dialog.nothing'),
            value: 0xffff,
          },
        ]
        const rxGroupList = this.rxGroupList.map(item => {
          return {
            label: item.groupName,
            value: item.groupId,
          }
        })
        return def.concat(rxGroupList)
      },
      defaultAddressList() {
        const cache = []
        return [
          {
            label: this.$t('dialog.nothing'),
            value: 0xffff,
          },
        ].concat(
          this.selectedAddressBook
            .concat(this.originAddressBook)
            .filter(address => {
              if (cache.includes(address.id)) {
                return false
              }
              cache.push(address.id)
              return true
            })
            .map(address => {
              return {
                label: address.name,
                value: address.id,
              }
            })
        )
      },
      tailToneList() {
        return [
          {
            label: this.$t('writeFreq.noSubtone'),
            value: 0,
          },
          {
            label: this.$t('writeFreq.standardPhase'),
            value: 1,
          },
          {
            label: this.$t('writeFreq.nonstandardPhase'),
            value: 2,
          },
        ]
      },
      busyChannelLockList() {
        return [
          {
            label: this.$t('dialog.off'),
            value: 0,
          },
          {
            label: this.$t('writeFreq.carrier'),
            value: 1,
          },
          {
            label: 'CTCSS/CDCSS',
            value: 2,
          },
        ]
      },
      getFixedDate() {
        const { year, month, day, hour, minute, second } = this.generalSettings

        let date = new Date()
        date = new Date(date.setSeconds(second))
        date = new Date(date.setMinutes(minute))
        date = new Date(date.setHours(hour))
        date = new Date(date.setDate(day))
        date = new Date(date.setMonth(month))
        date = new Date(date.setFullYear(year))

        return date
      },
      disAddDigital() {
        return this.digitAlarmList.length === this.digitAlarmLimit || !this.digitAlarm.alarmName
      },
      disAddScanGroup() {
        return this.scanList.length === this.scanGroupLimit || !this.scanGroup.name
      },
      disAddRoamGroup() {
        return this.roamList.length === this.roamGroupLimit || !this.roamGroup.name
      },
      deviceFuncConfig() {
        return this.deviceWriteInfo.config
      },
      isGConfig() {
        return this.deviceFuncConfig && this.deviceFuncConfig.locate
      },
      isLConfig() {
        return this.optsFunType === 0 && this.deviceFuncConfig && this.deviceFuncConfig.recording
      },
      enableBluetoothConfig() {
        return this.optsFunType === 1 && this.deviceFuncConfig && this.deviceFuncConfig.bluetooth
      },
      isLGConfig() {
        return this.isGConfig && this.isLConfig
      },
      isWConfig() {
        return this.deviceFuncConfig && this.deviceFuncConfig.workAlone
      },
      isBConfig() {
        return this.deviceFuncConfig && this.deviceFuncConfig.runBackward
      },
      isRConfig() {
        return this.deviceFuncConfig && this.deviceFuncConfig.roaming
      },
      isAConfig() {
        return this.isWConfig && this.isBConfig
      },
      isSConfig() {
        return this.isLGConfig && this.isAConfig
      },
      deviceBaseConfig() {
        return !this.isSConfig
      },
      onlyReceive() {
        return this.channelData.powerAndFlag.onlyReceiveSign
      },
      isConnectNetworking() {
        const data = this.channelData.subChannelData
        if (!data || !data.funcSettings) {
          return false
        }

        return data.funcSettings.networking
      },
      addressBookCallTypes() {
        return AddressBookCallTypes
      },
      addressBookTree() {
        return this.$refs[this.addrBookTreeId]
      },
      phoneBookTree() {
        return this.$refs[this.phoneBookTreeId]
      },
      receiveGroup() {
        return this.$refs[this.refReceiveGroup]
      },
      writeDataOption() {
        return [
          {
            type: 4,
            failedMsg: this.$t('msgbox.writeRegularSettingsFailed'),
          },
          {
            type: 5,
            failedMsg: this.$t('msgbox.writeDeySettingsFailed'),
          },
          {
            type: 6,
            failedMsg: this.$t('msgbox.writeSMSFailed'),
            option: { limit: 1 },
          },
          {
            type: 9,
            failedMsg: this.$t('msgbox.writeMenuFailed'),
          },
          {
            type: 10,
            failedMsg: this.$t('msgbox.writeGpsDataFailed'),
          },
          {
            type: 11,
            failedMsg: this.$t('msgbox.writeSignalingSystemFailed'),
          },
          {
            type: 13,
            failedMsg: this.$t('msgbox.writeDigitalAlarmFailed'),
          },
          {
            type: 15,
            failedMsg: this.$t('msgbox.writeAddressBookFailed'),
          },
          {
            type: 16,
            failedMsg: this.$t('msgbox.writeReceivingGroupFailed'),
          },
          {
            type: 17,
            failedMsg: this.$t('msgbox.writeZoneDataFailed'),
          },
          {
            type: 18,
            failedMsg: this.$t('msgbox.writeChannelDataFailed'),
          },
          {
            type: 19,
            failedMsg: this.$t('msgbox.writeScanConfigFailed'),
          },
          {
            type: 20,
            failedMsg: this.$t('msgbox.writeScanListFailed'),
          },
          {
            type: 23,
            failedMsg: this.$t('msgbox.writeRoamConfigFailed'),
          },
          {
            type: 24,
            failedMsg: this.$t('msgbox.writeRoamListFailed'),
          },
          {
            type: 44,
            failedMsg: this.$t('msgbox.writePhoneBookFailed'),
          },
          {
            type: 45,
            failedMsg: this.$t('msgbox.writePatrolSystemConfigFailed'),
          },
          {
            type: 47,
            failedMsg: this.$t('msgbox.writeEmergencyAlarmConfigFailed'),
          },
          {
            type: 48,
            failedMsg: this.$t('msgbox.writeTraceMonitorConfigFailed'),
          },
          // 加密、密钥
          {
            type: 54,
            failedMsg: this.$t('msgbox.writeEncryptConfigFailed'),
          },
          {
            type: 55,
            failedMsg: this.$t('msgbox.writeEncryptKeyFailed'),
          },
          {
            type: 56,
            failedMsg: this.$t('msgbox.writeEncryptionARC4KeyFailed'),
          },
          {
            type: 57,
            // 3个密钥一个数据包，超出了320字节
            option: {
              limit: 2,
            },
            failedMsg: this.$t('msgbox.writeEncryptionAES256KeyFailed'),
          },
          {
            type: 58,
            failedMsg: this.$t('msgbox.writeVirtualClusterFailed'),
          },
          {
            type: 59,
            option: {
              limit: 1,
            },
            failedMsg: this.$t('msgbox.writeSiteInfoFailed'),
          },
          // 最后写入编程密码
          {
            type: 3,
            failedMsg: this.$t('msgbox.writeProgrammingPwdFailed'),
          },
        ]
      },
      getClassInstance() {
        return getClassInstance
      },
      Model() {
        return this.expectedModel || Model
      },
      ipSiteConnect() {
        if (!this.channelData.roamConfig) {
          return false
        }
        return this.channelData.roamConfig.ipSiteConnect
      },
    },
    watch: {
      // 加密未启用时，禁用所有信道的加密配置
      encryptEnable(value) {
        if (value) return

        // 信道类型 0：数字 1：模拟 2：数字兼容模拟 3：模拟兼容数字
        const notEncryptChTypes = [1]
        for (let i = 0; i < this.channelDataList.length; i++) {
          if (notEncryptChTypes.includes(this.channelDataList[i].chType)) {
            continue
          }

          if (this.channelDataList[i].subChannelData.encryptConfig) {
            this.channelDataList[i].subChannelData.encryptConfig.enable = false
          }
        }
      },
      smsContent: {
        deep: true,
        handler(data) {
          this.$nextTick(() => {
            // 短信内容变化时，检测按键定义中单键呼叫功能的设置
            this.detectButtonDefinedFromSmsChange()
          })
        },
      },
      noDefaultAddressChannelList(val) {
        if (val.length === 0) {
          this.digitAlarm.replyChannel = 0xffff
        } else if (val.length === 1) {
          // 可选的信道个数只有一个时，回复信道固定选中该信道
          this.digitAlarm.replyChannel = val[0].value
        }
      },
      'buttonDefined.oneTouchFuncCall': {
        deep: true,
        handler(val) {
          if (!val) {
            return
          }
          const DefSmsId = 0xff
          for (let i = 0; i < val.length; i++) {
            const item = val[i]
            if (item.addrId === 0xffff) {
              item.optType = 1
              item.smsId = DefSmsId
              continue
            }

            if (item.optType === SoftKeyCallType.MSG && item.smsId === DefSmsId) {
              item.smsId = this.smsContent[0].msgId
              continue
            }

            if (item.optType !== SoftKeyCallType.MSG) {
              const address = this.getSelectedAddress(item.addrId)
              if (!address) {
                item.addrId = 0xffff
                item.optType = 1
                item.smsId = DefSmsId
                continue
              }

              if (address.callType === AddressBookCallTypes.GROUP) {
                item.optType = SoftKeyCallType.GROUP
              }
            }
          }
        },
      },
      'generalSettings.powerOnPassword'(val) {
        if (!val) {
          this.menuSettings.soundLightPassword.powerOnPassword = false
        }
      },
      'generalSettings.soundEncryptType'(val) {
        if (val === 0) {
          this.generalSettings.soundEncryptValue = ''
        }
      },
      // 常规设置中勾选全部静音，语音指示、信道空闲指示、呼叫允许指示不可勾选
      'generalSettings.soundAndDisplayTip.muteAll'(val) {
        if (val) {
          this.generalSettings.soundAndDisplayTip.voiceNotice = false
          this.generalSettings.soundAndDisplayTip.channelFreeNotice = false
          this.generalSettings.soundAndDisplayTip.allowCallInstruction = false
        }
      },
      // mixins计算属性
      selectDeviceData(data) {
        this.clearPrivateConfig()

        this.$nextTick(() => {
          if (data) {
            // 将选中的设备中关于信道等数据同步到界面中
            this.selectDeviceDataChanged(data)
          }
        })
      },
    },
    components: {
      WriteFreqFooter,
      bfInputNumber: defineAsyncComponent(() => import('@/components/common/bfInputNumber')),
      addressBook: defineAsyncComponent(() => import('@/platform/dataManage/deviceManage/common/addressBook')),
      phoneBook: defineAsyncComponent(() => import('@/platform/dataManage/deviceManage/common/phoneBook')),
      shortMessage: defineAsyncComponent(() => import('@/platform/dataManage/deviceManage/common/shortMessage')),
      receiveGroup: defineAsyncComponent(() => import('@/platform/dataManage/deviceManage/common/receiveGroup')),
      deviceInfo,
      patrolConfig: defineAsyncComponent(() => import('@/platform/dataManage/deviceManage/common/patrolConfig')),
      emergencyAlarmConfig: defineAsyncComponent(() => import('@/platform/dataManage/deviceManage/common/emergencyAlarmConfig')),
      selectDevice,
      TimeZone: defineAsyncComponent(() => import('@/platform/dataManage/deviceManage/common/TimeZoneV2.vue')),
      virtualClusterV0: defineAsyncComponent(() => import('@/platform/dataManage/deviceManage/common/virtualCluster/virtualCluster_v0')),
      encryptSettings: defineAsyncComponent(() => import('@/platform/dataManage/deviceManage/common/encryptSettings')),
      channelV9: defineAsyncComponent(() => import('@/platform/dataManage/deviceManage/common/channel/channel_v9')),
      SiteInfo_511svt: defineAsyncComponent(() => import('@/platform/dataManage/deviceManage/common/SiteInfo_511svt.vue')),
    },
    mounted() {
      // 常规配置的语言选项，根据语言选择重置默认值
      if (this.generalSettings.soundAndDisplayTip.locale !== undefined) {
        const localeList = this.localeList ?? []
        const localeOption = localeList.find(opt => opt.value === this.generalSettings.soundAndDisplayTip.locale)
        this.generalSettings.soundAndDisplayTip.locale = localeOption?.value ?? localeList[0]?.value ?? 0
      }
    },
    beforeMount() {
      this.initDigitalAlarm()
      this.initScanGroup()
      this.initRoamGroup()
      this.initSiteInfoList()

      bfglob.on('vdevices_table_update_data', this.updateDeviceData)
      bfglob.on('vdevices_table_delete_data', this.deleteDeviceData)
      bfglob.on('device_channel_changed', this.globalDeviceChannelsChanged)
    },
    beforeUnmount() {
      // Vue实例销毁前，取消订阅的一些方法主题
      bfglob.off('vdevices_table_update_data', this.updateDeviceData)
      bfglob.off('vdevices_table_delete_data', this.deleteDeviceData)
      bfglob.off('device_channel_changed', this.globalDeviceChannelsChanged)
    },
  }
</script>

<style lang="scss">
  @use '@/css/interphoneWf/tabsWf.scss' as *;
</style>
