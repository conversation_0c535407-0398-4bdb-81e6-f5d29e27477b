import { defineComponent, h, computed, ref } from 'vue'
import { ElDialog } from 'element-plus'
// 确保 ElDialog 样式被导入
import 'element-plus/es/components/dialog/style/css'
import './main.scss'

type ElDialogProps = InstanceType<typeof ElDialog>['$props']

export type BfDialogProps = ElDialogProps & {
  modelValue: boolean
  showHeader?: boolean
  hasBg?: boolean
}

export default defineComponent({
  name: 'BfDialog',
  props: {
    // 将 ElDialog 的属性作为组件的 props
    ...ElDialog.props,
    modelValue: {
      type: Boolean,
      default: false,
    },
    showHeader: {
      type: Boolean,
      default: true,
    },
    hasBg: {
      type: Boolean,
      default: true,
    },
  },
  emits: ['update:modelValue', 'open', 'opened', 'close', 'closed', 'open-auto-focus', 'close-auto-focus'],
  setup(props: BfDialogProps, { emit, slots, expose, attrs }) {
    const visible = computed({
      get() {
        return props.modelValue
      },
      set(value) {
        emit('update:modelValue', value)
      },
    })
    const CloseIcon = h('span', {
      class: 'w-full h-full close-icon',
    })

    // 构建传递给 ElDialog 的属性对象
    const dialogProps = computed(() => {
      const classes = ['bf-dialog']
      if (!props.showHeader) {
        classes.push('bf-dialog--no-header')
      }
      if (props.hasBg) {
        classes.push('bf-dialog--has-bg')
      }
      if (attrs.class) {
        classes.push(attrs.class as string)
      }

      return {
        ...attrs,
        ...props,
        closeIcon: props.closeIcon ?? CloseIcon,
        destroyOnClose: props.destroyOnClose ?? true,
        class: classes.filter(Boolean).join(' '),
        modelValue: visible.value,
        'onUpdate:modelValue': (val: boolean) => {
          visible.value = val
        },
        onOpen: () => emit('open'),
        onOpened: () => emit('opened'),
        onClose: () => emit('close'),
        onClosed: () => emit('closed'),
        'onOpen-auto-focus': () => emit('open-auto-focus'),
        'onClose-auto-focus': () => emit('close-auto-focus'),
      }
    })

    // 向父组件暴露 dialogRef
    const dialogRef = ref<InstanceType<typeof ElDialog>>()
    expose({
      dialogRef,
    })

    // 使用 h 函数渲染 ElDialog
    return () =>
      h(
        ElDialog,
        {
          ...dialogProps.value,
          ref: dialogRef,
        },
        slots
      )
  },
})
