<template>
  <div class="w-full freq-input-wrap">
    <bf-input v-model="mhzStr" :disabled="disabled" class="freq-input" :maxlength="maxlength" :formatter="formatter" @input="mhzOnInput" />
  </div>
</template>

<script>
  import { frequencyHz2Mhz, frequencyMhz2Hz } from '@/utils/bfutil'
  import { debounce } from 'lodash'
  import bfInput from '@/components/bfInput/main'

  export default {
    name: 'FrequencyMhz',
    emits: ['update:modelValue'],
    props: {
      modelValue: {
        type: Number,
        default: 0,
      },
      disabled: {
        type: Boolean,
        default: false,
      },
      maxlength: {
        type: Number,
      },
    },
    components: {
      bfInput,
    },
    data() {
      return {
        hzNum: 0,
        mhzStr: '',
      }
    },
    methods: {
      setHzNum(val) {
        if (this.hzNum === val) return
        this.hzNum = val
        this.$emit('update:modelValue', val)
      },
      mhzOnInput(val) {
        // 过滤后的值为空，则结束
        if (val === '') {
          this.setHzNum(0)
          return
        }

        this.setHzNum(frequencyMhz2Hz(val))
      },
      // 过滤非法字符, 只允许输入数字字符串
      formatter(value) {
        // 以'.'开始，默认为'0.'
        if (/^\./gi.test(value)) {
          value = '0.'
          return value
        }

        // 仅保留如"123.456"数值字符串
        return value.replace(/[^.\d]/g, '').replace(/(\w+(\.\w+))\..*/, (_, v) => v)
      },
    },
    watch: {
      modelValue: {
        immediate: true,
        handler(val) {
          this.hzNum = val
          const mhzStr = frequencyHz2Mhz(val)
          this.mhzStr = mhzStr.length >= 10 ? mhzStr.slice(0, 10) : mhzStr
        },
      },
    },
    beforeMount() {
      this.mhzOnInput = debounce(this.mhzOnInput, 350)
    },
  }
</script>

<style></style>
