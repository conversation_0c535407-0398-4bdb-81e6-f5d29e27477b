<template>
  <data-form-editor
    ref="formEditor"
    class="page-org"
    :title="$t('dialog.orgTitle')"
    :tableName="dataTable.name"
    :data="dataTable.body"
    :column="dthead"
    :getNewData="getNewData"
    :checkDataDisable="checkDataDisable"
    :beforeAction="beforeAction"
    :beforeConfirm="beforeConfirm"
    :getFormRef="() => $refs.orgDataEditorForm"
    @row-delete="onDelete"
    @row-update="onUpdate"
    @row-new="onNew"
    :show-close="true"
  >
    <template #form="{ formData, isNewStatus }">
      <el-form
        ref="orgDataEditorForm"
        :model="formData"
        :label-width="labelWidth"
        :rules="getRules(formData, isNewStatus)"
        :validate-on-rule-change="false"
        label-position="left"
        class="org-form grid grid-cols-1"
      >
        <el-form-item prop="orgSelfId">
          <template #label>
            <EllipsisText :content="$t('dialog.serialNo') + ':'" class="text-base font-medium text-[#fff] h-[50px] leading-[50px]" />
          </template>
          <BfInput v-model="formData.orgSelfId" :maxlength="16" class="h-[50px]" />
        </el-form-item>
        <el-form-item prop="parentOrgId">
          <template #label>
            <EllipsisText :content="$t('dialog.parentOrgName') + ':'" class="text-base font-medium text-[#fff] h-[50px] leading-[50px]" />
          </template>
          <BfSelect
            ref="parentOrgSelect"
            v-model="formData.parentOrgId"
            class="parent-org-select bf-select h-[50px]"
            :placeholder="$t('dialog.select')"
            filterable
            clearable
            :filter-method="onSelectFilter"
          >
            <el-option :value="formData.parentOrgId" :label="getOrgLabelById(formData.parentOrgId)" class="!hidden" />
            <el-tree
              ref="selectTree"
              class="org-select-tree !bg-transparent"
              :data="getOrgTreeSelectOptions(formData, isNewStatus)"
              node-key="value"
              :props="{ label: 'label', children: 'children', disabled: 'disabled' }"
              highlight-current
              :icon="parentOrgTreeIcon"
              :expand-on-click-node="false"
              :filter-node-method="filterNode"
              @node-click="handleNodeClick"
            >
              <template #default="{ data }">
                <template v-if="data.noPerm">
                  <span class="italic !text-gray-300 line-through">{{ data.label }}</span>
                </template>
                <template v-else>
                  {{ data.label }}
                </template>
              </template>
            </el-tree>
          </BfSelect>
        </el-form-item>
        <el-form-item prop="orgShortName">
          <template #label>
            <EllipsisText :content="$t('dialog.orgShortName') + ':'" class="text-base font-medium text-[#fff] h-[50px] leading-[50px]" />
          </template>
          <BfInput v-model="formData.orgShortName" :maxlength="32" class="h-[50px]" />
        </el-form-item>
        <el-form-item prop="orgFullName">
          <template #label>
            <EllipsisText :content="$t('dialog.orgFullName') + ':'" class="text-base font-medium text-[#fff] h-[50px] leading-[50px]" />
          </template>
          <BfInput v-model="formData.orgFullName" :maxlength="64" class="h-[50px]" />
        </el-form-item>
        <el-form-item prop="dmrId">
          <template #label>
            <EllipsisText content="DMRID:" class="text-base font-medium text-[#fff] h-[50px] leading-[50px]" />
          </template>
          <dmrid-input v-model="formData.dmrId" isGroup />
        </el-form-item>
        <el-form-item prop="orgSortValue">
          <template #label>
            <EllipsisText :content="$t('dialog.sortValue') + ':'" class="text-base font-medium text-[#fff] h-[50px] leading-[50px]" />
          </template>
          <BfInputNumber2
            v-model="formData.orgSortValue"
            class="orgs-sortValue h-[50px] !w-full"
            :placeholder="$t('msgbox.sortAdvice')"
            :min="-2147483648"
            :max="2147483648"
          />
        </el-form-item>
        <el-form-item prop="note">
          <template #label>
            <EllipsisText :content="$t('dialog.notes') + ':'" class="text-base font-medium text-[#fff] h-[50px] leading-[50px]" />
          </template>
          <BfInput v-model="formData.note" type="textarea" resize="none" :rows="3" :maxlength="256" />
        </el-form-item>
        <el-form-item>
          <BfCheckbox v-model="formData.orgIsVirtual" :true-value="1" :false-value="2" @change="isAddMapPoint = false">
            <span class="text-[16px]" v-text="$t('dialog.virOrg')" />
          </BfCheckbox>
        </el-form-item>
        <el-form-item v-if="isNewStatus && !isRootUser">
          <el-tooltip popper-class="bf-tooltip" effect="dark" :content="$t('dialog.orgsAddMapPointMsg')" placement="right">
            <BfCheckbox v-model="isAddMapPoint" :disabled="formEditor?.formData.orgIsVirtual === 1">
              <span class="text-[16px]" v-text="$t('dialog.addMapPoint')" />
            </BfCheckbox>
          </el-tooltip>
        </el-form-item>
      </el-form>
    </template>

    <template #form-footer="{ onClose, onConfirm, isNewStatus, formData }">
      <div class="flex justify-center gap-3 dialog-footer">
        <BfBtn color-type="info" @click="onClose">
          {{ $t('dialog.back') }}
        </BfBtn>
        <BfBtn color-type="warning" @click="onConfirm(isNewStatus)">
          {{ $t('dialog.confirm') }}
        </BfBtn>
        <template v-if="!isNewStatus && !isRootUser">
          <BfBtn color-type="warning" :disabled="formData.orgIsVirtual !== 2" @click="addOrUpdateOrgMapPoint(formData)">
            {{ $t('dialog.updateMapPoint') }}
          </BfBtn>
        </template>
      </div>
    </template>
  </data-form-editor>
</template>

<script>
  import { mapFlyTo } from '@/utils/map'
  import bfproto from '@/modules/protocol'
  import dbCmd from '@/modules/protocol/db.pb.cmd'
  import bfprocess from '@/utils/bfprocess'
  import bftree from '@/utils/bftree'
  import bfutil, { formatDmrIdLabel, getCommonOrgType } from '@/utils/bfutil'
  import bfNotify from '@/utils/notify'
  import { v1 as uuid } from 'uuid'
  import vueMixin from '@/utils/vueMixin'
  import DataFormEditor, { EditStatus } from '@/components/common/DataFormEditor.vue'
  import { cloneDeep } from 'lodash'
  import validateRules from '@/utils/validateRules'
  import { useRouteParams } from '@/router'
  import { defineAsyncComponent } from 'vue'
  import BfInput from '@/components/bfInput/main'
  import EllipsisText from '@/components/common/EllipsisText.vue'
  import BfCheckbox from '@/components/bfCheckbox/main'
  import BfInputNumber2 from '@/components/bfInputNumber/main'
  import { h } from 'vue'
  import BfBtn from '@/components/bfButton/main'
  import { convertPxToRem, calcScaleSize } from '@/utils/setRem'
  import BfSelect from '@/components/bfSelect/main'

  const dbSubject = `db.${bfglob.sysId}`
  const commonOrgType = getCommonOrgType()
  const filterDynamicGroup = dataList => {
    return dataList.filter(data => commonOrgType.includes(data.orgIsVirtual))
  }

  // 存储formData为key validateDmrId为value的弱映射
  const validateDmrIdWeakMap = new WeakMap()
  const { setRouteParams } = useRouteParams()

  export default {
    name: 'BfOrgs',
    mixins: [vueMixin],
    data() {
      const allOrgData = bfglob.gorgData.getAll()

      bfglob.orgsIsLoaded?.then(() => {
        const allOrgData = bfglob.gorgData.getAll()
        this.allOrgData = allOrgData
        this.dataTable.body = filterDynamicGroup(bfutil.objToArray(allOrgData))
      })

      return {
        allOrgData,
        dataTable: {
          name: 'orgsTable',
          body: filterDynamicGroup(bfutil.objToArray(allOrgData)),
        },
        selOrgList: bfglob.gorgData.getList(),
        isAddMapPoint: false,
        defaultData: {
          parentOrgId: bfutil.getBaseDataOrgId(),
          orgSelfId: bfutil.getBaseSelfId(),
          creator: bfglob.userInfo.rid,
          dmrId: '',
          dynamicGroupState: 0,
          note: '',
          orgFullName: '',
          orgImg: '',
          orgIsVirtual: 2,
          orgShortName: '',
          orgSortValue: 100,
          rid: '',
          setting: '{}',
          updateAt: '',
        },
      }
    },
    methods: {
      // 根据当前编辑的数据，进行禁用对应的单位选项，主要禁用当前数据的所有子节点
      getOrgTreeSelectOptions(row, isNewStatus) {
        if (isNewStatus) {
          return this.orgTreeSelectOptions
        }

        const options = cloneDeep(this.orgTreeSelectOptions)

        // Recursive function to update disabled status of node and its children
        const updateNodeDisabled = (node, parentDisabled) => {
          // Set node's disabled status based on whether its value matches row's parentOrgId
          node.disabled = row.rid === node.value || parentDisabled

          // no permission, disable
          if (node.noPerm) {
            node.disabled = true
          }

          // Recursively update all children with the same disabled status
          if (node.children?.length > 0) {
            node.children.forEach(child => updateNodeDisabled(child, node.disabled))
          }
        }

        // Process all root nodes
        options.forEach(node => updateNodeDisabled(node, false))

        return options
      },
      // el-select filter hook -> delegate to el-tree filter
      onSelectFilter(query) {
        this.$nextTick(() => {
          const tree = this.$refs.selectTree
          if (tree && typeof tree.filter === 'function') {
            tree.filter(query)
          }
        })
      },
      // el-tree filter method
      filterNode(value, data) {
        if (!value) return true
        const label = (data?.label ?? '').toString().toLowerCase()
        return label.includes(String(value).toLowerCase())
      },
      // click tree node to set select value and close dropdown
      handleNodeClick(data) {
        if (!data || data.disabled) return
        const rid = data.value
        if (this.$refs?.orgDataEditorForm?.model) {
          this.$refs.orgDataEditorForm.model.parentOrgId = rid
        } else if (this.formEditor?.formData) {
          this.formEditor.formData.parentOrgId = rid
        }
        // close dropdown
        try {
          this.$refs.parentOrgSelect?.blur?.()
          this.$refs.parentOrgSelect?.selectRef?.blur?.()
        } catch (e) {
          console.error('handleNodeClick', e)
        }
      },
      // get label for current selected org id (used by hidden option)
      getOrgLabelById(rid) {
        if (!rid || rid === bfutil.DefOrgRid) {
          return this.$t('dialog.default')
        }
        const org = bfglob.gorgData.get(rid) || bfglob.noPermOrgData.get(rid)
        return org?.orgShortName || this.selOrgList?.[rid]?.label || ''
      },
      closeEditorFromMapPoints() {
        this.$refs.formEditor?.onClose()
      },
      getRules(formData, _isNewStatus) {
        if (!validateDmrIdWeakMap.has(formData)) {
          const validateDmrId = validateRules.validateDmrId({
            encodeMsgType: 'db_org',
            decodeMsgType: 'db_org_list',
            command: dbCmd.DB_ORG_GETBY,
            dataManager: bfglob.gorgData,
          })
          validateDmrIdWeakMap.set(formData, validateDmrId)
        }

        return {
          orgSelfId: [
            {
              required: true,
              message: this.$t('dialog.requiredRule'),
              trigger: 'blur',
            },
          ],
          orgShortName: [
            {
              required: true,
              message: this.$t('dialog.requiredRule'),
              trigger: 'blur',
            },
          ],
          parentOrgId: [
            {
              required: true,
              message: this.$t('dialog.requiredRule'),
              trigger: 'change',
            },
          ],
          dmrId: [
            {
              validator: (rule, value, callback) => {
                if (value !== formData.dmrId) {
                  return callback()
                }
                const org = bfglob.gorgData.getDataByIndex(value)
                if (org && org.rid === formData.rid) {
                  return callback()
                }
                const validate = validateDmrIdWeakMap.get(formData)
                if (validate) {
                  validate(rule, value, callback)
                } else {
                  callback()
                }
              },
              trigger: 'change',
            },
          ],
        }
      },
      async onDelete(row) {
        await this.delete_org_data(row, dbCmd.DB_ORG_DELETE)
      },
      async onUpdate(row, done) {
        const isOk = await this.update_org_data(row, dbCmd.DB_ORG_UPDATE)
        if (!isOk) return
        done()
      },
      // addNewCb：存在这个回调函数则需要继续添加新的一行
      async onNew(row, done, addNewCb) {
        const isOk = await this.add_org_data(row, dbCmd.DB_ORG_INSERT)
        if (!isOk) return
        if (addNewCb) {
          const __data = this.getNewData()
          __data.dmrId = bfutil.dmrIdAutoIncrement(row.dmrId)
          __data.orgSelfId = bfutil.customNumberIncrement(row.orgSelfId)
          __data.orgShortName = bfutil.customNumberIncrement(row.orgShortName)
          __data.parentOrgId = row.parentOrgId
          // 重置标签页数据
          bfutil.resetForm(this, 'orgDataEditorForm')
          addNewCb(__data)
          return
        }
        done()
      },
      // 返回一个新的默认参数对象
      getNewData() {
        return cloneDeep(this.defaultData)
      },
      /**
       * 编辑数据前置执行方法，允许拒绝编辑或添加新数据
       * @param {number} status Add: 1, Edit: 2
       * @param {Record<string, any>?} row
       * @returns {Promise<boolan>}
       */
      beforeAction(status, data) {
        // 超级管理员允许编辑单位数据
        if (!bfglob.userInfo.isSuper && bfutil.notEditDataPermission()) {
          return Promise.reject('No permission')
        }
        if (status === EditStatus.Add) {
          data.dmrId = data.dmrId === '' ? '80080000' : data.dmrId
          while (bfglob.gorgData.getDataByIndex(data.dmrId)) {
            data.dmrId = bfutil.dmrIdAutoIncrement(data.dmrId)
            if (data.dmrId === '800FA11F') {
              break
            }
          }
        }

        return Promise.resolve(true)
      },
      /**
       * 编辑对话框的确定按钮前置执行方法，主要处理一些特殊字段
       * @param {Record<string, any>} row
       * @param {number} status Add: 1, Edit: 2
       */
      beforeConfirm(row, status) {
        if (status === 2 && !this.checkLegalityOfParentOrgId(row)) {
          bfNotify.warningBox(this.$t('msgbox.parentNotIsSelfOrSubUnit'))
          return Promise.reject('Can not select self or children')
        }

        return Promise.resolve(true)
      },
      /**
       * 检查一行数据的操作列是否禁用，例如没有权限
       * @param {Record<string, any>} row 被检查的数据
       * @param {Record<string, any>} meta datatables的列配置的render方法的meta参数
       * @returns {boolean}
       */
      checkDataDisable(row, _meta) {
        try {
          const settings = JSON.parse(row.setting || '{}')
          return !!settings.controlDmrId
        } catch (e) {
          bfglob.console.error('checkDataDisable JSON.parse catch:', e)
          return false
        }
      },

      // onRowDblclick(data) {
      //   if (!data) {
      //     return
      //   }
      //   // use relative org marker lonlat
      //   const lonlatInfo = bfglob.gorgData.getOrgMapMakerPointLonlatInfo(data.rid)
      //   if (!lonlatInfo.lonlat) {
      //     return
      //   }
      //   bfNotify.messageBox(this.$t('dialog.orgPointJumpTip'))
      //   mapFlyTo(lonlatInfo.lonlat, lonlatInfo.showLevel)
      // },
      addOrUpdateOrgMapPoint(orgData) {
        const mapPoint = bfglob.gorgData.getOrgMapMakerPoint(orgData.rid)
        // if (this.$el?.classList?.contains('first-dialog')) {
        //   this?.$el?.classList?.replace('first-dialog', 'other-dialog')
        // }
        if (mapPoint) {
          //update
          this.updateOrgMapPoint(mapPoint)
        } else {
          //add
          this.addOrgMapPoint(orgData, 1)
        }
        this.$refs.formEditor.closeDialog()
      },
      addOrgMapPoint(orgData, orgNoMapPointMsg = 0) {
        const params = {
          addOrgMapPoint: true,
          addOrUpdate: 0, // 0: 添加, 1: 修改
          orgNoMapPointMsg, // 默认0: 不提示当前单位没有地图标记点 , 1: 提示
          dbMapPoint: {
            rid: orgData.rid,
            orgId: orgData.rid || '00000000-0000-0000-0000-000000000000',
            selfId: orgData.orgSelfId,
            pointName: orgData.orgShortName,
            mapDisplayName: orgData.orgShortName,
            markerType: 1,
          },
        }
        setRouteParams('MapPoints', params)
        this.$router.push({
          path: '/dataManage/otherManage/MapPoints',
          query: this.$route.query, // 保持查询参数
        })
      },
      updateOrgMapPoint(mapPointData) {
        const params = {
          addOrgMapPoint: true,
          addOrUpdate: 1,
          mapPointData,
        }
        setRouteParams('MapPoints', params)
        this.$router.push({
          path: '/dataManage/otherManage/MapPoints',
          query: this.$route.query, // 保持查询参数
        })
      },
      tryOpenMapPointDlg(orgData) {
        if (this.isAddMapPoint) {
          this.addOrgMapPoint(orgData)
        }
      },
      add_org_data(data, add_cmd) {
        const msgObj = {
          ...data,
          rid: uuid(),
          parentOrgId: data.parentOrgId || '00000000-0000-0000-0000-000000000000',
          orgSortValue: parseInt(data.orgSortValue) || 100,
          orgFullName: data.orgFullName || data.orgShortName,
          orgImg: '11111111-1111-1111-1111-111111111111',
          setting: '{}',
          creator: bfglob.userInfo.rid,
        }

        return bfproto
          .sendMessage(add_cmd, msgObj, 'db_org', dbSubject)
          .then(rpc_cmd_obj => {
            bfglob.console.log('add org res:', rpc_cmd_obj)
            if (rpc_cmd_obj.resInfo === '+OK') {
              bfNotify.messageBox(this.$t('msgbox.addSuccess'), 'success')
              bfglob.emit('add_global_orgData', msgObj)
              // 是否勾选添加地图标记点
              this.tryOpenMapPointDlg(msgObj)

              // 重置标签页数据
              const __data = Object.assign({}, data)
              this.defaultData.dmrId = bfutil.dmrIdAutoIncrement(__data.dmrId)
              this.defaultData.orgSelfId = bfutil.customNumberIncrement(__data.orgSelfId)
              this.defaultData.parentOrgId = __data.parentOrgId

              // 为当前账号添加该组织权限
              const privelege_data = {
                rid: uuid(),
                userRid: bfglob.userInfo.rid,
                userOrg: msgObj.rid,
                includeChildren: 0,
              }
              bfprocess.add_user_privelege(privelege_data, dbCmd.DB_USER_PRIVELEGE_INSERT)

              // 添加查询日志
              const content = this.$t('dialog.add') + __data.orgSelfId + '/' + __data.orgShortName + this.$t('msgbox.orgData')
              bfglob.emit('addnote', content)
            } else {
              if (rpc_cmd_obj.resInfo.includes('db_org_org_self_id_key')) {
                bfNotify.warningBox(this.$t('msgbox.repeatNo'))
              } else if (rpc_cmd_obj.resInfo.includes('db_org_dmr_id_key')) {
                bfNotify.warningBox(this.$t('msgbox.repeatDMRID'))
              } else if (rpc_cmd_obj.resInfo.includes('db_org_org_short_name_key')) {
                bfNotify.warningBox(this.$t('msgbox.repeatOrgShortName'))
              } else {
                bfNotify.messageBox(this.$t('msgbox.addError') + ' ' + rpc_cmd_obj.resInfo, 'error')
              }
            }
            return Promise.resolve(rpc_cmd_obj.resInfo === '+OK')
          })
          .catch(err => {
            bfglob.console.warn('add org timeout:', err)
            bfNotify.messageBox(this.$t('msgbox.addError'), 'error')
            return Promise.resolve(false)
          })
      },
      checkMapPointWithOrgIsVirtual(dbOrg) {
        if (dbOrg.orgIsVirtual !== 1) {
          return
        }
        const mapPoint = bfglob.gmapPoints.get(dbOrg.rid)
        if (!mapPoint) {
          return
        }

        // 尝试删除对应的地图标记点
        bfproto
          .sendMessage(dbCmd.DB_MAP_POINT_DELETE, mapPoint, 'db_map_point', dbSubject)
          .then(rpc_cmd_obj => {
            bfglob.console.log('delete mapPoint res:', rpc_cmd_obj)
            bfglob.emit('delete_global_mapPointData', mapPoint)
          })
          .catch(err => {
            bfglob.console.warn('delete mapPoint timeout:', err)
          })
      },
      update_org_data(data, up_db_cmd) {
        const msgObj = {
          ...data,
          parentOrgId: data.parentOrgId || bfutil.DefOrgRid,
          orgSelfId: data.orgSelfId,
          orgSortValue: parseInt(data.orgSortValue) || 100,
          orgFullName: data.orgFullName || data.orgShortName,
        }
        return bfproto
          .sendMessage(up_db_cmd, msgObj, 'db_org', dbSubject)
          .then(rpc_cmd_obj => {
            bfglob.console.log('update org res:', rpc_cmd_obj)
            if (rpc_cmd_obj.resInfo === '+OK') {
              bfNotify.messageBox(this.$t('msgbox.upSuccess'), 'success')

              // 更新全局组织机构数据
              bfglob.emit('update_global_orgData', msgObj)

              // 是虚拟单位，则检测是否有地图标记点，有则删除
              this.checkMapPointWithOrgIsVirtual(msgObj)

              // 添加查询日志
              const note = this.$t('dialog.update') + msgObj.orgSelfId + '/' + msgObj.orgShortName + this.$t('msgbox.orgData')
              bfglob.emit('addnote', note)
            } else {
              if (rpc_cmd_obj.resInfo.includes('db_org_org_self_id_key')) {
                bfNotify.warningBox(this.$t('msgbox.repeatNo'))
              } else if (rpc_cmd_obj.resInfo.includes('db_org_dmr_id_key')) {
                bfNotify.warningBox(this.$t('msgbox.repeatDMRID'))
              } else if (rpc_cmd_obj.resInfo.includes('db_org_org_short_name_key')) {
                bfNotify.warningBox(this.$t('msgbox.repeatOrgShortName'))
              } else if (rpc_cmd_obj.resInfo.includes('Cannot set parent_org_id to one of its child organizations')) {
                bfNotify.warningBox(this.$t('msgbox.cannotSetAsSubOrg'))
              } else {
                bfNotify.messageBox(this.$t('msgbox.upError') + ' ' + rpc_cmd_obj.resInfo, 'error')
              }
            }
            return Promise.resolve(rpc_cmd_obj.resInfo === '+OK')
          })
          .catch(err => {
            bfglob.console.warn('update org timeout:', err)
            bfNotify.messageBox(this.$t('msgbox.upError'), 'error')
            return Promise.resolve(false)
          })
      },
      // 需要检测上级单位的合法性,
      checkLegalityOfParentOrgId(row) {
        // 如果当前单位的上级是根节点，则可直接更新
        if (!row.parentOrgId || row.parentOrgId === bfutil.DefOrgRid) {
          return true
        }

        // 上级不能是自己
        if (row.parentOrgId === row.rid) {
          return false
        }

        // 借助于单位、设备列表树，快速找到所有子级数据
        const node = bftree.getTreeNodeByRid('bftree', row.rid)
        if (!node) {
          return false
        }

        // 没有子节点，则不会将上级更新下子级单位
        const children = node.getChildren()
        if (!children) {
          return true
        }

        let isValid = true
        const checkChildren = children => {
          for (let i = 0; i < children.length; i++) {
            const item = children[i]
            // 跳过设备节点
            if (!item.isFolder()) {
              continue
            }

            // 如果节点的key与当前单位设置的上级相同，则不能更新单位
            if (item.key === row.parentOrgId) {
              isValid = false
              return isValid
            }

            // 如有子节点，则递归处理
            const _children = item.getChildren()
            if (!_children) {
              continue
            }

            // 如果返回true,则检查下个节点
            const vaild = checkChildren(_children)
            if (!vaild) {
              return false
            }
          }
          return true
        }

        return checkChildren(children)
      },
      delete_org_data(data, del_cmd) {
        return bfproto
          .sendMessage(del_cmd, data, 'db_org', dbSubject)
          .then(rpc_cmd_obj => {
            bfglob.console.log('delete org res:', rpc_cmd_obj)
            if (rpc_cmd_obj.resInfo === '+OK') {
              bfNotify.messageBox(this.$t('msgbox.delSuccess'), 'success')
              bfglob.emit('delete_global_orgData', data)

              // 添加查询日志
              const note = this.$t('dialog.delete') + data.orgSelfId + '/' + data.orgShortName + this.$t('msgbox.orgData')
              bfglob.emit('addnote', note)
            } else {
              if (rpc_cmd_obj.resInfo.includes('db_line_point')) {
                bfNotify.warningBox(this.$t('msgbox.disDelOrg'))
              } else {
                bfNotify.messageBox(this.$t('msgbox.delError') + ' ' + rpc_cmd_obj.resInfo, 'error')
              }
            }
            return Promise.resolve(rpc_cmd_obj.resInfo === '+OK')
          })
          .catch(err => {
            bfglob.console.warn('delete org timeout:', err)
            bfNotify.messageBox(this.$t('msgbox.delError'), 'error')
            return Promise.resolve(false)
          })
      },
      // 同步dataTable数据
      upsetDataTableBody() {
        this.dataTable.body = filterDynamicGroup(bfutil.objToArray(bfglob.gorgData.getAll()))
      },
    },
    components: {
      DmridInput: defineAsyncComponent(() => import('@/components/common/DmridInput.vue')),
      DataFormEditor,
      BfInput,
      EllipsisText,
      BfCheckbox,
      BfInputNumber2,
      BfBtn,
      BfSelect,
    },
    watch: {
      allOrgData: {
        deep: true,
        handler(val) {
          this.dataTable.body = filterDynamicGroup(bfutil.objToArray(val))
        },
      },
    },
    computed: {
      formEditor() {
        return this.$refs.formEditor
      },
      isRootUser() {
        return bfglob.userInfo.isSuper
      },
      dthead() {
        return [
          {
            title: this.$t('dialog.serialNo'),
            data: 'orgSelfId',
            width: this.isEN || this.isFR ? '80px' : '60px',
          },
          {
            title: this.$t('dialog.parentOrgName'),
            data: 'parentOrgId',
            width: '100px',
            render: data => {
              if (data === bfutil.DefOrgRid) {
                return this.$t('dialog.default')
              }

              const parent = bfglob.gorgData.get(data)
              if (!parent) {
                const item = bfglob.noPermOrgData.get(data)
                if (item) {
                  return item.orgShortName
                }
              }

              return parent?.orgShortName ?? ''
            },
          },
          {
            title: this.$t('dialog.orgShortName'),
            data: 'orgShortName',
            width: '100px',
          },
          {
            title: this.$t('dialog.orgFullName'),
            data: 'orgFullName',
            width: '150px',
          },
          {
            title: this.$t('dialog.orgDMRID'),
            data: 'dmrId',
            width: this.isFR ? '165px' : '135px',
            render: data => {
              return formatDmrIdLabel(data)
            },
          },
          {
            title: this.$t('dialog.sortValue'),
            data: 'orgSortValue',
            width: '60px',
          },
          {
            title: this.$t('dialog.virOrg'),
            data: null,
            width: this.isEN || this.isFR ? '135px' : '60px',
            render: (data, _type, _row, _meta) => {
              const str = data.orgIsVirtual === 1 ? 'dialog.yes' : 'dialog.no'
              return this.$t(str)
            },
          },
          {
            title: this.$t('dialog.notes'),
            data: 'note',
            width: '150px',
          },
        ]
      },
      labelWidth() {
        return convertPxToRem(calcScaleSize(100)) + 'rem'
      },
      orgSelectOptions() {
        return {
          [bfutil.DefOrgRid]: {
            label: 'root',
            rid: bfutil.DefOrgRid,
          },
          ...this.selOrgList,
        }
      },
      parentOrgTreeIcon() {
        return h('span', {
          class: 'bf-iconfont bfdx-xiala text-[20px]',
        })
      },
    },
    mounted() {
      bfglob.on('add_global_orgData', this.upsetDataTableBody)
      bfglob.on('update_global_orgData', this.upsetDataTableBody)
      bfglob.on('delete_global_orgData', this.upsetDataTableBody)
      bfglob.on('close_org_editor_dialog', this.closeEditorFromMapPoints)
    },
  }
</script>

<style lang="scss">
  // bfInput样式设置
  .el-form.org-form {
    .el-form-item {
      .bf-input {
        .el-input__wrapper {
          height: 50px;
        }

        .el-input__inner {
          height: 50px;
          line-height: 50px;
        }
      }
    }
  }

  .bf-select.parent-org-select {
    .org-select-tree.el-tree {
      .el-tree-node.is-current,
      .el-tree-node:hover {
        .el-tree-node__content {
          background-color: rgba(6, 121, 204, 0.5);
        }
      }
    }
  }
</style>
