<template>
  <div>
    <div class="flex flex-nowrap gap-3 generate-dmrId-wrap" :class="{ disabled: disabled }">
      <bf-input-number-v2
        v-model.number="mmm"
        class="flex-none generate-dmrId-number h-[50px] w-[30%]"
        :size="size"
        :min="minNo"
        :max="maxDmrNo"
        :disabled="disabled"
        @change="mmmChange"
      ></bf-input-number-v2>
      <bf-input v-model="dmrIdLabel" class="generate-dmrId-input grow" :size="size" readonly :suffix-icon="inputSuffixIcon"></bf-input>
    </div>

    <!-- DMR ID 解析对话框 -->
    <bf-dialog
      v-model="dialogVisible"
      :title="$t('dialog.alertTitle')"
      width="400px"
      center
      top="30vh"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      class="dmrid-input-dialog"
      show-close
    >
      <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium mb-2">{{ $t('msgbox.resolveDmrId') }}</label>
          <bf-input v-model="inputValue" :placeholder="$t('msgbox.enterCorrectDMRID')" class="w-full" @keyup.enter="handleConfirm" />
          <div v-if="inputError" class="text-red-500 text-sm mt-1">{{ inputError }}</div>
        </div>
      </div>

      <template #footer>
        <div class="flex justify-center gap-2">
          <bf-button @click="handleCancel">{{ $t('dialog.cancel') }}</bf-button>
          <bf-button color-type="warning" @click="handleConfirm">{{ $t('dialog.confirm') }}</bf-button>
        </div>
      </template>
    </bf-dialog>
  </div>
</template>

<script>
  import { getDmrMaxNo, ssmmm2dmrid, dmrid2ssmmm, formatDmrIdLabel } from '@/utils/bfutil'
  import bfInputNumberV2 from '@/components/bfInputNumber/main'
  import bfInput from '@/components/bfInput/main'
  import bfDialog from '@/components/bfDialog/main'
  import bfButton from '@/components/bfButton/main'
  import { h } from 'vue'

  export default {
    name: 'DmridInput',
    emits: ['update:modelValue'],
    components: {
      bfInputNumberV2,
      bfInput,
      bfDialog,
      bfButton,
    },
    props: {
      modelValue: {
        type: [String, Number],
        required: true,
      },
      // 禁用编号输入
      disabled: {
        type: Boolean,
        default: false,
      },
      // 标记dmrId是否为组呼
      isGroup: {
        type: Boolean,
        default: false,
      },
      maxNo: {
        type: Number,
        default: getDmrMaxNo(),
      },
      minNo: {
        type: Number,
        default: 0,
      },
      isDec: {
        type: Boolean,
        default: false,
      },
      size: {
        type: String,
        // default: 'small',
      },
    },
    data() {
      return {
        mmm: this.minNo,
        // 代理商id，默认为0
        salerId: 0,
        // 除了组呼标志位的4字节最高位
        groupBit: 0x7fffffff,
        // 最高19bti的编号
        fullMmmBit: 0x07ffff,
        // 对话框控制
        dialogVisible: false,
        inputValue: '',
        inputError: '',
      }
    },
    watch: {
      modelValue: {
        immediate: true,
        handler(val) {
          if (val === '' || val === 0) {
            this.mmmChange(this.mmm)
            return
          }

          if (typeof val === 'number') {
            this.mmm = val & this.fullMmmBit
            return
          }

          const res = dmrid2ssmmm(val)
          this.mmm = res.ok ? res.mmm : this.minNo
        },
      },
    },
    methods: {
      emitDmrId(dmrId) {
        if (this.isDec) {
          // 返回十进制dmrId
          this.$emit('update:modelValue', parseInt(dmrId, 16))
        } else {
          // 返回十六进制dmrId
          this.$emit('update:modelValue', dmrId)
        }
      },
      mmmChange(val) {
        const groupNo = this.isGroup ? 1 : 0
        const dmrId = ssmmm2dmrid(bfglob.sysId, val, groupNo, this.salerId)
        this.emitDmrId(dmrId)
      },
      showResolveFiledMessage() {
        ElMessage({
          message: this.$t('msgbox.resolveDmrIdFailed'),
          type: 'error',
        })
      },
      validator(val) {
        if (this.isGroup) {
          if (val > 0x80ffffff) {
            return false
          }
        } else if (val > 0x00ffffff) {
          return false
        }

        return true
      },
      resolveDmrId() {
        if (this.disabled) {
          return
        }
        this.inputValue = ''
        this.inputError = ''
        this.dialogVisible = true
      },
      validateInput(val) {
        if (!val) {
          return false
        }
        if (/(^[0-9]*$)|(^0x[0-9a-fA-F]*$)/.test(val)) {
          return this.validator(val)
        } else {
          return false
        }
      },
      handleConfirm() {
        if (!this.validateInput(this.inputValue)) {
          this.inputError = this.$t('msgbox.enterCorrectDMRID')
          return
        }

        try {
          const dmrIdHex = (this.inputValue & this.groupBit).toString(16).padStart(8, '0')
          const dmrObj = dmrid2ssmmm(dmrIdHex)
          if (!dmrObj.ok || dmrObj.mmm > this.maxDmrNo) {
            this.showResolveFiledMessage()
            this.dialogVisible = false
            return
          }
          this.mmm = dmrObj.mmm
          this.mmmChange(this.mmm)
          this.dialogVisible = false
        } catch (_err) {
          this.showResolveFiledMessage()
          this.dialogVisible = false
        }
      },
      handleCancel() {
        this.dialogVisible = false
      },
    },
    computed: {
      maxDmrNo() {
        return Math.min(this.maxNo, getDmrMaxNo(this.isGroup))
      },
      dmrIdLabel() {
        const dmrId = this.isDec ? this.modelValue.toString(16) : this.modelValue
        return formatDmrIdLabel(dmrId)
      },
      inputSuffixIcon() {
        return h('span', {
          class: `bf-iconfont bfdx-shezhi generate-dmrId-input-icon  ${this.disabled ? 'cursor-not-allowed' : 'cursor-pointer'}`,
          onClick: this.resolveDmrId,
        })
      },
    },
  }
</script>

<style lang="scss">
  .dmrid-input-dialog.el-dialog.bf-dialog.bf-dialog--has-bg {
    .el-dialog__body {
      padding: 16px;
    }
  }
</style>
