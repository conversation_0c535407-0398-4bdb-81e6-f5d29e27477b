import { createGlobalState } from '@vueuse/core'
import { ref } from 'vue'
import { Contact } from './types'

export const useCommonContact = createGlobalState(() => {
  const commonContacts = ref<Array<Contact>>([])

  const addCommonContact = (contact: Contact) => {
    commonContacts.value.push(contact)
  }

  const updateCommonContact = (contact: Contact) => {
    const index = commonContacts.value.findIndex(c => c.rid === contact.rid)
    if (index !== -1) {
      commonContacts.value[index] = {
        ...commonContacts.value[index],
        ...contact,
      }
    }
  }

  const removeCommonContact = (rid: string) => {
    const index = commonContacts.value.findIndex(c => c.rid === rid)
    if (index === -1) {
      return
    }
    commonContacts.value.splice(index, 1)
  }

  const moveCommonContact = (fromIndex: number, toIndex: number) => {
    if (fromIndex < 0 || fromIndex >= commonContacts.value.length || toIndex < 0 || toIndex >= commonContacts.value.length || fromIndex === toIndex) {
      return
    }

    // 交换数组元素位置
    const temp = commonContacts.value[fromIndex]
    commonContacts.value[fromIndex] = commonContacts.value[toIndex]
    commonContacts.value[toIndex] = temp
  }

  return {
    commonContacts,
    addCommonContact,
    updateCommonContact,
    removeCommonContact,
    moveCommonContact,
  }
})

export function isCommonGroupContact(dmrId: string): boolean {
  const contact = bfglob.gGroupCallContacts.getDataByIndex(dmrId)
  if (contact) {
    return true
  }
  return false
}

export function isCommonSingleContact(dmrId: string): boolean {
  const contact = bfglob.gSingleCallContacts.getDataByIndex(dmrId)
  if (contact) {
    return true
  }
  return false
}
